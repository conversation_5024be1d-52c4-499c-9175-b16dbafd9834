org.gradle.jvmargs=-Xmx6g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseParallelGC -XX:MaxMetaspaceSize=1g
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configuration-cache=true

android.enableAppCompileTimeRClass=true
android.enableBuildConfigAsBytecode=true
android.useAndroidX=true
# Improve build performance by enabling R8 parallelism.
android.r8.maxWorkers=4

kotlin.code.style=official

# Enable in Codespaces
# kotlin.compiler.execution.strategy=in-process
# kotlin.incremental=false
# Enable in Codespaces end
