[versions]
agp = "8.12.0"
aboutlibraries = "12.2.4"
androidX-lifecycle = "2.9.2"
androidX-room = "2.7.2"
firebase-bom = "34.1.0"
hiddenApiRefine = "4.4.0"
kotlin = "2.2.0"
protoc = "4.31.1"
shizuku = "13.1.5"
square-retrofit = "3.0.0"
zhaobozhen = "1.1.6"

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version = "3.0.6" }
gms = { id = "com.google.gms.google-services", version = "4.4.3" }
hiddenApiRefine = { id = "dev.rikka.tools.refine", version.ref = "hiddenApiRefine" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version = "2.2.0-2.0.2" }
protobuf = { id = "com.google.protobuf", version = "0.9.5" }
moshiX = "dev.zacsweers.moshix:0.31.0"
spotless = "com.diffplug.spotless:7.2.1"
aboutlibraries = { id = "com.mikepenz.aboutlibraries.plugin", version.ref = "aboutlibraries" }

[libraries]
gradlePlugin-android = { module = "com.android.tools.build:gradle", version.ref = "agp" }
gradlePlugin-kotlin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }

kotlinX-coroutines = "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.10.2"

androidX-activity = "androidx.activity:activity-ktx:1.10.1"
androidX-annotation = "androidx.annotation:annotation:1.9.1"
androidX-appCompat = "androidx.appcompat:appcompat:1.7.1"
androidX-browser = "androidx.browser:browser:1.9.0"
androidX-constraintLayout = "androidx.constraintlayout:constraintlayout:2.2.1"
androidX-core = "androidx.core:core-ktx:1.16.0"
androidX-fragment = "androidx.fragment:fragment-ktx:1.8.8"
androidX-lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "androidX-lifecycle" }
androidX-lifecycle-viewModel = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidX-lifecycle" }
androidX-preference = "androidx.preference:preference-ktx:1.2.1"
androidX-recyclerView = "androidx.recyclerview:recyclerview:1.4.0"
androidX-room-compiler = { module = "androidx.room:room-compiler", version.ref = "androidX-room" }
androidX-room-ktx = { module = "androidx.room:room-ktx", version.ref = "androidX-room" }
androidX-room-runtime = { module = "androidx.room:room-runtime", version.ref = "androidX-room" }
androidX-security = "androidx.security:security-crypto:1.1.0"
androidX-viewPager2 = "androidx.viewpager2:viewpager2:1.1.0"
androidX-window = "androidx.window:window:1.4.0"

square-leakCanary = "com.squareup.leakcanary:leakcanary-android:2.14"
square-moshi = "com.squareup.moshi:moshi:1.15.2"
square-okHttp = "com.squareup.okhttp3:okhttp:5.1.0"
square-okio = "com.squareup.okio:okio:3.16.0"
square-retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "square-retrofit" }
square-retrofit-moshi = { module = "com.squareup.retrofit2:converter-moshi", version.ref = "square-retrofit" }

android-apksig = { module = "com.android.tools.build:apksig", version.ref = "agp" }
google-dexlib2 = "com.android.tools.smali:smali-dexlib2:3.0.9"
google-material = "com.google.android.material:material:1.12.0"
google-protobuf-javaLite = { module = "com.google.protobuf:protobuf-javalite", version.ref = "protoc" }
google-protobuf-protoc = { module = "com.google.protobuf:protoc", version.ref = "protoc" }

rikka-refine-annotation = { module = "dev.rikka.tools.refine:annotation", version.ref = "hiddenApiRefine" }
rikka-refine-compiler = { module = "dev.rikka.tools.refine:annotation-processor", version.ref = "hiddenApiRefine" }
rikka-refine-runtime = { module = "dev.rikka.tools.refine:runtime", version.ref = "hiddenApiRefine" }
rikkax-appcompat = "dev.rikka.rikkax.appcompat:appcompat:1.6.1"
rikkax-borderview = "dev.rikka.rikkax.widget:borderview:1.1.0"
rikkax-core = "dev.rikka.rikkax.core:core:1.4.1"
rikkax-insets = "dev.rikka.rikkax.insets:insets:1.3.0"
rikkax-layoutinflater = "dev.rikka.rikkax.layoutinflater:layoutinflater:1.3.0"
rikkax-material = "dev.rikka.rikkax.material:material:2.7.2"
rikkax-material-preference = "dev.rikka.rikkax.material:material-preference:2.0.0"
rikkax-recyclerview = "dev.rikka.rikkax.recyclerview:recyclerview-ktx:1.3.2"
rikkax-simplemenu-preference = "dev.rikka.rikkax.preference:simplemenu-preference:1.0.3"

shizuku-api = { module = "dev.rikka.shizuku:api", version.ref = "shizuku" }
shizuku-provider = { module = "dev.rikka.shizuku:provider", version.ref = "shizuku" }

aboutlibraries-core = { module = "com.mikepenz:aboutlibraries-core", version.ref = "aboutlibraries" }
aboutlibraries-ui = { module = "com.mikepenz:aboutlibraries", version.ref = "aboutlibraries" }
appIconLoader = "me.zhanghai.android.appiconloader:appiconloader:1.5.0"
appIconLoader-coil = "me.zhanghai.android.appiconloader:appiconloader-coil:1.5.0"
brvah = "io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.14"
coil = "io.coil-kt:coil:2.7.0"
commons-compress = "org.apache.commons:commons-compress:1.27.1"
fastScroll = "me.zhanghai.android.fastscroll:library:1.3.0"
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebase-bom" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
flexbox = "com.google.android.flexbox:flexbox:3.0.0"
hiddenApiBypass = "org.lsposed.hiddenapibypass:hiddenapibypass:6.1"
lc-rules = "com.github.LibChecker:LibChecker-Rules-Bundle:41.6"
lottie = "com.airbnb.android:lottie:6.6.7"
mpAndroidChart = "com.github.AppDevNext:AndroidChart:3.1.0.30"
once = "com.jonathanfinerty.once:once:1.3.1"
processPhoenix = "com.jakewharton:process-phoenix:3.0.0"
timber = "com.jakewharton.timber:timber:5.0.1"
zhaobozhen-axml = { module = "com.github.zhaobozhen.libraries:axml", version.ref = "zhaobozhen" }
zhaobozhen-utils = { module = "com.github.zhaobozhen.libraries:utils", version.ref = "zhaobozhen" }
ktlint = "com.pinterest.ktlint:ktlint-cli:1.7.1"

[bundles]
androidX-lifecycle = [
  "androidX-lifecycle-viewModel",
  "androidX-lifecycle-service"
]
androidX-room = [
  "androidX-room-runtime",
  "androidX-room-ktx"
]
firebase = [
  "firebase-analytics",
  "firebase-crashlytics"
]
rikkax = [
  "rikkax-core",
  "rikkax-insets",
  "rikkax-material",
  "rikkax-borderview",
  "rikkax-appcompat",
  "rikkax-layoutinflater",
  "rikkax-recyclerview",
  "rikkax-simplemenu-preference",
  "rikkax-material-preference"
]
shizuku = [
  "shizuku-api",
  "shizuku-provider"
]
zhaobozhen = [
  "zhaobozhen-utils",
  "zhaobozhen-axml"
]
