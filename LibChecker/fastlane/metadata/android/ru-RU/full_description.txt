Это приложение используется для просмотра сторонних библиотек, используемых приложениями на вашем устройстве. Оно может просматривать архитектуру ABI нативной библиотеки приложения (в общем случае, является ли приложение 64-битным или 32-битным). Оно также может просматривать известные библиотеки, отмеченные в <a href="https://github.com/LibChecker/LibChecker-Rules">Репозитории правил</a>, и может даже сортировать и просматривать их по количеству ссылок на библиотеки.
