- Adapted for Android 16
- Supported analysis of APKs packages
- Supported detection of whether the app uses Compose Multiplatform technology
- Now supports viewing native library information for all architectures of an app
- Supports displaying 16 KB aligned apps in charts on Android 8+
- Native libraries with 16 KB alignment enabled, or uncompressed libraries that are not aligned to 16 KB in the ZIP package, will now be independently marked in the list on Android 8+. (When adapting an app to 16 KB, ensure all native libraries are labeled with ‘16 KB’ and that no native libraries are labeled with ‘NON 16 KB STORED’).
- Added an option for “Use IEC Units” for displaying file sizes in the app
- Added “Snapshot Auto-Remove” feature, now old snapshots can be automatically deleted
- Fixed the detection method for 16 KB alignment
- Fixed the display logic of the application component process indicator on the details page
- Updated to more modern Material Design page styles
- Supported recording basic information of “archived apps” on Android 15+
- Added reference links to feature detail popups
- Added installation time information to the installation source page
- Added supported store jump items to the “Further Options” section in the app details page
- Now no longer counts components starting with the Application ID in library reference statistics
- Fixed issues where snapshot backup and restoration could fail
- Native library information in the assets directory is no longer recorded in the snapshot
- Removed the “Dex” page from the app details page due to long-term lack of maintenance of Dex rules
- Migrated telemetry platform to Firebase
- Other fixes and optimizations
