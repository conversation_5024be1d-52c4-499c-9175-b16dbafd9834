<p>This is an application to view and analyze the third-party libraries used by apps on your device.</p><p><i>LibChecker</i> provides some basic functions, including App ABI architecture viewing and statistics (32-bit/64-bit), viewing native libraries, and viewing four major components (services, activities, broadcast receivers, content providers).</p><p><i>LibChecker</i> also provides some unique features:</p><ul><li>Well-known library tags: For some well-known third-party library components marked by <a href="https://github.com/LibChecker/LibChecker-Rules">The Rule Repository</a>, LibChecker will display tags, and you can view the detailed introduction.</li><li>Library reference statistics: count the frequency of use of each library</li><li>Package features: Analyze the apk package to determine whether the app is Split APK / whether it is written in Kotlin</li><li>Snapshot: Compare component differences before and after app upgrade</li></ul>