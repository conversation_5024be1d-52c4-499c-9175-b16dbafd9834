- 适配 Android 16
- 支持了分析 APKs 安装包
- 支持了检测应用是否使用 Compose Multiplatform 技术
- 现在支持查看应用所有架构下的原生库信息
- 在 Android 8 以上的设备上支持在图表中统计16 KB 对齐的应用
- 在 Android 8 以上的设备上，现在针对开启 16 KB 对齐的原生库，或未压缩但未在压缩包以 16 KB ZIP 对齐的原生库，会独立标记在列表项中（针对应用进行 16 KB 适配，应确保所有原生库都带有「16 KB」标签，并且没有任何原生库带有「NON 16 KB STORED」 标签）
- 新增了「使用 IEC 单位」的选项，针对应用内文件体积的展示生效
- 新增了「快照自动删除」功能，现在可以自动删除旧的快照
- 修正了 16 KB 对齐的检测方式
- 修正了详情页应用组件进程指示栏的显示逻辑
- 更新了更多现代的 Material Design 页面样式
- 在 Android 15 以上的设备上支持记录「已归档应用」的基础信息
- 为特性详情弹窗中加入了参考链接
- 在安装来源页面增加了安装时间信息
- 现在在应用详情页的「进一步操作」中增加了支持的商店跳转项
- 现在在库引用统计中不再统计以应用程序 ID 开头的组件
- 修复了快照备份和恢复可能失败的问题
- 该版本快照中不再记录 assets 目录内的原生库信息
- 由于长时间缺少维护 Dex 规则，现在已经移除应用详情页的「Dex」页
- 迁移遥测平台至 Firebase
- 其他修正和优化
