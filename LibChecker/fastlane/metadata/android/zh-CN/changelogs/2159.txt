- 适配 Android 15
- 支持了检测 16 KB 内存对齐的应用
- 支持了分析 XAPK 安装包
- 支持了展示应用是否为 APEX
- 支持了检测支持 MIPS 和 RISC-V 架构的应用
- 支持了库详情信息的多语言化，并增加了英语选项
- FOSS 版本支持了应用内检查更新
- 在应用列表的高级菜单中增加了显示 Compile SDK 的选项
- 现在在应用详情页面会展示 Split APKs 的完整体积
- 对于支持的应用，现在在 Kotlin 徽标的弹窗中会额外展示编译时的 Gradle 版本和 Java 兼容版本
- 更新了图表页面的样式，现在对于饼图，会额外以列表显示数据项，并更新了数据类型的切换按钮样式
- 在图表中增加了 Compile SDK 和 AAB 的统计类型，并针对 ABI 统计增加了详细模式
- 在应用详情页中长按「启动」时可以复制应用的启动器 Activity 信息到剪贴板
- 在 Android 13 以上启用系统级的单应用语言切换支持并隐藏应用设置页的选项
- 新增了一个调试功能，现在在应用列表的搜索框中输入`/dumpAppsInfoTxt` 或 `/dumpAppsInfoMd` 后，会将所有应用的基础信息保存为文件
- 更新了横屏布局时的 UI 布局
- 优化了一些 UI 表现
- 优化了检测 Jetpack Compose 的方法
- 优化了对预置应用的判断
- 修复了应用属性页面错乱的问题
- 修复了应用详情页面列表分割线的问题
- 修复了某些情况下备选的启动方式页面为空的问题
- 修复了 Android 12 以上无法使用 Activity 嵌入的问题
- 修复了 APK 分析时查看签名信息失败的问题

* Note: 由于 Microsoft App Center 即将停止服务，我们可能在后续版本将遥测平台迁移至 Firebase