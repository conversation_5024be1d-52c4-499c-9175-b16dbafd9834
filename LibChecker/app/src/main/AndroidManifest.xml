<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  tools:ignore="QueryAllPackagesPermission">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

  <permission
    android:name="${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
    android:protectionLevel="signature"
    tools:node="remove" />

  <uses-permission
    android:name="${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
    tools:node="remove" />

  <application
    android:name=".LibCheckerApp"
    android:appCategory="productivity"
    android:configChanges="orientation"
    android:dataExtractionRules="@xml/data_extraction_rules"
    android:enableOnBackInvokedCallback="@bool/appOnBackInvokedCallbackEnabled"
    android:fullBackupContent="@xml/backup_rules"
    android:hardwareAccelerated="true"
    android:icon="@mipmap/ic_launcher"
    android:label="@string/app_name"
    android:largeHeap="true"
    android:roundIcon="@mipmap/ic_launcher_round"
    android:supportsRtl="true"
    android:theme="@style/AppTheme"
    tools:targetApi="36">

    <activity
      android:name=".features.home.ui.MainActivity"
      android:configChanges="orientation"
      android:exported="true"
      android:launchMode="singleTop">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
        <category android:name="android.intent.category.DEFAULT" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.APPLICATION_PREFERENCES" />
      </intent-filter>

      <meta-data
        android:name="android.app.shortcuts"
        android:resource="@xml/shortcuts" />
    </activity>
    <activity
      android:name=".features.applist.detail.ui.AppDetailActivity"
      android:configChanges="orientation"
      android:exported="true"
      android:launchMode="singleTask">
      <intent-filter>
        <action android:name="android.intent.action.SHOW_APP_INFO" />

        <category android:name="android.intent.category.DEFAULT" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data
          android:scheme="market"
          android:host="details" />
      </intent-filter>
    </activity>
    <activity
      android:name=".features.applist.detail.ui.ApkDetailActivity"
      android:configChanges="orientation"
      android:exported="true"
      android:label="@string/detail_label"
      android:launchMode="singleTask">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:scheme="content" />
        <data android:mimeType="application/vnd.android.package-archive" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.SEND" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:mimeType="application/vnd.android.package-archive" />
      </intent-filter>
    </activity>
    <activity
      android:name=".features.snapshot.detail.ui.SnapshotDetailActivity"
      android:configChanges="orientation" />
    <activity
      android:name=".features.album.ui.AlbumActivity"
      android:configChanges="orientation"
      android:label="@string/title_album" />
    <activity
      android:name=".features.album.backup.ui.BackupActivity"
      android:exported="true"
      android:label="@string/album_item_backup_restore_title">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:scheme="content"
          tools:ignore="AppLinkUrlError" />
        <data android:pathPattern=".*\\.lcss" />
      </intent-filter>
    </activity>
    <activity
      android:name=".features.album.comparison.ui.ComparisonActivity"
      android:exported="true"
      android:label="@string/album_item_comparison_title">
      <intent-filter>
        <action android:name="android.intent.action.SEND_MULTIPLE" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:mimeType="application/vnd.android.package-archive" />
      </intent-filter>
    </activity>
    <activity
      android:name=".features.album.track.ui.TrackActivity"
      android:label="@string/album_item_track_title" />
    <activity
      android:name=".features.statistics.ui.LibReferenceActivity"
      android:configChanges="orientation"
      android:label="@string/tab_lib_reference_statistics"
      android:launchMode="singleTask" />
    <activity
      android:name=".features.chart.ui.ChartActivity"
      android:label="@string/tab_chart" />
    <activity
      android:name=".ui.bridge.BridgeActivity"
      android:exported="true"
      android:theme="@style/Transparent">

      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data
          android:host="bridge"
          android:scheme="lc" />
      </intent-filter>
    </activity>

    <service android:name=".services.ShootService"
      android:foregroundServiceType="shortService"
      android:exported="false" />
    <service android:name=".services.WorkerService" />

    <provider
      android:name="rikka.shizuku.ShizukuProvider"
      android:authorities="${applicationId}.shizuku"
      android:multiprocess="false"
      android:enabled="true"
      android:exported="true"
      android:permission="android.permission.INTERACT_ACROSS_USERS_FULL" />

    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${applicationId}.fileprovider"
      android:exported="false"
      android:grantUriPermissions="true">

      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/filepaths" />
    </provider>

    <meta-data
      android:name="channel"
      android:value="${channel}" />

    <property
      android:name="android.window.PROPERTY_ACTIVITY_EMBEDDING_SPLITS_ENABLED"
      android:value="true" />

  </application>

</manifest>
