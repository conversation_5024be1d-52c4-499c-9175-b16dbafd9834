syntax = "proto3";

package protocol;

option java_package = "com.absinthe.libchecker.protocol";
option java_outer_classname = "LibCheckerProtocol";
option java_multiple_files = true;

message SnapshotList {
  repeated Snapshot snapshots = 1;
}

message Snapshot {
  string packageName = 1;
  int64 timeStamp = 2;
  string label = 3;
  string versionName = 4;
  int64 versionCode = 5;
  int64 installedTime = 6;
  int64 lastUpdatedTime = 7;
  bool isSystem = 8;
  uint32 abi = 9;
  uint32 targetApi = 10;
  string nativeLibs = 11;
  string services = 12;
  string activities = 13;
  string receivers = 14;
  string providers = 15;
  string permissions = 16;
  string metadata = 17;
  int64 packageSize = 18;
  uint32 compileSdk = 19;
  uint32 minSdk = 20;
}

message CloudRulesBundle {
  int32 version = 1;
  int32 count = 2;
  CloudRulesList rulesList = 3;
}

message CloudRulesList {
  repeated CloudRule cloudRules = 1;
}

message CloudRule {
  string name = 1;
  string label = 2;
  uint32 type = 3;
  uint32 iconIndex = 4;
  bool isRegexRule = 5;
  string regexName = 6;
}
