<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">
  <item app:state_raised="true">
    <set>
      <objectAnimator
        android:duration="@android:integer/config_mediumAnimTime"
        android:propertyName="alpha"
        android:valueTo="0.9"
        android:valueType="floatType" />
    </set>
  </item>
  <item>
    <set>
      <objectAnimator
        android:duration="@android:integer/config_mediumAnimTime"
        android:propertyName="alpha"
        android:valueTo="1"
        android:valueType="floatType" />
    </set>
  </item>
</selector>
