<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Ứng dụng</string>
  <string name="title_statistics">H<PERSON> thống dữ liệu</string>
  <string name="title_snapshot">Ch<PERSON><PERSON> ảnh màn hình</string>
  <string name="title_settings">Thiết trí</string>
  <string name="title_album"><PERSON><PERSON><PERSON><PERSON> tập</string>
  <string name="loading"><PERSON><PERSON> tải…</string>
  <string name="channel_shoot">L<PERSON><PERSON> màn hình chụp nhanh</string>
  <string name="noti_shoot_title"><PERSON><PERSON> lưu ảnh chụp màn hình hiện tại</string>
  <string name="noti_shoot_title_saved">Ảnh chụp màn hình đã được lưu</string>
  <!--  App list  -->
  <string name="menu_search">Tìm kiếm</string>
  <string name="menu_sort">Sắ<PERSON> xếp</string>
  <string name="menu_filter">L<PERSON>c</string>
  <string name="search_hint">Tì<PERSON> kiếm…</string>
  <string name="string_64_bit">64bit</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs">Không có thư viện gốc</string>
  <string name="cannot_read">Unable to read</string>
  <string name="unknown">không xác định</string>
  <string name="empty_list">Danh sách trống</string>
  <string name="uncharted_territory">Vùng chưa được khám phá</string>
  <string name="get_app_list_denied_tip">Vui lòng cấp\n\"Nhận thông tin về các ứng dụng đã cài đặt\"\n Và cấp quyền cho LibChecker</string>
  <string name="advanced_menu">Cài đặt nâng cấp cao</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">Ứng dụng có %s ABI</string>
  <string name="title_statistics_dialog_no_native_libs">Ứng dụng không có thư viện gốc</string>
  <string name="tab_chart">Biểu đồ biểu tượng</string>
  <string name="tab_lib_reference_statistics">Tham khảo thư viện</string>
  <string name="not_marked_lib">Thư viện chưa được đánh dấu</string>
  <string name="submenu_title_component">Thành phần</string>
  <string name="submenu_title_manifest">kế khai</string>
  <string name="ref_category_all">Tất cả</string>
  <string name="ref_category_native">Thư viện gốc</string>
  <string name="ref_category_service">Dịch vụ</string>
  <string name="ref_category_activity">Hoạt động</string>
  <string name="ref_category_br">Máy thu phát sóng</string>
  <string name="ref_category_cp">Nhà cung cấp nội dung</string>
  <string name="ref_category_perm">Quyền</string>
  <string name="ref_category_static">Thư viện</string>
  <string name="ref_category_metadata">Metadata</string>
  <string name="ref_category_package">Gói</string>
  <string name="ref_category_shared_uid">UID dùng chung</string>
  <string name="ref_category_signatures">Chữ ký</string>
  <string name="ref_category_only_not_marked">Chỉ không được đánh dấu</string>
  <string name="string_kotlin_used">Kotlin đã sử dụng</string>
  <string name="string_kotlin_unused">Kotlin chưa sử dụng</string>
  <string name="string_compose_used">Jetpack Compose đã được sử dụng</string>
  <string name="string_compose_unused">Chưa sử dụng Jetpack Compose</string>
  <!--  About  -->
  <string name="settings_about">Giới thiệu</string>
  <string name="settings_about_summary">Đây là LibChecker!</string>
  <string name="settings_translate">Tham gia dịch</string>
  <string name="settings_translate_summary">Giúp mình với nhóm dịch ứng dụng này</string>
  <string name="settings_rate_us">Đánh giá chúng tôi</string>
  <string name="settings_rate_us_summary">Điều này có thể khiến chúng tôi được nhiều người tìm thấy hơn</string>
  <string name="about_info">Ứng dụng này được sử dụng để xem các thư viện của bên thứ ba được sử dụng bởi các ứng dụng trong thiết bị của bạn.</string>
  <string name="toolbar_rate">Tỷ lệ</string>
  <string name="resource_declaration">Một phần tài nguyên trong ứng dụng đến từ:</string>
  <string name="library_declaration">Tất cả thông tin thư viện được đánh dấu trong LibChecker đều đến từ kho lưu trữ tài liệu phát triển hoặc mã của SDK chứa thông tin đó. Nếu thông tin không chính xác, vui lòng liên hệ: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Bình thường</string>
  <string name="pref_group_others">Người khác</string>
  <string name="apk_analytics">Phân tích APK</string>
  <string name="colorful_icon">Biểu tượng đầy màu sắc</string>
  <string name="rules_repo_title">Quy tắc repo</string>
  <string name="lib_ref_threshold">Ngưỡng tham khảo thư viện</string>
  <string name="languages">Ngữ Ngôn</string>
  <string name="reload_apps">Tải lại danh sách ứng dụng</string>
  <string name="help_docs">Tài liệu trợ giúp</string>
  <string name="join_telegram_group">Giao tiếp</string>
  <string name="anonymous_statistics">Thống kê ẩn danh</string>
  <string name="cloud_rules">Quy tắc đám mây</string>
  <string name="dark_mode">Chế độ tối</string>
  <string name="snapshot_keep">Ảnh chụp nhanh Mặc định giữ nguyên tắc</string>
  <string name="apk_analytics_summary">Mở trang chi tiết khi nhấp vào tệp APK</string>
  <string name="colorful_icon_summary">Một phần của các thư viện được đánh dấu sẽ xuất hiện dưới dạng logo màu</string>
  <string name="lib_ref_threshold_summary">Chỉ những thư viện có số lượng tài liệu tham khảo đạt ngưỡng mới được hiển thị trong danh sách</string>
  <string name="reload_apps_summary">Hãy thử khi danh sách ứng dụng được hiển thị bất thường</string>
  <string name="help_docs_summary">Tìm hiểu cách sử dụng LibChecker</string>
  <string name="join_telegram_group_summary">Tham gia nhóm Telegram của chúng tôi</string>
  <string name="anonymous_statistics_summary">Chúng tôi sử dụng Google Firebase để gửi ẩn danh các thư viện được đánh dấu được sử dụng phổ biến nhất và một số dữ liệu về thói quen sử dụng để làm cho LibChecker trở nên thiết thực hơn</string>
  <string name="cloud_rules_summary">Cập nhật nội quy thư viện được đánh dấu mới nhất tại đây</string>
  <string name="array_dark_mode_off">TẮT</string>
  <string name="array_dark_mode_on">MỞ</string>
  <string name="array_dark_mode_auto">Tự động</string>
  <string name="array_dark_mode_system">Mặc định hệ thống</string>
  <string name="array_dark_mode_battery">Đặt theo Trình tiết kiệm pin</string>
  <string name="array_snapshot_default">Thông báo</string>
  <string name="array_snapshot_keep">Giữ</string>
  <string name="array_snapshot_discard">loại bỏ</string>
  <!--  Detail  -->
  <string name="detail">Chi tiết</string>
  <string name="detail_label">Xem chi tiết ứng dụng</string>
  <string name="not_found">Không tìm thấy</string>
  <string name="create_an_issue">Hỗ trợ chúng tôi bổ sung thông tin</string>
  <string name="app_bundle_details">Android App Bundle là định dạng xuất bản chính thức, mới của Android, cung cấp một cách hiệu quả hơn để xây dựng và phát hành ứng dụng của bạn. Android App Bundle cho phép bạn dễ dàng cung cấp trải nghiệm tuyệt vời hơn trong một ứng dụng có kích thước nhỏ hơn, điều này có thể cải thiện khả năng cài đặt thành công và giảm số lượt gỡ cài đặt. Thật dễ dàng để chuyển đổi. Bạn không cần cấu trúc lại mã của mình để bắt đầu hưởng lợi từ một ứng dụng nhỏ hơn. Và khi bạn đã chuyển đổi, bạn sẽ được hưởng lợi từ việc phát triển ứng dụng theo mô-đun và cung cấp tính năng có thể tùy chỉnh.</string>
  <string name="kotlin_details">Kotlin là một ngôn ngữ lập trình đa nền tảng, được gõ tĩnh, có mục đích chung với suy luận kiểu. Kotlin được thiết kế để tương tác hoàn toàn với Java và phiên bản JVM của thư viện chuẩn của Kotlin phụ thuộc vào Thư viện lớp Java, nhưng suy luận kiểu cho phép cú pháp của nó ngắn gọn hơn.</string>
  <string name="items_count">Số lượng vật phẩm:</string>
  <string name="further_operation">hoạt động thêm</string>
  <string name="app_info_launch">Phóng</string>
  <string name="app_info_settings">Cài đặt</string>
  <string name="lib_detail_dialog_title">Chi tiết thư viện</string>
  <string name="agp_details">Hệ thống xây dựng Android Studio dựa trên Gradle và plugin Android Gradle bổ sung một số tính năng dành riêng cho việc xây dựng ứng dụng Android. Mặc dù plugin Android thường được cập nhật theo bước khóa với Android Studio, nhưng plugin này (và phần còn lại của hệ thống Gradle) có thể chạy độc lập với Android Studio và được cập nhật riêng.</string>
  <string name="xposed_module">Mô-đun Xposed</string>
  <string name="xposed_module_details">Xposed là một khung dành cho các mô-đun có thể thay đổi hành vi của hệ thống và ứng dụng mà không cần chạm vào bất kỳ APK nào. Điều đó thật tuyệt vì điều đó có nghĩa là các mô-đun có thể hoạt động cho các phiên bản khác nhau và thậm chí cả ROM mà không có bất kỳ thay đổi nào.</string>
  <string name="play_app_signing">Chữ ký Play app</string>
  <string name="play_app_signing_details">Với Tính năng ký ứng dụng của Play, Google quản lý và bảo vệ khóa ký ứng dụng cho bạn, đồng thời sử dụng từ khóa đó để ký các APK phân phối, được tối ưu hóa được tạo từ các gói ứng dụng của bạn. Play App Signing bạn lưu trữ.</string>
  <string name="pwa_details">Ứng dụng web triển khai ứng dụng (PWA) được xây dựng và cải tiến với các API hiện đại để cung cấp khả năng, độ tin cậy và khả năng cài đặt nâng cao trong khi tiếp cận bất kỳ ai, ở bất kỳ đâu, trên bất kỳ any device only by a codebase.</string>
  <string name="jetpack_compose_details">Jetpack Compose là bộ công cụ hiện đại của Android để xây dựng giao diện người dùng gốc. Nó đơn giản hóa và tăng tốc phát triển giao diện người dùng trên Android. Nhanh chóng đưa ứng dụng của bạn vào cuộc sống với ít mã hơn, công cụ mạnh mẽ và API Kotlin trực quan.</string>
  <string name="extract_native_libs_tip">Ứng dụng tuyên bố </string>
  <string name="xml_detail">Chi tiết XML</string>
  <string name="format_last_updated">Đã cập nhật: %s</string>
  <string name="menu_process">Quá trình</string>
  <string name="menu_split">Tách</string>
  <string name="alternative_launch_method">Phương pháp chia sẻ thay thế</string>
  <string name="compare_with_current">So sánh với gói hiện tại</string>
  <string name="rx_detail">RxJava là một sự phát triển khai thác máy ảo Java của Tiện ích mở rộng phản ứng: một thư viện để tạo các chương trình không đồng bộ và dựa trên sự kiện bằng cách sử dụng các chuỗi có thể quan sát được.</string>
  <string name="rx_android_detail">Tiện ích mở rộng Reactive cho Android</string>
  <string name="rx_kotlin_detail">Tiện ích mở rộng của Kotlin cho RxJava</string>
  <string name="permission_not_granted">Chưa cấp quyền</string>
  <string name="signature_detail">Chi tiết chữ ký</string>
  <string name="signature_version">Phiên bản</string>
  <string name="signature_serial_number">Số sê-ri</string>
  <string name="signature_issuer">Bên phát hành</string>
  <string name="signature_subject">Tiêu đề</string>
  <string name="signature_validity_not_before">Có hiệu lực từ</string>
  <string name="signature_validity_not_after">Có hiệu lực từ</string>
  <string name="signature_public_key_format">Định dạng Key</string>
  <string name="signature_public_key_algorithm">Công khai thuật toán</string>
  <string name="signature_public_key_exponent">Mã Key</string>
  <string name="signature_public_key_modulus_size">Kích thước module Key</string>
  <string name="signature_public_key_modulus">Khai báo module Key công khai</string>
  <string name="signature_public_key_y">Key công khai Giá trị Y</string>
  <string name="signature_public_key_type">Loại Key công khai</string>
  <string name="signature_algorithm_name">Chữ ký thuật toán</string>
  <string name="signature_algorithm_oid">Chữ ký thuật toán</string>
  <string name="lib_detail_label_tip">Tiêu đề</string>
  <string name="lib_detail_develop_team_tip">Đội chơi đồ</string>
  <string name="lib_detail_rule_contributors_tip">Người đóng góp quy tắc</string>
  <string name="lib_detail_description_tip">Miêu tả</string>
  <string name="lib_detail_relative_link_tip">Liên kết tương đối</string>
  <string name="lib_detail_last_update_tip">Cập nhật tại</string>
  <string name="lib_detail_app_props_title">Thuộc tính ứng dụng</string>
  <string name="lib_detail_app_props_tip">Thêm thông tin</string>
  <string name="lib_detail_xposed_min_version">Phiên bản tối thiểu</string>
  <string name="lib_detail_xposed_default_scope">Phạm vi mặc định</string>
  <string name="lib_detail_xposed_init_class">Lớp khởi tạo</string>
  <string name="lib_detail_app_install_source_title">Nguồn cài đặt</string>
  <string name="lib_detail_app_install_source_originating_package">người yêu cầu cài đặt</string>
  <string name="lib_detail_app_install_source_installing_package">Người thực hiện cài đặt</string>
  <string name="lib_detail_app_install_source_empty">không xác định</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell hoặc ứng dụng đã gỡ cài đặt</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Vì Android hạn chế API nhận trình yêu cầu cài đặt nên chúng tôi sử dụng Shizuku để nhận trình yêu cầu cài đặt.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku chưa được cài đặt</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">Nhấn vào đây để cài đặt Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Yêu cầu Shizuku API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">Nhấn vào đây để cập nhật Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku không chạy</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">Nhấn vào đây để chuyển đến Shizuku để bắt đầu nó</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku không được phép</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">Nhấp vào đây để Shizuku ủy quyền cho người yêu cầu cài đặt</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Sao chép vào clipboard</string>
  <string name="toast_not_existing_market">Không tồn tại bất kỳ thị trường ứng dụng nào</string>
  <string name="toast_use_another_file_manager">Vui lòng sử dụng Trình quản lý tệp khác để mở APK</string>
  <string name="toast_cant_open_app">Không thể mở ứng dụng này</string>
  <string name="toast_cloud_rules_update_error">Không thể cập nhật quy tắc đám mây. Vui lòng thử lại.</string>
  <string name="toast_not_enough_storage_space">Không đủ dung lượng lưu trữ</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Lưu ảnh chụp nhanh hiện tại</string>
  <string name="snapshot_current_timestamp">Dấu thời gian của ảnh chụp nhanh hiện tại</string>
  <string name="snapshot_apps_count">Số lượng ứng dụng trong Ảnh chụp nhanh / Số lượng ứng dụng</string>
  <string name="comparison_snapshot_apps_count">Số lượng ứng dụng trong Ảnh chụp nhanh</string>
  <string name="snapshot_indicator_added">Thêm</string>
  <string name="snapshot_indicator_removed">LOẠI BỎ</string>
  <string name="snapshot_indicator_changed">đã thay đổi</string>
  <string name="snapshot_indicator_moved">Đã chuyển</string>
  <string name="snapshot_empty_list_title">Không có thay đổi thành phần</string>
  <string name="snapshot_no_snapshot">Không có ảnh chụp nhanh</string>
  <string name="snapshot_detail_new_install_title">Ứng dụng này đã được cài đặt mới</string>
  <string name="snapshot_detail_deleted_title">Ứng dụng này đã bị xóa</string>
  <string name="snapshot_time_node_uninitialized">(Chưa khởi tạo)</string>
  <string name="snapshot_preinstalled_app">ứng dụng được cài đặt sẵn</string>
  <string name="snapshot_generate_text_report">Tạo báo cáo văn bản</string>
  <string name="snapshot_scheme_tip"><![CDATA[Tip: Giờ đây, bạn có thể tạo ảnh chụp nhanh ở chế độ nền thông qua:<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Bạn có chắc chắn tải lại tất cả ứng dụng không?</string>
  <string name="dialog_subtitle_reload_apps">Điều này có thể mất một vài giây</string>
  <string name="dialog_title_change_timestamp">Thay đổi nút thời gian</string>
  <string name="dialog_title_keep_previous_snapshot">Chú ý</string>
  <string name="dialog_message_keep_previous_snapshot">Bạn có muốn giữ ảnh chụp nhanh trước đó không?</string>
  <string name="dialog_title_select_to_delete">Chọn một ngày để xóa ảnh chụp nhanh</string>
  <string name="dialog_title_confirm_to_delete">Bạn có chắc chắn xóa ảnh chụp nhanh này không?</string>
  <string name="btn_keep">Giữ</string>
  <string name="btn_drop">loại bỏ</string>
  <string name="dialog_title_compare_diff_apk">Chú ý</string>
  <string name="dialog_message_compare_diff_apk">Bạn có muốn so sánh hai APK khác nhau không?</string>
  <!--  Album  -->
  <string name="album_compare">So sánh</string>
  <string name="album_item_comparison_title">So sánh</string>
  <string name="album_item_comparison_subtitle">So sánh với hai ảnh chụp nhanh</string>
  <string name="album_item_comparison_invalid_compare">So sánh không hợp lệ</string>
  <string name="album_item_comparison_invalid_shared_items">Vui lòng chọn hai tệp APK để so sánh</string>
  <string name="album_item_comparison_choose_local_apk">Chọn APK cục bộ</string>
  <string name="album_item_management_title">Quản lý</string>
  <string name="album_item_management_subtitle">Quản lý tất cả ảnh chụp nhanh</string>
  <string name="album_item_backup_restore_title">Sao lưu &amp; Khôi phục</string>
  <string name="album_item_backup_restore_subtitle">Sao lưu và khôi phục ảnh chụp nhanh</string>
  <string name="album_item_track_title">Theo dõi</string>
  <string name="album_item_track_subtitle">Chọn ứng dụng để buộc thay đổi so sánh</string>
  <string name="album_backup">Hỗ trợ</string>
  <string name="album_backup_summary">Sao lưu tất cả ảnh chụp nhanh</string>
  <string name="album_restore">Khôi phục</string>
  <string name="album_restore_summary">Khôi phục ảnh chụp nhanh từ tệp sao lưu</string>
  <string name="album_restore_detail">%1$s : tổng cộng %2$s mục\n</string>
  <string name="album_click_to_choose">Bấm để chọn</string>
  <string name="album_dialog_delete_snapshot_message">Processing…</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Chặn nó qua \"MonkeyKing Purify\"</string>
  <string name="integration_monkey_king_menu_unblock">Bỏ chặn nó qua \"MonkeyKing Purify\"</string>
  <string name="integration_blocker_menu_block">Chặn nó qua \"Blocker\"</string>
  <string name="integration_blocker_menu_unblock">Bỏ chặn nó qua \"Trình chặn\"</string>
  <string name="integration_anywhere_menu_editor">Mở nó trong \"Anywhere- Editor\"</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Phiên bản repo cục bộ</string>
  <string name="rules_remote_repo_version">Phiên bản repo từ xa</string>
  <string name="rules_btn_restart_to_update">Khởi động lại để cập nhật</string>
  <string name="rules_btn_update">Cập nhật</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">Hiển thị ứng dụng hệ thống</string>
  <string name="adv_show_overlays">Hiển thị lớp phủ</string>
  <string name="adv_show_64_bit">Hiển thị ứng dụng 64-bit</string>
  <string name="adv_show_32_bit">Hiển thị ứng dụng 32-bit</string>
  <string name="adv_sort_mode">Chế độ sắp xếp</string>
  <string name="adv_sort_by_time">Thời gian cập nhật</string>
  <string name="adv_sort_by_target_version">Phiên bản mục tiêu</string>
  <string name="adv_sort_by_name">Tên</string>
  <string name="adv_show_android_version">Hiển thị phiên bản Android</string>
  <string name="adv_show_target_version">Hiển thị phiên bản mục tiêu</string>
  <string name="adv_show_min_version">Hiển thị phiên bản tối thiểu</string>
  <string name="adv_tint_abi_label">Tô màu nhãn ABI</string>
  <string name="adv_mark_exported">Đánh dấu thành phần đã xuất</string>
  <string name="adv_mark_disabled">Đánh dấu thành phần bị vô hiệu hóa</string>
  <string name="adv_show_marked_lib">Hiển thị thư viện được đánh dấu</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Hiển thị thời gian cập nhật</string>
</resources>
