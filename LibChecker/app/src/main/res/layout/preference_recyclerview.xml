<?xml version="1.0" encoding="utf-8"?>
<rikka.widget.borderview.BorderRecyclerView xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:id="@android:id/list"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:clipToPadding="false"
  android:fadeScrollbars="true"
  android:overScrollMode="never"
  android:scrollbarStyle="outsideOverlay"
  android:scrollbars="vertical"
  app:borderBottomVisibility="never"
  app:borderTopDrawable="@null"
  app:borderTopVisibility="whenTop"
  app:fitsSystemWindowsInsets="bottom"
  tools:ignore="UnusedResources"
  tools:viewBindingIgnore="true" />
