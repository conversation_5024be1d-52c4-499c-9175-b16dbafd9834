<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:fitsSystemWindows="false"
  app:consumeSystemWindowsInsets="start|end"
  app:edgeToEdge="true"
  app:fitsSystemWindowsInsets="start|end"
  tools:context=".features.statistics.ui.LibReferenceActivity">

  <com.google.android.material.appbar.AppBarLayout
    android:id="@+id/appbar"
    style="?appBarStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.appcompat.widget.ToolbarFix
      android:id="@+id/toolbar"
      style="?actionBarStyle"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="@null" />

  </com.google.android.material.appbar.AppBarLayout>

  <ViewFlipper
    android:id="@+id/vf_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@null"
    android:clipChildren="true">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:background="@null"
      android:gravity="center">

      <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottie"
        android:layout_width="@dimen/lottie_anim_size"
        android:layout_height="@dimen/lottie_anim_size"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_enableMergePathsForKitKatAndAbove="true" />

    </LinearLayout>

    <rikka.widget.borderview.BorderRecyclerView
      android:id="@android:id/list"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:clipToPadding="false"
      android:paddingTop="?actionBarSize"
      android:scrollbars="none"
      app:borderBottomVisibility="never"
      app:borderTopDrawable="@null"
      app:borderTopVisibility="whenTop"
      app:fitsSystemWindowsInsets="bottom|top"
      app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

  </ViewFlipper>

</androidx.constraintlayout.widget.ConstraintLayout>
