<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  xmlns:tools="http://schemas.android.com/tools"
  tools:context=".features.snapshot.ui.SnapshotFragment">

  <com.absinthe.libchecker.view.app.CustomViewFlipper
    android:id="@+id/vf_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:gravity="center"
      android:orientation="vertical"
      android:paddingTop="?actionBarSize">

      <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/loading"
        android:layout_width="@dimen/lottie_anim_size"
        android:layout_height="@dimen/lottie_anim_size"
        app:lottie_autoPlay="true"
        app:lottie_fileName="anim/holographic_radar.json.zip"
        app:lottie_loop="true" />

      <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progress_indicator"
        style="@style/GeneralProgressIndicator"
        android:layout_width="200dp"
        android:layout_height="wrap_content" />

    </LinearLayout>

    <rikka.widget.borderview.BorderRecyclerView
      android:id="@android:id/list"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:clipToPadding="false"
      android:scrollbars="none"
      app:borderBottomVisibility="never"
      app:borderTopDrawable="@null"
      app:borderTopVisibility="whenTop"
      app:fitsSystemWindowsInsets="bottom" />

  </com.absinthe.libchecker.view.app.CustomViewFlipper>

</androidx.constraintlayout.widget.ConstraintLayout>
