<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:animateLayoutChanges="true"
  android:orientation="vertical"
  android:paddingTop="?actionBarSize"
  app:fitsSystemWindowsInsets="bottom|top">

  <com.google.android.material.progressindicator.LinearProgressIndicator
    android:id="@+id/progress_horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:max="100"
    android:visibility="gone"
    app:hideAnimationBehavior="outward" />

  <LinearLayout
    android:id="@+id/dashboard_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" />

  <com.google.android.flexbox.FlexboxLayout
    android:id="@+id/features_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:overScrollMode="never"
    android:padding="4dp"
    android:scrollbars="none"
    app:flexDirection="row"
    app:flexWrap="wrap"
    app:justifyContent="flex_start" />

</LinearLayout>
