<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  tools:context=".features.statistics.ui.LibReferenceFragment">

  <com.absinthe.libchecker.view.app.CustomViewFlipper
    android:id="@+id/vf_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.absinthe.libchecker.features.statistics.ui.view.LibReferenceLoadingView
      android:id="@+id/loading_view"
      android:layout_width="match_parent"
      android:layout_height="match_parent" />

    <rikka.widget.borderview.BorderRecyclerView
      android:id="@android:id/list"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:clipToPadding="false"
      android:scrollbars="none"
      app:borderBottomVisibility="never"
      app:borderTopDrawable="@null"
      app:borderTopVisibility="whenTop"
      app:fitsSystemWindowsInsets="bottom" />

  </com.absinthe.libchecker.view.app.CustomViewFlipper>

</androidx.constraintlayout.widget.ConstraintLayout>
