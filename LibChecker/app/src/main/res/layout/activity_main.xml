<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:fitsSystemWindows="false"
  app:consumeSystemWindowsInsets="start|end"
  app:edgeToEdge="true"
  app:fitsSystemWindowsInsets="start|end">

  <androidx.coordinatorlayout.widget.CoordinatorLayout
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <com.google.android.material.appbar.AppBarLayout
      android:id="@+id/appbar"
      style="?appBarStyle"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_gravity="top"
      android:fitsSystemWindows="true"
      app:liftOnScroll="true">

      <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        style="?actionBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null" />

    </com.google.android.material.appbar.AppBarLayout>

    <com.google.android.material.progressindicator.LinearProgressIndicator
      android:id="@+id/progress_horizontal"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="?actionBarSize"
      android:indeterminate="true"
      android:visibility="gone"
      app:hideAnimationBehavior="outward"
      app:layout_fitsSystemWindowsInsets="top" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
      android:id="@+id/nav_view"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="?android:attr/windowBackground"
      app:layout_anchor="@id/viewpager"
      app:layout_anchorGravity="bottom"
      app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
      app:menu="@menu/bottom_nav_menu" />

    <androidx.viewpager2.widget.ViewPager2
      android:id="@+id/viewpager"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:orientation="horizontal"
      app:layout_behavior="@string/appbar_scrolling_view_behavior" />

  </androidx.coordinatorlayout.widget.CoordinatorLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
