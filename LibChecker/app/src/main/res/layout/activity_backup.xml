<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/container"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:fitsSystemWindows="false"
  app:consumeSystemWindowsInsets="start|end"
  app:edgeToEdge="true"
  app:fitsSystemWindowsInsets="start|end">

  <com.google.android.material.appbar.AppBarLayout
    android:id="@+id/appbar"
    style="?appBarStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    app:layout_constraintTop_toTopOf="parent">

    <androidx.appcompat.widget.ToolbarFix
      android:id="@+id/toolbar"
      style="?actionBarStyle"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="@null" />

  </com.google.android.material.appbar.AppBarLayout>

  <androidx.fragment.app.FragmentContainerView
    android:id="@+id/fragment_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
