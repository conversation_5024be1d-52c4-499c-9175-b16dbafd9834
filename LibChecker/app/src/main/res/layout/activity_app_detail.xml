<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:fitsSystemWindows="false"
  app:consumeSystemWindowsInsets="start|end"
  app:edgeToEdge="true"
  app:fitsSystemWindowsInsets="start|end|top"
  tools:context=".features.applist.detail.ui.AppDetailActivity">

  <com.google.android.material.appbar.AppBarLayout
    android:id="@+id/header_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.CollapsingToolbarLayout
      android:id="@+id/collapsing_toolbar"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="@null"
      app:contentScrim="?android:colorBackground"
      app:expandedTitleTextAppearance="?attr/textAppearanceHeadlineSmall"
      app:layout_scrollFlags="scroll|exitUntilCollapsed">

      <LinearLayout
        android:id="@+id/header_content_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/normal_padding"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/normal_padding"
        android:paddingTop="?attr/actionBarSize"
        app:layout_collapseMode="parallax">

        <com.absinthe.libchecker.features.applist.detail.ui.view.DetailsTitleView
          android:id="@+id/details_title"
          android:layout_width="match_parent"
          android:layout_height="wrap_content" />

      </LinearLayout>

      <androidx.appcompat.widget.ToolbarFix
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_collapseMode="pin" />

    </com.google.android.material.appbar.CollapsingToolbarLayout>

  </com.google.android.material.appbar.AppBarLayout>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

    <com.google.android.material.tabs.TabLayout
      android:id="@+id/tab_layout"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="?android:colorBackground"
      app:tabGravity="start"
      app:tabMode="scrollable">

    </com.google.android.material.tabs.TabLayout>

    <androidx.cardview.widget.CardView
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:backgroundTint="?android:colorBackground"
      app:layout_constraintTop_toTopOf="parent">

      <LinearLayout
        android:id="@+id/detail_toolbar_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:animateLayoutChanges="true">

        <androidx.constraintlayout.widget.ConstraintLayout
          android:layout_width="match_parent"
          android:layout_height="@dimen/detail_toolbar_size"
          android:background="@null"
          android:animateLayoutChanges="true">

          <TextView
            android:id="@+id/tv_items"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/normal_padding"
            android:gravity="center"
            android:text="@string/items_count"
            android:textColor="?attr/colorControlNormal"
            app:layout_constraintStart_toStartOf="parent" />

          <com.absinthe.libchecker.features.applist.detail.ui.view.TextSwitcherView
            android:id="@+id/ts_component_count"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:padding="8dp"
            app:layout_constraintStart_toEndOf="@id/tv_items" />

          <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_toolbar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintStart_toEndOf="@id/ts_component_count" />

        </androidx.constraintlayout.widget.ConstraintLayout>

      </LinearLayout>

    </androidx.cardview.widget.CardView>

    <androidx.viewpager2.widget.ViewPager2
      android:id="@+id/viewpager"
      android:layout_width="match_parent"
      android:layout_height="match_parent" />

  </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
