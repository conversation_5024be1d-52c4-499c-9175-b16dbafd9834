<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:fitsSystemWindows="false"
  app:consumeSystemWindowsInsets="start|end"
  app:edgeToEdge="true"
  app:fitsSystemWindowsInsets="start|end|top"
  tools:context=".features.snapshot.detail.ui.SnapshotDetailActivity">

  <com.google.android.material.appbar.AppBarLayout
    android:id="@+id/header_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:colorBackground">

    <com.google.android.material.appbar.CollapsingToolbarLayout
      android:id="@+id/collapsing_toolbar"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:background="@null"
      app:contentScrim="?android:colorBackground"
      app:expandedTitleTextAppearance="?attr/textAppearanceHeadlineSmall"
      app:layout_scrollFlags="scroll|exitUntilCollapsed">

      <com.absinthe.libchecker.features.snapshot.detail.ui.view.SnapshotTitleView
        android:id="@+id/snapshot_title"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="@dimen/normal_padding"
        android:paddingTop="?attr/actionBarSize"
        android:paddingBottom="@dimen/normal_padding"
        app:layout_collapseMode="parallax" />

      <androidx.appcompat.widget.ToolbarFix
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_collapseMode="pin" />

    </com.google.android.material.appbar.CollapsingToolbarLayout>

  </com.google.android.material.appbar.AppBarLayout>

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

    <rikka.widget.borderview.BorderRecyclerView
      android:id="@android:id/list"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:clipToPadding="false"
      android:overScrollMode="never"
      android:padding="@dimen/normal_padding"
      app:fitsSystemWindowsInsets="bottom"
      app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

  </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
