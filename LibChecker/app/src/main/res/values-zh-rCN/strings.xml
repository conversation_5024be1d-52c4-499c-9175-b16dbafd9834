<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">应用</string>
  <string name="title_statistics">统计</string>
  <string name="title_snapshot">快照</string>
  <string name="title_settings">设置</string>
  <string name="title_album">相簿</string>
  <string name="loading">加载中…</string>
  <string name="channel_shoot">保存快照</string>
  <string name="noti_shoot_title">正在保存当前快照</string>
  <string name="noti_shoot_title_saved">快照已保存</string>
  <!--  App list  -->
  <string name="menu_search">搜索</string>
  <string name="menu_sort">排序</string>
  <string name="menu_filter">过滤</string>
  <string name="search_hint">搜索…</string>
  <string name="string_64_bit">64 位</string>
  <string name="string_32_bit">32 位</string>
  <string name="no_libs">无原生库</string>
  <string name="cannot_read">无法读取</string>
  <string name="unknown">未知</string>
  <string name="empty_list">空列表</string>
  <string name="uncharted_territory">未知领域</string>
  <string name="get_app_list_denied_tip">请授予 LibChecker\n“读取应用列表”\n权限</string>
  <string name="advanced_menu">高级菜单</string>
  <string name="archived_app">已归档应用</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">%s架构的应用</string>
  <string name="title_statistics_dialog_no_native_libs">无原生库的应用</string>
  <string name="tab_chart">图表</string>
  <string name="tab_lib_reference_statistics">库引用统计</string>
  <string name="not_marked_lib">未标记的库</string>
  <string name="submenu_title_component">组件</string>
  <string name="submenu_title_manifest">清单</string>
  <string name="ref_category_all">全部</string>
  <string name="ref_category_native">原生库</string>
  <string name="ref_category_service">服务</string>
  <string name="ref_category_activity">活动</string>
  <string name="ref_category_br">广播接收器</string>
  <string name="ref_category_cp">内容提供器</string>
  <string name="ref_category_perm">权限</string>
  <string name="ref_category_static">静态库</string>
  <string name="ref_category_metadata">元数据</string>
  <string name="ref_category_package">包</string>
  <string name="ref_category_shared_uid">共享的 UID</string>
  <string name="ref_category_signatures">签名</string>
  <string name="ref_category_only_not_marked">只展示未标记库</string>
  <string name="string_kotlin_used">使用了 Kotlin</string>
  <string name="string_kotlin_unused">未使用 Kotlin</string>
  <string name="string_compose_used">使用了 Jetpack Compose</string>
  <string name="string_compose_unused">未使用 Jetpack Compose</string>
  <string name="android_dist_label">分布</string>
  <string name="android_dist_source">来源</string>
  <string name="android_dist_title">Android 版本分布统计</string>
  <string name="android_dist_subtitle_format">更新时间： %s</string>
  <string name="chart_abi_detailed">详细</string>
  <string name="chart_abi_concise">简洁</string>
  <string name="chart_item_not_support">不支持</string>
  <!--  About  -->
  <string name="settings_about">关于</string>
  <string name="settings_about_summary">这里是 LibChecker!</string>
  <string name="settings_translate">参与翻译</string>
  <string name="settings_translate_summary">帮助我们翻译此应用</string>
  <string name="settings_rate_us">给我们评分</string>
  <string name="settings_rate_us_summary">这可以使我们被更多的人发现</string>
  <string name="settings_get_updates">获取更新</string>
  <string name="settings_get_updates_summary">获取更加稳定和功能众多的最新版本</string>
  <string name="settings_get_updates_in_app">应用内</string>
  <string name="settings_get_updates_in_app_chip_stable">稳定版</string>
  <string name="settings_get_updates_in_app_chip_ci">持续集成</string>
  <string name="about_info">本应用用来查看设备上的应用使用到的第三方库的情况。</string>
  <string name="toolbar_rate">评分</string>
  <string name="resource_declaration">应用中的部分素材来自：</string>
  <string name="library_declaration">LibChecker 中的所有标记库信息均来自其所属的 SDK 的开发文档或代码仓库，如果信息有有误，请联系：<EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">常规</string>
  <string name="pref_group_others">其它</string>
  <string name="apk_analytics">APK 分析</string>
  <string name="colorful_icon">多彩图标</string>
  <string name="rules_repo_title">规则仓库</string>
  <string name="lib_ref_threshold">库引用阈值</string>
  <string name="languages">语言</string>
  <string name="reload_apps">重载应用列表</string>
  <string name="help_docs">帮助文档</string>
  <string name="join_telegram_group">划水</string>
  <string name="anonymous_statistics">匿名统计</string>
  <string name="cloud_rules">云端规则</string>
  <string name="dark_mode">深色模式</string>
  <string name="snapshot_keep">快照默认保留规则</string>
  <string name="export_log">导出日志</string>
  <string name="apk_analytics_summary">当点击一个 APK 文件时打开详情页</string>
  <string name="colorful_icon_summary">部分标记库会呈现为彩色 Logo</string>
  <string name="lib_ref_threshold_summary">只有库引用数量达到阈值的库才显示在列表中</string>
  <string name="reload_apps_summary">当应用列表显示不正常时尝试此选项</string>
  <string name="export_log_summary">导出应用的日志文件</string>
  <string name="help_docs_summary">学习如何使用 LibChecker</string>
  <string name="join_telegram_group_summary">来一起划水</string>
  <string name="anonymous_statistics_summary">我们使用 Google Firebase 来匿名发送最常被人们使用的标记库和一些使用习惯数据来使得 LibChecker 变得更加实用</string>
  <string name="cloud_rules_summary">在此更新最新的标记库</string>
  <string name="array_dark_mode_off">关闭</string>
  <string name="array_dark_mode_on">开启</string>
  <string name="array_dark_mode_auto">自动</string>
  <string name="array_dark_mode_system">系统默认</string>
  <string name="array_dark_mode_battery">由省电模式设置</string>
  <string name="array_snapshot_default">询问</string>
  <string name="array_snapshot_keep">保留</string>
  <string name="array_snapshot_discard">丢弃</string>
  <!--  Detail  -->
  <string name="detail">详情</string>
  <string name="detail_label">查看应用详情</string>
  <string name="not_found">未找到</string>
  <string name="create_an_issue">协助我们来补充信息</string>
  <string name="app_bundle_details">Android App Bundle 是 Android 新推出的一种官方发布格式，可让您以更高效的方式开发和发布应用。借助 Android App Bundle，您可以更轻松地以更小的应用提供优质的使用体验，从而提升安装成功率并减少卸载量。转换过程轻松便捷。您无需重构代码即可开始获享较小应用的优势。改用这种格式后，您可以体验模块化应用开发和可自定义功能交付，并从中受益。</string>
  <string name="kotlin_details">Kotlin 是由 JetBrains 开发的开源静态类型编程语言。Kotlin 简洁、安全、可与 Java 和其他语言互操作，并提供了许多在多个平台之间重用代码的方法，从而实现高效编程。Kotlin 适用于 JVM、Android、JavaScript、Wasm 和 Native，可用于任何类型的开发，包括服务器端、客户端 Web、Android 和多平台库。</string>
  <string name="items_count">项数：</string>
  <string name="further_operation">进一步操作</string>
  <string name="app_info_launch">启动</string>
  <string name="app_info_settings">设置</string>
  <string name="lib_detail_dialog_title">库详情</string>
  <string name="lib_permission_dialog_title">权限详情</string>
  <string name="agp_details">Android Studio 构建系统以 Gradle 为基础，并且 Android Gradle 插件添加了几项专用于构建 Android 应用的功能。虽然 Android 插件通常会与 Android Studio 的更新步调保持一致，但插件（以及 Gradle 系统的其余部分）可独立于 Android Studio 运行并单独更新。</string>
  <string name="xposed_module">Xposed 模块</string>
  <string name="xposed_module_details">Xposed 是一个模块框架，可以在不接触任何 APK 的情况下更改系统和应用程序的行为。这很棒，因为这意味着模块可以在不同版本甚至 ROM 上工作而无需任何更改。</string>
  <string name="play_app_signing">Play 应用签名</string>
  <string name="play_app_signing_details">借助 Play 应用签名，Google 可以为您管理和保护您应用的签名密钥，并使用它对从您的 app bundle 生成的优化分发 APK 进行签名。Play App Signing 将您的应用签名密钥存储在 Google 的安全基础架构中，并提供升级选项以提高安全性。</string>
  <string name="pwa_details">渐进式 Web 应用程序 (PWA) 使用现代 API 构建和增强，以提供增强的功能、可靠性和可安装性，同时使用单个代码库在任何设备上访问任何人、任何地方。</string>
  <string name="jetpack_compose_details">Jetpack Compose 是用于构建原生 Android 界面的新工具包。它可简化并加快 Android 上的界面开发，使用更少的代码、强大的工具和直观的 Kotlin API，快速打造生动而精彩的应用。</string>
  <string name="jetbrain_compose_multiplatform_details">Compose Multiplatform 是一个由 JetBrains 开发的现代化声明式和响应式 UI 框架，它提供了一种只需少量 Kotlin 代码即可构建用户界面的简单方法。它还允许您只需编写一次用户界面，即可在任何支持的平台——iOS、Android、桌面（Windows、macOS、Linux）和 Web——上运行。</string>
  <string name="multi_arch_dialog_details"><![CDATA[如果某个应用提供了可供其他应用（32 位或 64 位）使用的 API，为避免可能出现的错误，必须在该应用的清单中将 <b>android:multiarch</b> 属性设为 true。]]></string>
  <string name="extract_native_libs_tip">该应用声明了 </string>
  <string name="xml_detail">XML 详情</string>
  <string name="lib_detail_dialog_title_16kb_page_size">16 KB 页大小支持</string>
  <string name="lib_detail_dialog_content_16kb_page_size">历史上，Android 仅支持 4 KB 的内存页大小，这优化了系统内存性能，适用于 Android 设备通常拥有的平均总内存量。自 Android 15 开始，AOSP 支持配置为使用 16 KB 页大小的设备（16 KB 设备）。如果您的应用程序直接或通过 SDK 间接使用任何 NDK 库，那么您需要重新构建您的应用程序，以便其在这些 16 KB 设备上正常运行。</string>
  <string name="lib_detail_dialog_title_16kb_page_size_compat">16 KB 向后兼容</string>
  <string name="lib_detail_dialog_content_16kb_page_size_compat"><![CDATA[Android 15 引入了对 16 KB 内存页面的支持，以优化平台性能。Android 16 添加了兼容模式，让一些针对 4 KB 内存页面构建的应用可以在配置为 16 KB 内存页面的设备上运行。\n\n如果 Android 检测到您的应用具有 4 KB 对齐的内存页，则会自动使用兼容模式并向用户显示通知对话框。在 <b>AndroidManifest.xml</b> 中设置 <b>android:pageSizeCompat</b> 属性以启用向后兼容模式，将会阻止应用启动时显示对话框。为了获得最佳性能、可靠性和稳定性，您的应用仍应采用 16 KB 对齐。]]></string>
  <string name="format_last_updated">更新于: %s</string>
  <string name="menu_process">进程</string>
  <string name="menu_split">模块</string>
  <string name="alternative_launch_method">备选的启动方式</string>
  <string name="compare_with_current">与当前包比较</string>
  <string name="rx_detail">RxJava 是 Reactive Extensions 的 Java VM 实现：一个使用可观察序列组成异步和基于事件的程序的库。</string>
  <string name="rx_android_detail">适用于 Android 的 Reactive Extensions</string>
  <string name="rx_kotlin_detail">RxJava 的 Kotlin 扩展</string>
  <string name="permission_not_granted">未授予</string>
  <string name="signature_detail">签名详情</string>
  <string name="signature_scheme_version">签名方案</string>
  <string name="signature_version">版本</string>
  <string name="signature_serial_number">序列号</string>
  <string name="signature_issuer">发行人</string>
  <string name="signature_subject">主题</string>
  <string name="signature_validity_not_before">有效期始</string>
  <string name="signature_validity_not_after">有效期至</string>
  <string name="signature_public_key_format">公钥格式</string>
  <string name="signature_public_key_algorithm">公钥算法</string>
  <string name="signature_public_key_exponent">公钥指数</string>
  <string name="signature_public_key_modulus_size">模数大小</string>
  <string name="signature_public_key_modulus">模数</string>
  <string name="signature_public_key_y">公钥值Y</string>
  <string name="signature_public_key_type">公钥类型</string>
  <string name="signature_algorithm_name">签名算法</string>
  <string name="signature_algorithm_oid">签名算法 OID</string>
  <string name="lib_detail_label_tip">标签</string>
  <string name="lib_detail_develop_team_tip">开发团队</string>
  <string name="lib_detail_rule_contributors_tip">规则贡献者</string>
  <string name="lib_detail_description_tip">描述</string>
  <string name="lib_detail_relative_link_tip">相关链接</string>
  <string name="lib_detail_last_update_tip">最近更新于</string>
  <string name="lib_detail_app_props_title">应用属性</string>
  <string name="lib_detail_app_props_tip">更多资料</string>
  <string name="lib_detail_xposed_min_version">Min 版本</string>
  <string name="lib_detail_xposed_default_scope">默认 Scope</string>
  <string name="lib_detail_xposed_init_class">初始化类</string>
  <string name="lib_detail_app_install_source_title">安装来源</string>
  <string name="lib_detail_app_install_source_originating_package">安装请求方</string>
  <string name="lib_detail_app_install_source_installing_package">安装执行方</string>
  <string name="lib_detail_app_installed_time">安装时间</string>
  <string name="lib_detail_app_first_installed_time">首次安装时间：</string>
  <string name="lib_detail_app_last_updated_time">最近更新时间：</string>
  <string name="lib_detail_app_install_source_empty">未知</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell 或已被卸载的应用</string>
  <string name="lib_detail_app_install_source_shizuku_usage">由于 Android 限制了获取安装请求方的接口, 我们使用 Shizuku 来获取安装请求方。</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku 未安装</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">点此安装 Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">需要 Shizuku API 版本 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">点此升级 Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku 未启动</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">点此跳转到 Shizuku 以启动它</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku 未授权</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">点此进行 Shizuku 授权以获取安装请求方</string>
  <string name="lib_detail_elf_info">更多信息</string>
  <string name="lib_detail_dependency_tip">依赖</string>
  <string name="lib_detail_entry_points_tip">入口点</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">已复制到剪贴板</string>
  <string name="toast_not_existing_market">设备中不存在应用市场</string>
  <string name="toast_use_another_file_manager">请使用其它文件管理器打开此 APK</string>
  <string name="toast_cant_open_app">无法启动此应用</string>
  <string name="toast_cloud_rules_update_error">云端规则更新失败，请重试</string>
  <string name="toast_not_enough_storage_space">存储空间不足</string>
  <string name="toast_downloading_app">已请求下载</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">保存当前快照</string>
  <string name="snapshot_current_timestamp">当前快照时间戳</string>
  <string name="snapshot_apps_count">快照应用程序数量 / 应用程序数量</string>
  <string name="comparison_snapshot_apps_count">快照应用数量</string>
  <string name="snapshot_indicator_added">增加</string>
  <string name="snapshot_indicator_removed">移除</string>
  <string name="snapshot_indicator_changed">变更</string>
  <string name="snapshot_indicator_moved">移动</string>
  <string name="snapshot_empty_list_title">没有组件变更</string>
  <string name="snapshot_no_snapshot">没有快照</string>
  <string name="snapshot_detail_new_install_title">这是一个全新的应用</string>
  <string name="snapshot_detail_deleted_title">此应用已被移除</string>
  <string name="snapshot_time_node_uninitialized">（未初始化）</string>
  <string name="snapshot_preinstalled_app">预置应用</string>
  <string name="snapshot_generate_text_report">生成文字报告</string>
  <string name="snapshot_scheme_tip"><![CDATA[提示: 现在您可以通过<br><b>%s</b><br>在后台生成快照。]]></string>
  <string name="snapshot_build_id">Build ID</string>
  <string name="snapshot_build_security_patch">安全补丁</string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">确定要重载应用列表吗？</string>
  <string name="dialog_subtitle_reload_apps">这可能会花费一点时间</string>
  <string name="dialog_title_change_timestamp">更换时间节点</string>
  <string name="dialog_title_keep_previous_snapshot">注意</string>
  <string name="dialog_message_keep_previous_snapshot">是否保留上一张快照？</string>
  <string name="dialog_title_select_to_delete">选择日期以删除快照</string>
  <string name="dialog_title_confirm_to_delete">确定要删除此快照吗？</string>
  <string name="btn_keep">保留</string>
  <string name="btn_drop">丢弃</string>
  <string name="dialog_title_compare_diff_apk">注意</string>
  <string name="dialog_message_compare_diff_apk">确定要对比两个不同的 APK 吗？</string>
  <!--  Album  -->
  <string name="album_compare">对比</string>
  <string name="album_item_comparison_title">对比</string>
  <string name="album_item_comparison_subtitle">对比两个快照</string>
  <string name="album_item_comparison_invalid_compare">无效的对比</string>
  <string name="album_item_comparison_invalid_shared_items">请选择两个 APK 文件进行比较</string>
  <string name="album_item_comparison_choose_local_apk">选择本地 APK</string>
  <string name="album_item_management_title">管理</string>
  <string name="album_item_management_subtitle">管理所有快照</string>
  <string name="album_item_management_snapshot_auto_remove_default_title">只保留最近的快照</string>
  <string name="album_item_management_snapshot_auto_remove_specific_title">只保留最近的 %d 项</string>
  <string name="album_item_management_snapshot_auto_remove_desc">从点按「%s」开始，将只保留最近的若干项快照，并自动删除旧的快照</string>
  <string name="album_item_backup_restore_title">备份与恢复</string>
  <string name="album_item_backup_restore_subtitle">备份与恢复快照</string>
  <string name="album_item_track_title">追踪</string>
  <string name="album_item_track_subtitle">选择应用进行强制对比变化</string>
  <string name="album_backup">备份</string>
  <string name="album_backup_summary">将所有快照备份</string>
  <string name="album_restore">恢复</string>
  <string name="album_restore_summary">从备份文件恢复快照</string>
  <string name="album_restore_detail">%1$s : 共 %2$s 项\n</string>
  <string name="album_click_to_choose">点击选择</string>
  <string name="album_dialog_delete_snapshot_message">处理中…</string>
  <string name="album_snapshot_top_apps_not_initialized">尚未加载</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">使用「大圣净化」禁用此组件</string>
  <string name="integration_monkey_king_menu_unblock">使用「大圣净化」解禁此组件</string>
  <string name="integration_blocker_menu_block">使用「Blocker」禁用此组件</string>
  <string name="integration_blocker_menu_unblock">使用「Blocker」解禁此组件</string>
  <string name="integration_anywhere_menu_editor">在「Anywhere- 编辑器」中打开</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">本地规则库版本</string>
  <string name="rules_remote_repo_version">远端规则库版本</string>
  <string name="rules_btn_restart_to_update">重启以更新</string>
  <string name="rules_btn_update">更新</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">显示系统应用</string>
  <string name="adv_show_overlays">显示 Overlay</string>
  <string name="adv_show_64_bit">显示 64 位应用</string>
  <string name="adv_show_32_bit">显示 32 位应用</string>
  <string name="adv_sort_mode">排序模式</string>
  <string name="adv_sort_by_time">更新时间</string>
  <string name="adv_sort_by_target_version">Target 版本</string>
  <string name="adv_sort_by_name">名称</string>
  <string name="adv_show_android_version">显示 Android 版本</string>
  <string name="adv_show_target_version">显示 Target 版本</string>
  <string name="adv_show_min_version">显示 Min 版本</string>
  <string name="adv_show_compile_version">显示 Compile 版本</string>
  <string name="adv_tint_abi_label">ABI 徽标染色</string>
  <string name="adv_mark_exported">标记已导出的组件</string>
  <string name="adv_mark_disabled">标记已禁用的组件</string>
  <string name="adv_show_marked_lib">显示已标记的库</string>
  <string name="adv_show_system_framework_apps">显示系统框架应用</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">显示更新时间</string>
  <string name="snapshot_menu_hide_no_component_changes">隐藏无组件变更</string>
  <string name="snapshot_menu_diff_highlight">差异高亮</string>
  <string name="snapshot_menu_use_iec_units">使用 IEC 单位</string>
</resources>
