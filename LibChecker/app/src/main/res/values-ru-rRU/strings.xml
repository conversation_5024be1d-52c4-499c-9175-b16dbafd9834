<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Приложения</string>
  <string name="title_statistics">Статистика</string>
  <string name="title_snapshot">Снимок</string>
  <string name="title_settings">Настройки</string>
  <string name="title_album">Альбом</string>
  <string name="loading">Загрузка…</string>
  <string name="channel_shoot">Сохранить</string>
  <string name="noti_shoot_title">Сохранение текущего снимка</string>
  <string name="noti_shoot_title_saved">Снимок сохранен</string>
  <!--  App list  -->
  <string name="menu_search">Поиск</string>
  <string name="menu_sort">Сортировка</string>
  <string name="menu_filter">Фильтр</string>
  <string name="search_hint">Поиск…</string>
  <string name="string_64_bit">64 бит</string>
  <string name="string_32_bit">32 бит</string>
  <string name="no_libs">Нет нативных библиотек</string>
  <string name="cannot_read">Невозможно прочитать</string>
  <string name="unknown">Неизвестно</string>
  <string name="empty_list">Список пуст</string>
  <string name="uncharted_territory">Неизученная территория</string>
  <string name="get_app_list_denied_tip">Пожалуйста, предоставьте\nLibChecker разрешение получить\nинформацию об установленных приложениях</string>
  <string name="advanced_menu">Расширенное меню</string>
  <string name="archived_app">Архивированные приложения</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">Приложения с %s ABI</string>
  <string name="title_statistics_dialog_no_native_libs">Приложения без нативных библиотек</string>
  <string name="tab_chart">Диаграмма</string>
  <string name="tab_lib_reference_statistics">Ссылка на библиотеку</string>
  <string name="not_marked_lib">Не отмеченная библиотека</string>
  <string name="submenu_title_component">Компонент</string>
  <string name="submenu_title_manifest">Манифест</string>
  <string name="ref_category_all">Все</string>
  <string name="ref_category_native">Нативные библиотеки</string>
  <string name="ref_category_service">Службы</string>
  <string name="ref_category_activity">Активности</string>
  <string name="ref_category_br">Широковещательные приемники</string>
  <string name="ref_category_cp">Поставщики контента</string>
  <string name="ref_category_perm">Разрешения</string>
  <string name="ref_category_static">Нативные библиотеки</string>
  <string name="ref_category_metadata">Метаданные</string>
  <string name="ref_category_package">Пакет</string>
  <string name="ref_category_shared_uid">Общий UID</string>
  <string name="ref_category_signatures">подпись</string>
  <string name="ref_category_only_not_marked">Только не помеченные</string>
  <string name="string_kotlin_used">Используется Kotlin</string>
  <string name="string_kotlin_unused">Kotlin не используется</string>
  <string name="string_compose_used">Используется Jetpack Compose</string>
  <string name="string_compose_unused">Jetpack Compose не используется</string>
  <string name="android_dist_label">Распределение</string>
  <string name="android_dist_source">Исходный код</string>
  <string name="android_dist_title">Статистика распределения версий Android</string>
  <string name="android_dist_subtitle_format">Время обновления: %s</string>
  <string name="chart_abi_detailed">Подробнее</string>
  <string name="chart_abi_concise">Меньше</string>
  <string name="chart_item_not_support">Не поддерживается</string>
  <!--  About  -->
  <string name="settings_about">О приложении</string>
  <string name="settings_about_summary">Это LibChecker!</string>
  <string name="settings_translate">Участвуйте в переводе</string>
  <string name="settings_translate_summary">Помогите нам перевести это приложение</string>
  <string name="settings_rate_us">Оцените Нас</string>
  <string name="settings_rate_us_summary">Это может сделать нас доступнее для большего числа людей</string>
  <string name="settings_get_updates">Обновления</string>
  <string name="settings_get_updates_summary">Получить последнюю версию, которая является более стабильной и имеет больше функционала</string>
  <string name="settings_get_updates_in_app">В приложении</string>
  <string name="settings_get_updates_in_app_chip_stable">Стабильное</string>
  <string name="settings_get_updates_in_app_chip_ci">CI</string>
  <string name="about_info">Это приложение используется для просмотра сторонних библиотек, используемых приложениями на вашем устройстве.</string>
  <string name="toolbar_rate">Оценка</string>
  <string name="resource_declaration">Часть ресурсов в приложении поступает из: </string>
  <string name="library_declaration">Вся информация об отмеченных библиотеках в LibChecker берется из документации для разработчиков или из репозитория кода SDK, которому он принадлежит. Если информация неверна, пожалуйста, обращайтесь: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Общие</string>
  <string name="pref_group_others">Другие</string>
  <string name="apk_analytics">Анализирование АРК</string>
  <string name="colorful_icon">Цветные значки</string>
  <string name="rules_repo_title">Репозиторий правил</string>
  <string name="lib_ref_threshold">Порог числа ссылок на библиотеку</string>
  <string name="languages">Язык</string>
  <string name="reload_apps">Обновить список приложений</string>
  <string name="help_docs">Справочная документация</string>
  <string name="join_telegram_group">Связь</string>
  <string name="anonymous_statistics">Анонимная статистика</string>
  <string name="cloud_rules">Облачные правила</string>
  <string name="dark_mode">Темный режим</string>
  <string name="snapshot_keep">Стандартное поведение при сохранении снимков</string>
  <string name="apk_analytics_summary">Открывает страницу сведений при нажатии на APK-файл</string>
  <string name="colorful_icon_summary">Часть отмеченных библиотек будет отображаться в виде цветных значков.</string>
  <string name="lib_ref_threshold_summary">В списке отображаются только те библиотеки, число ссылок на которые превысило заданный порог</string>
  <string name="reload_apps_summary">Попробуйте, если список приложений отображается неправильно</string>
  <string name="help_docs_summary">Ознакомьтесь, как использовать LibChecker</string>
  <string name="join_telegram_group_summary">Присоединяйтесь к нашей группе в Telegram</string>
  <string name="anonymous_statistics_summary">Мы используем Google Firebase для анонимной отправки наиболее часто используемых помеченных библиотек и некоторых данных о привычках использования, чтобы сделать LibChecker более полезным</string>
  <string name="cloud_rules_summary">Обновите последние отмеченные правила библиотеки здесь</string>
  <string name="array_dark_mode_off">Выкл</string>
  <string name="array_dark_mode_on">Вкл</string>
  <string name="array_dark_mode_auto">Авто</string>
  <string name="array_dark_mode_system">Как в системе</string>
  <string name="array_dark_mode_battery">Зависит от режима энергосбережения</string>
  <string name="array_snapshot_default">Спросить</string>
  <string name="array_snapshot_keep">Оставить</string>
  <string name="array_snapshot_discard">Удалить</string>
  <!--  Detail  -->
  <string name="detail">подробности</string>
  <string name="detail_label">Просмотр сведений о приложении</string>
  <string name="not_found">Не найдено</string>
  <string name="create_an_issue">Помогите нам дополнить информацию</string>
  <string name="app_bundle_details">Android App Bundle - это новый официальный формат публикации приложений в ОС Android, который предлагает более эффективный способ создания и выпуска вашего приложения. Android App Bundle позволяет выпускать приложения меньшего размера, что может повысить успешность установки и сократить количество удалений. Переключиться на App Bundle легко. Вам не нужно проводить рефакторинг кода, чтобы получить пользу от небольшого размера приложения. И после перехода вы получите выгоду от разработки модульных приложений и настраиваемой доставки функций.</string>
  <string name="kotlin_details">Kotlin - это кроссплатформенный язык программирования общего назначения со статической типизацией и выводом типов. Kotlin разработан для полного взаимодействия с Java, а версия JVM стандартной библиотеки Kotlin зависит от библиотеки классов Java, но вывод типа позволяет его синтаксису быть более лаконичным.</string>
  <string name="items_count">Кол-во пунктов: </string>
  <string name="further_operation">Дополнительные опции</string>
  <string name="app_info_launch">Запустить</string>
  <string name="app_info_settings">Настройки</string>
  <string name="lib_detail_dialog_title">Подробности библиотеки</string>
  <string name="lib_permission_dialog_title">Детали разрешения</string>
  <string name="agp_details">Система сборки Android Studio основана на Gradle, а подключаемый модуль Android Gradle добавляет несколько функций, характерных для создания приложений для Android. Хотя подключаемый модуль Android обычно обновляется одновременно с Android Studio, этот подключаемый модуль (и остальная часть системы Gradle) может работать независимо от Android Studio и обновляться отдельно.</string>
  <string name="xposed_module">Xposed модуль</string>
  <string name="xposed_module_details">Xposed — это фреймворк для модулей, которые могут изменить поведение системы и приложений, не затрагивая никаких APK. Это здорово, потому что это означает, что модули могут работать с разными версиями ОС и прошивками без каких-либо изменений.</string>
  <string name="play_app_signing">Play подпись приложения</string>
  <string name="play_app_signing_details">С помощью Play App Signing Google управляет ключом подписи вашего приложения и защищает его от вашего имени и использует его для подписи оптимизированных APK-файлов для распространения, которые создаются из ваших пакетов приложений. Play App Signing сохраняет ключ подписи вашего приложения в защищённой инфраструктуре Google и предлагает варианты обновления для повышения безопасности.</string>
  <string name="pwa_details">Прогрессивные веб-приложения (PWA) создаются и улучшаются с помощью современных API-интерфейсов, чтобы обеспечить расширенные возможности, надёжность и простоту установки, а также охватывать всех где угодно и на любом устройстве с помощью единой кодовой базы.</string>
  <string name="jetpack_compose_details">Jetpack Compose - это современный набор инструментов для создания нативных пользовательских интерфейсов в Android. Он упрощает и ускоряет разработку пользовательского интерфейса на Android. Вы сможете быстро воплотить в жизнь своё приложение благодаря меньшему количеству кода, мощным инструментам и интуитивно понятным API-интерфейсам Kotlin.</string>
  <string name="jetbrain_compose_multiplatform_details">Compose Multiplatform - это современный декларативный и реактивный UI фреймворк, разработанный JetBrains, который предоставляет простой способ создания пользовательских интерфейсов с небольшим количеством кода Kotlin. Он также позволяет написать пользовательский интерфейс единожды и запускать его на любой из поддерживаемых платформ – iOS, Android, ПК (Windows, macOS, Linux) и веб.</string>
  <string name="extract_native_libs_tip">Приложение декларирует </string>
  <string name="xml_detail">XML Деталь</string>
  <string name="lib_detail_dialog_title_16kb_page_size">Размер страницы 16 КБ</string>
  <string name="lib_detail_dialog_content_16kb_page_size">Исторически, Android имел поддержку только 4 КБ памяти для страниц, что позволяло оптимизировать производительность системной памяти относительно среднего объёма общей памяти, которым обычно располагали устройства Android. Начиная с Android 15, AOSP поддерживает устройства, с размером страниц в 16 КБ (устройства с 16 КБ). Если ваше приложение использует какие-либо библиотеки NDK, прямо или косвенно через SDK, то вам придётся переделать своё приложение, чтобы оно работало на устройствах с 16 КБ.</string>
  <string name="lib_detail_dialog_title_16kb_page_size_compat">16 КБ обратная совместимость (Backcompat)</string>
  <string name="lib_detail_dialog_content_16kb_page_size_compat"><![CDATA[В Android 15 появилась поддержка страниц памяти размером 16 КБ для оптимизации производительности платформы. В Android 16 добавлен режим совместимости, позволяющий некоторым приложениям, созданным для страниц памяти размером 4 КБ, работать на устройстве, настроенном для страниц памяти размером 16 КБ.\n\nЕсли Android обнаруживает, что у вашего приложения есть выровненные страницы памяти размером 4 КБ, он автоматически использует режим совместимости и отображает диалоговое окно уведомления для пользователя. Установка свойства <b>android:pageSizeCompat</b> в <b>AndroidManifest.xml</b> для включения режима обратной совместимости предотвратит отображение диалогового окна при запуске вашего приложения. Для лучшей производительности, надёжности и стабильности ваше приложение по-прежнему должно быть выровнено по размеру 16 КБ.]]></string>
  <string name="format_last_updated">Обновлено: %s</string>
  <string name="menu_process">Приложение</string>
  <string name="menu_split">Разделение</string>
  <string name="alternative_launch_method">Альтернативный метод запуска</string>
  <string name="compare_with_current">Сравнить с текущим пакетом</string>
  <string name="rx_detail">RxJava — это реализация Reactive Extensions на виртуальной машине Java: библиотека для составления асинхронных и событийных программ с использованием наблюдаемых последовательностей.</string>
  <string name="rx_android_detail">Reactive Extensions для Android</string>
  <string name="rx_kotlin_detail">Расширения Kotlin для RxJava</string>
  <string name="permission_not_granted">Не предоставлено</string>
  <string name="signature_detail">Детали подписи</string>
  <string name="signature_version">Версия</string>
  <string name="signature_serial_number">Серийный номер</string>
  <string name="signature_issuer">Издатель</string>
  <string name="signature_subject">Тема</string>
  <string name="signature_validity_not_before">Срок действия не ранее</string>
  <string name="signature_validity_not_after">Срок действия не позже</string>
  <string name="signature_public_key_format">Формат публичного ключа</string>
  <string name="signature_public_key_algorithm">Алгоритм публичного ключа</string>
  <string name="signature_public_key_exponent">Экспонента публичного ключа</string>
  <string name="signature_public_key_modulus_size">Размер модуля публичного ключа</string>
  <string name="signature_public_key_modulus">Модуль публичного ключа</string>
  <string name="signature_public_key_y">Значение Y публичного ключа</string>
  <string name="signature_public_key_type">Тип публичного ключа</string>
  <string name="signature_algorithm_name">Название алгоритма подписи</string>
  <string name="signature_algorithm_oid">OID алгоритма подписи</string>
  <string name="lib_detail_label_tip">Метка</string>
  <string name="lib_detail_develop_team_tip">Команда разработчиков</string>
  <string name="lib_detail_rule_contributors_tip">Вкладчик(и)</string>
  <string name="lib_detail_description_tip">Описание</string>
  <string name="lib_detail_relative_link_tip">Связанные ссылки</string>
  <string name="lib_detail_last_update_tip">Обновлено</string>
  <string name="lib_detail_app_props_title">Свойства приложения</string>
  <string name="lib_detail_app_props_tip">Дополнительная информация</string>
  <string name="lib_detail_xposed_min_version">Мин. версия</string>
  <string name="lib_detail_xposed_default_scope">Область по умолчанию</string>
  <string name="lib_detail_xposed_init_class">Начальный класс</string>
  <string name="lib_detail_app_install_source_title">Источник установки</string>
  <string name="lib_detail_app_install_source_originating_package">Инициатор установки</string>
  <string name="lib_detail_app_install_source_installing_package">Исполнитель установки</string>
  <string name="lib_detail_app_installed_time">Время установки</string>
  <string name="lib_detail_app_first_installed_time">Время первой установки: </string>
  <string name="lib_detail_app_last_updated_time">Время последней установки: </string>
  <string name="lib_detail_app_install_source_empty">Неизвестно</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell или отдалённое приложение</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Поскольку Android ограничивает API запроса на установку, мы используем Shizuku для его получения.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku не установлен</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">Нажмите здесь, чтобы установить Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Требуется Shizuku API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">Нажмите здесь, чтобы обновить Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku не запущен</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">Нажмите здесь, чтобы перейти к Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku не авторизован</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">Нажмите здесь для авторизации Shizuku для инсталлятора</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Скопировано в буфер обмена</string>
  <string name="toast_not_existing_market">Нет магазина приложений</string>
  <string name="toast_use_another_file_manager">Пожалуйста, используйте другой файловый менеджер, чтобы открыть APK</string>
  <string name="toast_cant_open_app">«Невозможно открыть это приложение»</string>
  <string name="toast_cloud_rules_update_error">Не удалось обновить облачные правила. Пожалуйста, попробуйте ещё раз.</string>
  <string name="toast_not_enough_storage_space">Недостаточно места</string>
  <string name="toast_downloading_app">Началось скачивание</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Сохранить текущий снимок</string>
  <string name="snapshot_current_timestamp">Отметка времени текущего снимка</string>
  <string name="snapshot_apps_count">Количество приложений в снимке / Количество приложений</string>
  <string name="comparison_snapshot_apps_count">Количество приложений в снимке</string>
  <string name="snapshot_indicator_added">Добавлено</string>
  <string name="snapshot_indicator_removed">Удалено</string>
  <string name="snapshot_indicator_changed">Изменено</string>
  <string name="snapshot_indicator_moved">Перемещено</string>
  <string name="snapshot_empty_list_title">Нет изменений</string>
  <string name="snapshot_no_snapshot">Нет снимка</string>
  <string name="snapshot_detail_new_install_title">Это приложение недавно установлено</string>
  <string name="snapshot_detail_deleted_title">Это приложение было удалено</string>
  <string name="snapshot_time_node_uninitialized">(Неинициализировано)</string>
  <string name="snapshot_preinstalled_app">Предустановленное приложение</string>
  <string name="snapshot_generate_text_report">Создать текстовый отчет</string>
  <string name="snapshot_scheme_tip"><![CDATA[Совет: Теперь вы можете генерировать снимки в фоновом режиме через:<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Вы уверены, что хотите перезагрузить все приложения?</string>
  <string name="dialog_subtitle_reload_apps">Это может занять несколько секунд</string>
  <string name="dialog_title_change_timestamp">Выбор временного узла</string>
  <string name="dialog_title_keep_previous_snapshot">Внимание</string>
  <string name="dialog_message_keep_previous_snapshot">Вы хотите сохранить предыдущий снимок?</string>
  <string name="dialog_title_select_to_delete">Выберите дату для удаления снимка</string>
  <string name="dialog_title_confirm_to_delete">Вы уверены, что хотите удалить этот снимок?</string>
  <string name="btn_keep">Оставить</string>
  <string name="btn_drop">Удалить</string>
  <string name="dialog_title_compare_diff_apk">Внимание</string>
  <string name="dialog_message_compare_diff_apk">Вы хотите сравнить два различных APK?</string>
  <!--  Album  -->
  <string name="album_compare">Сравнить</string>
  <string name="album_item_comparison_title">Сравнение</string>
  <string name="album_item_comparison_subtitle">Сравнение двух снимков</string>
  <string name="album_item_comparison_invalid_compare">Недопустимое сравнение</string>
  <string name="album_item_comparison_invalid_shared_items">Пожалуйста, выберите два APK файла для сравнения</string>
  <string name="album_item_comparison_choose_local_apk">Выберите локальный APK</string>
  <string name="album_item_management_title">Менеджер</string>
  <string name="album_item_management_subtitle">Управление всеми снимками</string>
  <string name="album_item_management_snapshot_auto_remove_default_title">Оставить только самые последние снимки</string>
  <string name="album_item_management_snapshot_auto_remove_specific_title">Оставить только %d последних элементов</string>
  <string name="album_item_management_snapshot_auto_remove_desc">Начиная с нажатия на «%s», только последние снимки будут сохранены, более старые будут автоматически удалены.</string>
  <string name="album_item_backup_restore_title">Резервирование и восстановление</string>
  <string name="album_item_backup_restore_subtitle">Резервирование и восстановление снимков</string>
  <string name="album_item_track_title">Отслеживание</string>
  <string name="album_item_track_subtitle">Выберите приложения для принудительного сравнения изменений</string>
  <string name="album_backup">Бекап</string>
  <string name="album_backup_summary">Резервирование всех снимков</string>
  <string name="album_restore">Восстановление</string>
  <string name="album_restore_summary">Восстановление снимков из резервной копии</string>
  <string name="album_restore_detail">%1$s : %2$s элементов всего\n</string>
  <string name="album_click_to_choose">Нажмите, чтобы выбрать</string>
  <string name="album_dialog_delete_snapshot_message">Обработка…</string>
  <string name="album_snapshot_top_apps_not_initialized">Ещё не загружено</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Заблокировать с помощью «MonkeyKing Purify»</string>
  <string name="integration_monkey_king_menu_unblock">Разблокировать с помощью «MonkeyKing Purify»</string>
  <string name="integration_blocker_menu_block">Заблокировать через «Blocker»</string>
  <string name="integration_blocker_menu_unblock">Разблокировать через «Blocker»</string>
  <string name="integration_anywhere_menu_editor">Открыть в «Anywhere- Editor»</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Версия локального репозитория</string>
  <string name="rules_remote_repo_version">Версия отдалённого репозитория</string>
  <string name="rules_btn_restart_to_update">Перезагрузите, чтобы обновить</string>
  <string name="rules_btn_update">Обновить</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">Показать системные приложения</string>
  <string name="adv_show_overlays">Показать оверлеи</string>
  <string name="adv_show_64_bit">Показывать 64-битные приложения</string>
  <string name="adv_show_32_bit">Показывать 32-битные приложения</string>
  <string name="adv_sort_mode">Тип сортировки</string>
  <string name="adv_sort_by_time">Обновлено</string>
  <string name="adv_sort_by_target_version">Целевая версия</string>
  <string name="adv_sort_by_name">Название</string>
  <string name="adv_show_android_version">Показать версию Android</string>
  <string name="adv_show_target_version">Показать целевую версию</string>
  <string name="adv_show_min_version">Показать минимальную версию</string>
  <string name="adv_show_compile_version">Показать версию компиляции</string>
  <string name="adv_tint_abi_label">Tint ABI метка</string>
  <string name="adv_mark_exported">Отметить экспортируемый компонент</string>
  <string name="adv_mark_disabled">Отметить неактивный компонент</string>
  <string name="adv_show_marked_lib">Показать отмеченные библиотеки</string>
  <string name="adv_show_system_framework_apps">Показать фреймворки</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Показать время обновления</string>
  <string name="snapshot_menu_hide_no_component_changes">Скрыть компоненты без изменений</string>
  <string name="snapshot_menu_diff_highlight">Показать различия</string>
  <string name="snapshot_menu_use_iec_units">Использовать единицы IEC</string>
</resources>
