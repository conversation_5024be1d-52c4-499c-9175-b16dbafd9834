<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Apps</string>
  <string name="title_statistics">Statistiken</string>
  <string name="title_snapshot"><PERSON><PERSON>appschuss</string>
  <string name="title_settings">Einstellungen</string>
  <string name="title_album">Album</string>
  <string name="loading">L<PERSON><PERSON>…</string>
  <string name="channel_shoot">Schnappschuss speichern</string>
  <string name="noti_shoot_title">Aktuellen Speichere Schnappschuss</string>
  <string name="noti_shoot_title_saved">Ein Schnappschuss wurde gespeichert</string>
  <!--  App list  -->
  <string name="menu_search">Suchen</string>
  <string name="menu_sort">Sortierung</string>
  <string name="menu_filter">Filter</string>
  <string name="search_hint"><PERSON><PERSON>…</string>
  <string name="string_64_bit">64Bit</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs">Keine nativen Bibliotheken</string>
  <string name="cannot_read">Lesen nicht möglich</string>
  <string name="unknown">Unbekannt</string>
  <string name="empty_list">Die Liste ist leer</string>
  <string name="uncharted_territory">Unerforschtes Gebiet</string>
  <string name="get_app_list_denied_tip">Bitte erteilen Sie\n\"Informationen über installierte Apps\"\nBerechtigung für LibChecker</string>
  <string name="advanced_menu">Erweitertes Menü</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">Apps mit %s ABI</string>
  <string name="title_statistics_dialog_no_native_libs">Apps ohne native Bibliotheken</string>
  <string name="tab_chart">Diagramm</string>
  <string name="tab_lib_reference_statistics">Bibliotheken Referenz</string>
  <string name="not_marked_lib">Nicht markierte Bibliothek</string>
  <string name="submenu_title_component">Komponente</string>
  <string name="submenu_title_manifest">Manifest</string>
  <string name="ref_category_all">Alle</string>
  <string name="ref_category_native">Native Bibliotheken</string>
  <string name="ref_category_service">Services</string>
  <string name="ref_category_activity">Activities</string>
  <string name="ref_category_br">Rundfunkempfänger</string>
  <string name="ref_category_cp">Inhaltsanbieter</string>
  <string name="ref_category_perm">Berechtigungen</string>
  <string name="ref_category_static">Statische Bibliotheken</string>
  <string name="ref_category_metadata">Metadaten</string>
  <string name="ref_category_package">Paket</string>
  <string name="ref_category_shared_uid">Gemeinsame UID</string>
  <string name="ref_category_signatures">Signaturen</string>
  <string name="ref_category_only_not_marked">Nur nicht markiert</string>
  <string name="string_kotlin_used">Nutzt Kotlin</string>
  <string name="string_kotlin_unused">Nutzt kein Kotlin</string>
  <string name="string_compose_used">Nutzt Jetpack Compose</string>
  <string name="string_compose_unused">Nutzt kein Jetpack Compose</string>
  <string name="android_dist_label">Vertrieb</string>
  <string name="android_dist_source">Quelle</string>
  <string name="android_dist_title">Android Version Vertriebsstatistik</string>
  <string name="android_dist_subtitle_format">Updatezeit: %s</string>
  <!--  About  -->
  <string name="settings_about">Über</string>
  <string name="settings_about_summary">Dies ist LibChecker!</string>
  <string name="settings_translate">Beteilige dich an der Übersetzung</string>
  <string name="settings_translate_summary">Hilf uns die App zu übersetzen</string>
  <string name="settings_rate_us">Bewerte uns</string>
  <string name="settings_rate_us_summary">Dies hilft uns von mehr Leuten gefunden zu werden</string>
  <string name="settings_get_updates">Erhalte Updates</string>
  <string name="settings_get_updates_summary">Hole dir die neuste Version, die stabiler und mit Funktionen voll gepackt ist</string>
  <string name="about_info">Diese App wird verwendet, um die von Anwendungen auf Ihrem Gerät verwendeten Bibliotheken von Drittanbietern anzuzeigen.</string>
  <string name="toolbar_rate">Bewerten sie diese App</string>
  <string name="resource_declaration">Ein Teil der Ressourcen in der Anwendung stammt aus: </string>
  <string name="library_declaration">Alle in LibChecker markierten Bibliotheksinformationen stammen aus der Entwicklungsdokumentation oder dem Code Repository des SDK, zu dem es gehört. Wenn die Informationen falsch sind, kontaktieren Sie bitte die folgende E-Mail-Adresse: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Normal</string>
  <string name="pref_group_others">Sonstige</string>
  <string name="apk_analytics">APK-Analyse</string>
  <string name="colorful_icon">Buntes Icon</string>
  <string name="rules_repo_title">Regelrepo</string>
  <string name="lib_ref_threshold">Bibliotheksreferenz-Grenzwert</string>
  <string name="languages">Sprachen</string>
  <string name="reload_apps">App-Liste aktualisieren</string>
  <string name="help_docs">Handbuch</string>
  <string name="join_telegram_group">Kommunikation</string>
  <string name="anonymous_statistics">Anonyme Statistiken</string>
  <string name="cloud_rules">Cloud-Regeln</string>
  <string name="dark_mode">Nachtmodus</string>
  <string name="array_dark_mode_off">Aus</string>
  <string name="array_dark_mode_on">An</string>
  <!--  Detail  -->
  <!--  Toast  -->
  <!--  Snapshot  -->
  <!--  Dialog  -->
  <!--  Album  -->
  <string name="album_item_track_subtitle">Wählen Sie Anwendungen aus, um eine Vergleichsänderung zu erzwingen</string>
  <string name="album_backup">Sicherung</string>
  <string name="album_backup_summary">Alle Schnappschüsse sichern</string>
  <string name="album_restore">Wiederherstellen</string>
  <string name="album_restore_summary">Schnappschüsse aus der Sicherungsdatei wiederherstellen</string>
  <string name="album_restore_detail">%1$s : %2$s Insgesamt Elemente\n</string>
  <string name="album_click_to_choose">Zum Auswählen klicken</string>
  <string name="album_dialog_delete_snapshot_message">Verarbeitung…</string>
  <string name="album_snapshot_top_apps_not_initialized">Noch nicht geladen</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Über „MonkeyKing Purify“ blockieren</string>
  <string name="integration_monkey_king_menu_unblock">Über „MonkeyKing Purify“ entsperren</string>
  <string name="integration_blocker_menu_block">Im „Blocker“ blockieren</string>
  <string name="integration_blocker_menu_unblock">Im „Blocker“ entsperren</string>
  <string name="integration_anywhere_menu_editor">Im „Anywhere-Editor“ öffnen</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Version des lokalen Repositories</string>
  <string name="rules_remote_repo_version">Version des Remote-Repositories</string>
  <string name="rules_btn_restart_to_update">Zum Aktualisieren neu starten</string>
  <string name="rules_btn_update">Aktualisierung</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">System-Apps anzeigen</string>
  <string name="adv_show_overlays">Overlays anzeigen</string>
  <string name="adv_show_64_bit">64-Bit-Apps anzeigen</string>
  <string name="adv_show_32_bit">32-Bit-Apps anzeigen</string>
  <string name="adv_sort_mode">Sortiermodus</string>
  <string name="adv_sort_by_time">Aktualisierte Zeit</string>
  <string name="adv_sort_by_target_version">Zielversion</string>
  <string name="adv_sort_by_name">Name</string>
  <string name="adv_show_android_version">Android-Version anzeigen</string>
  <string name="adv_show_target_version">Zielversion anzeigen</string>
  <string name="adv_show_min_version">Minimale Version anzeigen</string>
  <string name="adv_show_compile_version">Kompilierte Version anzeigen</string>
  <string name="adv_tint_abi_label">ABI-Bezeichnungsfarbe ändern</string>
  <string name="adv_mark_exported">Exportierte Komponente markieren</string>
  <string name="adv_mark_disabled">Deaktivierte Komponente markieren</string>
  <string name="adv_show_marked_lib">Markierte Bibliotheken anzeigen</string>
  <string name="adv_show_system_framework_apps">Framework-Apps anzeigen</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Aktualisierungszeit anzeigen</string>
  <string name="snapshot_menu_hide_no_component_changes">Keine Komponentenänderungen ausblenden</string>
  <string name="snapshot_menu_diff_highlight">Differenz hervorheben</string>
  <string name="snapshot_menu_use_iec_units">IEC-Einheiten verwenden</string>
</resources>
