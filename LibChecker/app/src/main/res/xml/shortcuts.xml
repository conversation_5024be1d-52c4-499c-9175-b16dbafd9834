<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  tools:targetApi="n_mr1">

  <shortcut
    android:icon="@drawable/ic_sc_chart"
    android:shortcutId="start_chart"
    android:shortcutShortLabel="@string/tab_chart">

    <intent
      android:action="android.intent.action.VIEW"
      android:targetClass="com.absinthe.libchecker.features.chart.ui.ChartActivity"
      android:targetPackage="com.absinthe.libchecker" />
  </shortcut>

  <shortcut
    android:icon="@drawable/ic_sc_statistics"
    android:shortcutId="start_statistics"
    android:shortcutShortLabel="@string/title_statistics">

    <intent
      android:action="com.absinthe.libchecker.intent.action.START_STATISTICS"
      android:targetClass="com.absinthe.libchecker.features.home.ui.MainActivity"
      android:targetPackage="com.absinthe.libchecker" />
  </shortcut>

  <shortcut
    android:icon="@drawable/ic_sc_snapshot"
    android:shortcutId="start_snapshot"
    android:shortcutShortLabel="@string/title_snapshot">

    <intent
      android:action="com.absinthe.libchecker.intent.action.START_SNAPSHOT"
      android:targetClass="com.absinthe.libchecker.features.home.ui.MainActivity"
      android:targetPackage="com.absinthe.libchecker" />
  </shortcut>

</shortcuts>
