<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto">

  <PreferenceCategory
    app:key="normalCategory"
    app:title="@string/pref_group_normal">

    <rikka.material.preference.MaterialSwitchPreference
      app:defaultValue="true"
      app:icon="@drawable/ic_apk_analytics"
      app:key="apkAnalytics"
      app:summary="@string/apk_analytics_summary"
      app:title="@string/apk_analytics" />

    <rikka.material.preference.MaterialSwitchPreference
      app:defaultValue="true"
      app:icon="@drawable/ic_color_palette"
      app:key="colorfulIcon"
      app:summary="@string/colorful_icon_summary"
      app:title="@string/colorful_icon" />

    <rikka.preference.SimpleMenuPreference
      app:defaultValue="notify"
      app:entries="@array/snapshot_keep"
      app:entryValues="@array/snapshot_keep_value"
      app:icon="@drawable/ic_outline_save"
      app:key="snapshotKeep"
      app:summary="%s"
      app:title="@string/snapshot_keep" />

    <rikka.preference.SimpleMenuPreference
      app:defaultValue="system"
      app:entries="@array/list_dark_mode"
      app:entryValues="@array/list_dark_mode_value"
      app:icon="@drawable/ic_dark_mode"
      app:key="darkMode"
      app:summary="%s"
      app:title="@string/dark_mode" />

    <rikka.preference.SimpleMenuPreference
      app:defaultValue="gitlab"
      app:entries="@array/list_repo"
      app:entryValues="@array/list_repo_value"
      app:icon="@drawable/ic_repository"
      app:key="rulesRepository"
      app:summary="%s"
      app:title="@string/rules_repo_title" />

    <ListPreference
      app:defaultValue="SYSTEM"
      app:entries="@array/language"
      app:entryValues="@array/language_value"
      app:icon="@drawable/ic_language"
      app:key="locale"
      app:summary="%s"
      app:title="@string/languages" />

    <Preference
      app:icon="@drawable/ic_cloud_rules"
      app:key="cloudRules"
      app:summary="@string/cloud_rules_summary"
      app:title="@string/cloud_rules" />

    <Preference
      app:icon="@drawable/ic_threshold"
      app:key="libRefThreshold"
      app:summary="@string/lib_ref_threshold_summary"
      app:title="@string/lib_ref_threshold" />

    <Preference
      app:icon="@drawable/ic_refresh"
      app:key="reloadApps"
      app:summary="@string/reload_apps_summary"
      app:title="@string/reload_apps" />

    <Preference
      app:icon="@drawable/ic_log"
      app:key="exportLog"
      app:summary="@string/export_log_summary"
      app:title="@string/export_log" />

  </PreferenceCategory>

  <PreferenceCategory
    app:key="othersCategory"
    app:title="@string/pref_group_others">

    <Preference
      app:icon="@drawable/ic_logo"
      app:key="about"
      app:summary="@string/settings_about_summary"
      app:title="@string/settings_about">

    </Preference>

    <Preference
      app:icon="@drawable/ic_upgrade"
      app:key="getUpdates"
      app:summary="@string/settings_get_updates_summary"
      app:title="@string/settings_get_updates">

    </Preference>

    <Preference
      app:icon="@drawable/ic_translate"
      app:key="translation"
      app:summary="@string/settings_translate_summary"
      app:title="@string/settings_translate">

    </Preference>

    <Preference
      app:icon="@drawable/ic_help"
      app:key="help"
      app:summary="@string/help_docs_summary"
      app:title="@string/help_docs">

    </Preference>

    <Preference
      android:icon="@drawable/ic_favorite"
      app:key="rate"
      app:summary="@string/settings_rate_us_summary"
      app:title="@string/settings_rate_us">

    </Preference>

    <Preference
      app:icon="@drawable/ic_lib_telegram"
      app:key="tg"
      app:summary="@string/join_telegram_group_summary"
      app:title="@string/join_telegram_group">

    </Preference>

    <rikka.material.preference.MaterialSwitchPreference
      app:defaultValue="true"
      app:icon="@drawable/ic_firebase_monochrome"
      app:key="analytics"
      app:summary="@string/anonymous_statistics_summary"
      app:title="@string/anonymous_statistics" />

  </PreferenceCategory>

</PreferenceScreen>
