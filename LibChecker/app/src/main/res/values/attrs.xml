<?xml version="1.0" encoding="utf-8"?>
<resources>
  <declare-styleable name="SnapshotClassIndicatorView">
    <attr name="icon" format="reference" />
    <attr name="indicator" format="color" />
    <attr name="text" format="string" />
  </declare-styleable>

  <declare-styleable name="AlbumItemView">
    <attr name="albumItemTitle" format="string" />
    <attr name="albumItemSubtitle" format="string" />
    <attr name="itemIcon" format="reference" />
    <attr name="itemIconTint" format="color" />
  </declare-styleable>

  <declare-styleable name="AppInfoItemView">
    <attr name="itemTitle" format="string" />
    <attr name="infoSrc" format="reference" />
    <attr name="infoBackgroundTint" format="color" />
  </declare-styleable>

  <declare-styleable name="BottomSheetHeaderView">
    <attr name="bottomSheetHeaderHandlerColor" format="color" />
    <attr name="bottomSheetHeaderHandlerColorActivated" format="color" />
  </declare-styleable>

  <declare-styleable name="AlbumMaterialCard">
    <attr name="albumCardViewColorSurface" format="color" />
    <attr name="albumCardBorderColor" format="color" />
  </declare-styleable>

  <attr name="checkableChipViewStyle" format="reference"/>

  <declare-styleable name="CheckableChipView">
    <attr name="android:checked"/>
    <attr name="android:text"/>
    <attr name="android:textColor"/>
    <attr name="android:textSize"/>
    <attr name="ccv_checkedTextColor" format="color"/>

    <attr name="android:color"/>
    <attr name="android:padding"/>

    <attr name="ccv_outlineCornerRadius" format="dimension"/>
    <attr name="ccv_outlineColor" format="color"/>
    <attr name="ccv_outlineWidth" format="dimension"/>

    <attr name="ccv_foreground" format="reference"/>
    <attr name="ccv_clearIcon" format="reference"/>
  </declare-styleable>
</resources>
