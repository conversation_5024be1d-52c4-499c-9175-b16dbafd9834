<resources>
  <!--  General  -->
  <string name="title_app_list">Apps</string>
  <string name="title_statistics">Statistics</string>
  <string name="title_snapshot">Snapshots</string>
  <string name="title_settings">Settings</string>
  <string name="title_album">Album</string>
  <string name="loading">Loading…</string>
  <string name="channel_shoot">Save snapshots</string>
  <string name="noti_shoot_title">Saving current snapshot</string>
  <string name="noti_shoot_title_saved">A snapshot has saved</string>

  <!--  App list  -->
  <string name="menu_search">Search</string>
  <string name="menu_sort">Sort</string>
  <string name="menu_filter">Filter</string>
  <string name="search_hint">Search…</string>
  <string name="string_64_bit">64bit</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs">No native libs</string>
  <string name="cannot_read">Can\'t read</string>
  <string name="unknown">Unknown</string>
  <string name="empty_list">Empty list</string>
  <string name="uncharted_territory">Uncharted territory</string>
  <string name="get_app_list_denied_tip">Please grant\n"Get info about installed apps"\npermission to LibChecker</string>
  <string name="advanced_menu">Advanced Menu</string>
  <string name="archived_app">Archived app</string>

  <!-- Statistics   -->
  <string name="title_statistics_dialog">Apps with %s ABI</string>
  <string name="title_statistics_dialog_no_native_libs">Apps without native libraries</string>
  <string name="tab_chart">Chart</string>
  <string name="tab_lib_reference_statistics">Lib Reference</string>
  <string name="not_marked_lib">Unmarked library</string>

  <string name="submenu_title_component">Component</string>
  <string name="submenu_title_manifest">Manifest</string>
  <string name="ref_category_all">All</string>
  <string name="ref_category_native">Native Libraries</string>
  <string name="ref_category_service">Services</string>
  <string name="ref_category_activity">Activities</string>
  <string name="ref_category_br">Broadcast Receivers</string>
  <string name="ref_category_cp">Content Providers</string>
  <string name="ref_category_perm">Permissions</string>
  <string name="ref_category_static">Static Libraries</string>
  <string name="ref_category_metadata">Meta Data</string>
  <string name="ref_category_package">Package</string>
  <string name="ref_category_shared_uid">Shared UID</string>
  <string name="ref_category_signatures">Signatures</string>
  <string name="ref_category_only_not_marked">Only Not Marked</string>
  <string name="ref_category_action">Action</string>

  <string name="string_kotlin_used">Kotlin Used</string>
  <string name="string_kotlin_unused">Kotlin Unused</string>
  <string name="string_compose_used">Jetpack Compose Used</string>
  <string name="string_compose_unused">Jetpack Compose Unused</string>

  <string name="android_dist_label">Distribution</string>
  <string name="android_dist_source">Source</string>
  <string name="android_dist_title">Android Version Distribution Statistics</string>
  <string name="android_dist_subtitle_format">Update Time: %s</string>

  <string name="chart_abi_detailed">Detailed</string>
  <string name="chart_abi_concise">Concise</string>
  <string name="chart_item_not_support">Not Support</string>

  <!--  About  -->
  <string name="settings_about">About</string>
  <string name="settings_about_summary">This is LibChecker!</string>
  <string name="settings_translate">Participate in Translation</string>
  <string name="settings_translate_summary">Help us translate this app</string>
  <string name="settings_rate_us">Rate Us</string>
  <string name="settings_rate_us_summary">This can make us found by more people</string>
  <string name="settings_get_updates">Get Updates</string>
  <string name="settings_get_updates_summary">Get the latest version that is more stable and packed with features</string>
  <string name="settings_get_updates_in_app">In-app</string>
  <string name="settings_get_updates_in_app_chip_stable">Stable</string>
  <string name="settings_get_updates_in_app_chip_ci">CI</string>
  <string name="about_info">This app is used to view the third-party libraries used by applications in your device.</string>
  <string name="toolbar_rate">Rate</string>
  <string name="resource_declaration">Part of the resources in the application comes from: </string>
  <string name="library_declaration">All marked libraries information in LibChecker comes from the development documentation or code repository of the SDK to which it belongs. If the information is incorrect, please contact: <EMAIL></string>

  <!--  Settings  -->
  <string name="pref_group_normal">Normal</string>
  <string name="pref_group_others">Others</string>

  <string name="apk_analytics">APK Analytics</string>
  <string name="colorful_icon">Colorful Icon</string>
  <string name="rules_repo_title">Rules Repo</string>
  <string name="lib_ref_threshold">Library Reference Threshold</string>
  <string name="languages">Languages</string>
  <string name="reload_apps">Reload Apps List</string>
  <string name="help_docs">Help Docs</string>
  <string name="join_telegram_group">Communication</string>
  <string name="anonymous_statistics">Anonymous Statistics</string>
  <string name="cloud_rules">Cloud Rules</string>
  <string name="dark_mode">Dark Mode</string>
  <string name="snapshot_keep">Snapshot Default Keep Rule</string>
  <string name="export_log">Export Log</string>

  <string name="apk_analytics_summary">Open the detail page when click an APK file</string>
  <string name="colorful_icon_summary">Part of the marked libraries will appear as a colored logo</string>
  <string name="lib_ref_threshold_summary">Only libraries with the number of library references reaching the threshold are displayed in the list</string>
  <string name="reload_apps_summary">Have a try when apps list is shown abnormally</string>
  <string name="export_log_summary">Export the log file of the app</string>
  <string name="help_docs_summary">Learn how to use LibChecker</string>
  <string name="join_telegram_group_summary">Join our Telegram group</string>
  <string name="anonymous_statistics_summary">We use Google Firebase to anonymously send the most commonly used marked libraries and some usage habits data to make LibChecker more practical</string>
  <string name="cloud_rules_summary">Update the latest marked library rules here</string>

  <string name="array_dark_mode_off">Off</string>
  <string name="array_dark_mode_on">On</string>
  <string name="array_dark_mode_auto">Auto</string>
  <string name="array_dark_mode_system">System Default</string>
  <string name="array_dark_mode_battery">Set by Battery Saver</string>

  <string name="array_snapshot_default">Notify</string>
  <string name="array_snapshot_keep">Keep</string>
  <string name="array_snapshot_discard">Discard</string>

  <!--  Detail  -->
  <string name="detail">Details</string>
  <string name="detail_label">View App Details</string>
  <string name="not_found">Not found</string>
  <string name="create_an_issue">Assist us to supplement the information</string>
  <string name="app_bundle_details">The Android App Bundle is Android\'s new, official publishing format that offers a more efficient way to build and release your app. The Android App Bundle lets you more easily deliver a great experience in a smaller app size, which can improve install success and reduce uninstalls. It\'s easy to switch. You don\'t need to refactor your code to start benefiting from a smaller app. And once you\'ve switched, you\'ll benefit from modular app development and customizable feature delivery.</string>
  <string name="kotlin_details">Kotlin is an open-source statically typed programming language that targets the JVM, Android, JavaScript, Wasm, and Native. It\'s developed by JetBrains. Kotlin is a modern but already mature programming language designed to make developers happier. It\'s concise, safe, interoperable with Java and other languages, and provides many ways to reuse code between multiple platforms for productive programming.</string>
  <string name="items_count">Items count: </string>
  <string name="further_operation">Further Operation</string>
  <string name="app_info_launch">Launch</string>
  <string name="app_info_settings">Settings</string>
  <string name="lib_detail_dialog_title">Library Details</string>
  <string name="lib_permission_dialog_title">Permission Detail</string>
  <string name="agp_details">The Android Studio build system is based on Gradle, and the Android Gradle plugin adds several features that are specific to building Android apps. Although the Android plugin is typically updated in lock-step with Android Studio, the plugin (and the rest of the Gradle system) can run independent of Android Studio and be updated separately.</string>
  <string name="xposed_module">Xposed Module</string>
  <string name="xposed_module_details">Xposed is a framework for modules that can change the behavior of the system and apps without touching any APKs. That\'s great because it means that modules can work for different versions and even ROMs without any changes.</string>
  <string name="play_app_signing">Play App Signing</string>
  <string name="play_app_signing_details">With Play App Signing, Google manages and protects your app\'s signing key for you and uses it to sign optimized, distribution APKs that are generated from your app bundles. Play App Signing stores your app signing key on Google’s secure infrastructure and offers upgrade options to increase security.</string>
  <string name="pwa_details">Progressive Web Apps (PWA) are built and enhanced with modern APIs to deliver enhanced capabilities, reliability, and installability while reaching anyone, anywhere, on any device with a single codebase.</string>
  <string name="jetpack_compose_details">Jetpack Compose is Android’s modern toolkit for building native UI. It simplifies and accelerates UI development on Android. Quickly bring your app to life with less code, powerful tools, and intuitive Kotlin APIs.</string>
  <string name="jetbrain_compose_multiplatform_details">Compose Multiplatform is a modern declarative and reactive UI framework developed by JetBrains that provides a simple way to build user interfaces with a small amount of Kotlin code. It also allows you to write your UI once and run it on any of the supported platforms – iOS, Android, desktop (Windows, macOS, Linux), and web.</string>
  <string name="multi_arch_dialog_details"><![CDATA[If an app exposes an API to other apps that can be either 32 bit or 64 bit, the app must have the <b>android:multiarch</b> property set to true within its manifest to avoid potential errors.]]></string>
  <string name="extract_native_libs_tip">The app declares </string>
  <string name="xml_detail">XML Detail</string>
  <string name="lib_detail_dialog_title_16kb_page_size">16 KB Page Size Support</string>
  <string name="lib_detail_dialog_content_16kb_page_size">Historically, Android has only supported 4 KB memory page sizes, which has optimized system memory performance for the average amount of total memory that Android devices have typically had. Beginning with Android 15, AOSP supports devices that are configured to use a page size of 16 KB (16 KB devices). If your app uses any NDK libraries, either directly or indirectly through an SDK, then you will need to rebuild your app for it to work on these 16 KB devices.</string>
  <string name="lib_detail_dialog_title_16kb_page_size_compat">16 KB Backcompat</string>
  <string name="lib_detail_dialog_content_16kb_page_size_compat"><![CDATA[Android 15 introduced support for 16 KB memory pages to optimize performance of the platform. Android 16 adds a compatibility mode, allowing some apps built for 4 KB memory pages to run on a device configured for 16 KB memory pages.\n\nIf Android detects that your app has 4 KB aligned memory pages, it automatically uses compatibility mode and display a notification dialog to the user. Setting the <b>android:pageSizeCompat</b> property in the <b>AndroidManifest.xml</b> to enable the backwards compatibility mode will prevent the display of the dialog when your app launches. For best performance, reliability, and stability, your app should still be 16 KB aligned.]]></string>
  <string name="format_last_updated">Updated at: %s</string>
  <string name="menu_process">Process</string>
  <string name="menu_split">Split</string>
  <string name="alternative_launch_method">Alternative Launch Method</string>
  <string name="compare_with_current">Compare with current package</string>
  <string name="rx_detail">RxJava is a Java VM implementation of Reactive Extensions: a library for composing asynchronous and event-based programs by using observable sequences.</string>
  <string name="rx_android_detail">Reactive Extensions for Android</string>
  <string name="rx_kotlin_detail">Kotlin Extensions for RxJava</string>
  <string name="permission_not_granted">Not Granted</string>
  <string name="signature_detail">Signature Detail</string>
  <string name="signature_scheme_version">Signature Scheme</string>
  <string name="signature_version">Version</string>
  <string name="signature_serial_number">Serial Number</string>
  <string name="signature_issuer">Issuer</string>
  <string name="signature_subject">Subject</string>
  <string name="signature_validity_not_before">Validity Not Before</string>
  <string name="signature_validity_not_after">Validity Not After</string>
  <string name="signature_public_key_format">Public key Format</string>
  <string name="signature_public_key_algorithm">Public key Algorithm</string>
  <string name="signature_public_key_exponent">Public key Exponent</string>
  <string name="signature_public_key_modulus_size">Public key Modulus Size</string>
  <string name="signature_public_key_modulus">Public key Modulus</string>
  <string name="signature_public_key_y">Public key Value Y</string>
  <string name="signature_public_key_type">Public key Type</string>
  <string name="signature_algorithm_name">Signature Algorithm Name</string>
  <string name="signature_algorithm_oid">Signature Algorithm OID</string>
  <string name="lib_detail_label_tip">Label</string>
  <string name="lib_detail_develop_team_tip">Dev Team</string>
  <string name="lib_detail_rule_contributors_tip">Rule Contributor(s)</string>
  <string name="lib_detail_description_tip">Description</string>
  <string name="lib_detail_relative_link_tip">Relative Link</string>
  <string name="lib_detail_last_update_tip">Updated at</string>
  <string name="lib_detail_app_props_title">Application Properties</string>
  <string name="lib_detail_app_props_tip">More information</string>
  <string name="lib_detail_xposed_min_version">Min Version</string>
  <string name="lib_detail_xposed_default_scope">Default Scope</string>
  <string name="lib_detail_xposed_init_class">Init Class</string>
  <string name="lib_detail_app_install_source_title">Installation Source</string>
  <string name="lib_detail_app_install_source_originating_package">Installation requester</string>
  <string name="lib_detail_app_install_source_installing_package">Installation Executor</string>
  <string name="lib_detail_app_installed_time">Installation Time</string>
  <string name="lib_detail_app_first_installed_time">First installed time: </string>
  <string name="lib_detail_app_last_updated_time">Last installed time: </string>
  <string name="lib_detail_app_install_source_empty">Unknown</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell or uninstalled app</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Because Android restricts the API to get the installation requester, we use Shizuku to get the installation requester.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku not installed</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">Click here to install Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Requires Shizuku API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">Click here to update Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku not running</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">Click here to jump to Shizuku to start it</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku not authorized</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">Click here for Shizuku authorization for installation requester</string>
  <string name="lib_detail_elf_info">More Info</string>
  <string name="lib_detail_dependency_tip">Dependencies</string>
  <string name="lib_detail_entry_points_tip">Entry Points</string>

  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Copied to clipboard</string>
  <string name="toast_not_existing_market">There\'s not existing any app market</string>
  <string name="toast_use_another_file_manager">Please use another File Manager to open the APK</string>
  <string name="toast_cant_open_app">Can\'t open this app</string>
  <string name="toast_cloud_rules_update_error">Failed to update cloud rules. Please try again.</string>
  <string name="toast_not_enough_storage_space">Not enough storage space</string>
  <string name="toast_downloading_app">Download requested</string>

  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Save current snapshot</string>
  <string name="snapshot_current_timestamp">Current Snapshot Timestamp</string>
  <string name="snapshot_apps_count">Count of Apps in Snapshot / Count of Apps</string>
  <string name="comparison_snapshot_apps_count">Count of Apps in Snapshot</string>
  <string name="snapshot_indicator_added">Added</string>
  <string name="snapshot_indicator_removed">Removed</string>
  <string name="snapshot_indicator_changed">Changed</string>
  <string name="snapshot_indicator_moved">Moved</string>
  <string name="snapshot_empty_list_title">No Components Changes</string>
  <string name="snapshot_no_snapshot">No Snapshot</string>
  <string name="snapshot_detail_new_install_title">This App has newly installed</string>
  <string name="snapshot_detail_deleted_title">This App has been removed</string>
  <string name="snapshot_time_node_uninitialized">(Uninitialized)</string>
  <string name="snapshot_preinstalled_app">Preinstalled app</string>
  <string name="snapshot_generate_text_report">Generate Text Report</string>
  <string name="snapshot_scheme_tip"><![CDATA[Tip: You can now generate snapshots in the background via:<br><b>%s</b>]]></string>
  <string name="snapshot_build_id">Build ID</string>
  <string name="snapshot_build_security_patch">Security Patch</string>

  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Are you sure to reload all apps?</string>
  <string name="dialog_subtitle_reload_apps">This may take a few seconds</string>
  <string name="dialog_title_change_timestamp">Change Time Node</string>
  <string name="dialog_title_keep_previous_snapshot">Attention</string>
  <string name="dialog_message_keep_previous_snapshot">Do you want to keep the previous snapshot?</string>
  <string name="dialog_title_select_to_delete">Choose a Date to Delete Snapshot</string>
  <string name="dialog_title_confirm_to_delete">Are you sure to delete this snapshot?</string>
  <string name="btn_keep">Keep</string>
  <string name="btn_drop">Discard</string>
  <string name="dialog_title_compare_diff_apk">Attention</string>
  <string name="dialog_message_compare_diff_apk">Do you want to compare two different APKs?</string>

  <!--  Album  -->
  <string name="album_compare">Compare</string>
  <string name="album_item_comparison_title">Comparison</string>
  <string name="album_item_comparison_subtitle">Compare with two snapshots</string>
  <string name="album_item_comparison_invalid_compare">Invalid comparison</string>
  <string name="album_item_comparison_invalid_shared_items">Please select two APK files for comparison</string>
  <string name="album_item_comparison_choose_local_apk">Choose Local APK</string>
  <string name="album_item_management_title">Management</string>
  <string name="album_item_management_subtitle">Manage all snapshots</string>
  <string name="album_item_management_snapshot_auto_remove_default_title">Keep Only the Most Recent Snapshots</string>
  <string name="album_item_management_snapshot_auto_remove_specific_title">Keep Only the %d Most Recent Items</string>
  <string name="album_item_management_snapshot_auto_remove_desc">Starting from tapping \"%s\", only the most recent snapshots will be retained, and the older snapshots will be automatically deleted.</string>
  <string name="album_item_backup_restore_title">Backup &amp; Restore</string>
  <string name="album_item_backup_restore_subtitle">Backup and restore snapshots</string>
  <string name="album_item_track_title">Track</string>
  <string name="album_item_track_subtitle">Select applications to force a comparison change</string>
  <string name="album_backup">Backup</string>
  <string name="album_backup_summary">Backup all snapshots</string>
  <string name="album_restore">Restore</string>
  <string name="album_restore_summary">Restore snapshots from backup file</string>
  <string name="album_restore_detail">%1$s : %2$s items in total\n</string>
  <string name="album_click_to_choose">Click to Choose</string>
  <string name="album_dialog_delete_snapshot_message">Processing…</string>
  <string name="album_snapshot_top_apps_not_initialized">Not loaded yet</string>

  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Block it via \"MonkeyKing Purify\"</string>
  <string name="integration_monkey_king_menu_unblock">Unblock it via \"MonkeyKing Purify\"</string>
  <string name="integration_blocker_menu_block">Block it via \"Blocker\"</string>
  <string name="integration_blocker_menu_unblock">Unblock it via \"Blocker\"</string>
  <string name="integration_anywhere_menu_editor">Open it in \"Anywhere- Editor\"</string>

  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Local repo version</string>
  <string name="rules_remote_repo_version">Remote repo version</string>
  <string name="rules_btn_restart_to_update">Restart to Update</string>
  <string name="rules_btn_update">Update</string>

  <!-- Advanced menu -->
  <string name="adv_show_system_apps">Show System Apps</string>
  <string name="adv_show_overlays">Show Overlays</string>
  <string name="adv_show_64_bit">Show 64-bit Apps</string>
  <string name="adv_show_32_bit">Show 32-bit Apps</string>
  <string name="adv_sort_mode">Sort Mode</string>
  <string name="adv_sort_by_time">Updated Time</string>
  <string name="adv_sort_by_target_version">Target Version</string>
  <string name="adv_sort_by_name">Name</string>
  <string name="adv_show_android_version">Show Android Version</string>
  <string name="adv_show_target_version">Show Target Version</string>
  <string name="adv_show_min_version">Show Min Version</string>
  <string name="adv_show_compile_version">Show Compile Version</string>
  <string name="adv_tint_abi_label">Tint ABI Label</string>
  <string name="adv_mark_exported">Mark Exported Component</string>
  <string name="adv_mark_disabled">Mark Disabled Component</string>
  <string name="adv_show_marked_lib">Show Marked Libraries</string>
  <string name="adv_show_system_framework_apps">Show Framework Apps</string>

  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Show Update Time</string>
  <string name="snapshot_menu_hide_no_component_changes">Hide No Component Changes</string>
  <string name="snapshot_menu_diff_highlight">Diff Highlight</string>
  <string name="snapshot_menu_use_iec_units">Use IEC Units</string>
</resources>
