<resources>

  <!-- Base application theme. -->
  <style name="AppTheme" parent="Theme" />

  <style name="App.LibChip.Material3" parent="Widget.Material3.Chip.Assist.Elevated">
    <item name="chipIconTint">@null</item>
  </style>

  <style name="CustomBottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.BottomSheetDialog">
    <item name="bottomSheetStyle">@style/CustomBottomSheet</item>
  </style>

  <style name="CustomBottomSheetDialog.Md3" parent="@style/ThemeOverlay.Material3.BottomSheetDialog">
    <item name="bottomSheetStyle">@style/CustomBottomSheet.Md3</item>
  </style>

  <style name="CustomBottomSheet" parent="Widget.MaterialComponents.BottomSheet">
    <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
  </style>

  <style name="CustomBottomSheet.Md3" parent="Widget.Material3.BottomSheet">
    <item name="shapeAppearanceOverlay">@style/CustomShapeAppearanceBottomSheetDialog</item>
  </style>

  <style name="CustomShapeAppearanceBottomSheetDialog" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSizeTopRight">16dp</item>
    <item name="cornerSizeTopLeft">16dp</item>
    <item name="cornerSizeBottomRight">0dp</item>
    <item name="cornerSizeBottomLeft">0dp</item>
  </style>

  <style name="GeneralProgressIndicator" parent="Widget.MaterialComponents.LinearProgressIndicator">
    <item name="android:width">200dp</item>
    <item name="trackCornerRadius">3dp</item>
  </style>

  <style name="TextView.SansSerif" parent="Widget.MaterialComponents.TextView">
    <item name="fontFamily">sans-serif</item>
  </style>

  <style name="TextView.SansSerifBlack" parent="Widget.MaterialComponents.TextView">
    <item name="fontFamily">sans-serif-black</item>
  </style>

  <style name="TextView.SansSerifMedium" parent="Widget.MaterialComponents.TextView">
    <item name="fontFamily">sans-serif-medium</item>
  </style>

  <style name="TextView.SansSerifCondensed" parent="Widget.MaterialComponents.TextView">
    <item name="fontFamily">sans-serif-condensed</item>
  </style>

  <style name="TextView.SansSerifCondensedMedium" parent="Widget.MaterialComponents.TextView">
    <item name="fontFamily">sans-serif-condensed-medium</item>
  </style>

  <style name="TextView.MonoSpace" parent="Widget.MaterialComponents.TextView">
    <item name="fontFamily">monospace</item>
  </style>

  <style name="AppListMaterialCard" parent="Widget.MaterialComponents.CardView">
    <item name="cardCornerRadius">3dp</item>
    <item name="cardElevation">1dp</item>
  </style>

  <style name="AlbumMaterialCard" parent="Widget.Material3.CardView.Outlined">
    <item name="cardCornerRadius">8dp</item>
  </style>

  <style name="App.Material3.CardView" parent="Widget.Material3.CardView.Outlined">
    <item name="android:stateListAnimator">@null</item>
    <item name="android:background">@null</item>
    <item name="android:foreground">@null</item>
    <item name="cardPreventCornerOverlap">false</item>
    <item name="strokeColor">@android:color/transparent</item>
  </style>

  <style name="Widget.LC.PopupMenu" parent="Widget.AppCompat.ListView.DropDown">
    <item name="android:paddingEnd">-16dp</item>
    <item name="android:clipToPadding">false</item>
  </style>

  <style name="Transparent" parent="@style/AppTheme">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="windowNoTitle">true</item>
    <item name="android:windowIsTranslucent">true</item>
  </style>

  <style name="Widget.CheckableChipView" parent="">
    <item name="android:padding">8dp</item>

    <item name="android:textColor">@color/advanced_menu_item_text_not_checked</item>
    <item name="android:textSize">14sp</item>

    <item name="ccv_outlineWidth">1dp</item>
    <item name="ccv_outlineColor">@color/advanced_menu_item_overlay</item>

    <item name="android:color">@color/advanced_menu_item_overlay</item>

    <item name="ccv_clearIcon">@drawable/ic_check</item>
    <item name="ccv_foreground">?android:selectableItemBackground</item>
  </style>

  <style name="App.Widget.AdvancedMenuToggle" parent="Base.AppTheme">
    <item name="materialButtonStyle">@style/Widget.Material3.Button.OutlinedButton</item>
    <item name="colorSecondaryContainer">@color/advanced_menu_item_overlay</item>
    <item name="colorOnSecondaryContainer">@color/advanced_menu_item_text_checked</item>
    <item name="colorOutline">@color/advanced_menu_item_overlay</item>
    <item name="colorOnSurface">@color/advanced_menu_item_text_not_checked</item>
  </style>
</resources>
