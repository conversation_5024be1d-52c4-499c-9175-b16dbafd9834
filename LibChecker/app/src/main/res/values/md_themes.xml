<?xml version="1.0" encoding="utf-8"?>
<resources>
  <color name="md_theme_libchecker_light_primary">#FF6D23F8</color>
  <color name="md_theme_libchecker_light_onPrimary">#FFFFFFFF</color>
  <color name="md_theme_libchecker_light_primaryContainer">#FFEADDFF</color>
  <color name="md_theme_libchecker_light_onPrimaryContainer">#FF21005E</color>
  <color name="md_theme_libchecker_light_secondary">#FF615B70</color>
  <color name="md_theme_libchecker_light_onSecondary">#FFFFFFFF</color>
  <color name="md_theme_libchecker_light_secondaryContainer">#FFE8DFF8</color>
  <color name="md_theme_libchecker_light_onSecondaryContainer">#FF1E192B</color>
  <color name="md_theme_libchecker_light_tertiary">#FF7D5260</color>
  <color name="md_theme_libchecker_light_onTertiary">#FFFFFFFF</color>
  <color name="md_theme_libchecker_light_tertiaryContainer">#FFFFD8E4</color>
  <color name="md_theme_libchecker_light_onTertiaryContainer">#FF31101D</color>
  <color name="md_theme_libchecker_light_error">#FFBA1B1B</color>
  <color name="md_theme_libchecker_light_onError">#FFFFFFFF</color>
  <color name="md_theme_libchecker_light_errorContainer">#FFFFDAD4</color>
  <color name="md_theme_libchecker_light_onErrorContainer">#FF410001</color>
  <color name="md_theme_libchecker_light_background">#FFFFFBFE</color>
  <color name="md_theme_libchecker_light_onBackground">#FF1C1B1E</color>
  <color name="md_theme_libchecker_light_surface">#FFFFFBFE</color>
  <color name="md_theme_libchecker_light_onSurface">#FF1C1B1E</color>
  <color name="md_theme_libchecker_light_surfaceVariant">#FFE6E1E6</color>
  <color name="md_theme_libchecker_light_onSurfaceVariant">#FF484649</color>
  <color name="md_theme_libchecker_light_outline">#FF79767A</color>
  <color name="md_theme_libchecker_light_inverseOnSurface">#FFF4EFF3</color>
  <color name="md_theme_libchecker_light_inverseSurface">#FF313033</color>
  <color name="md_theme_libchecker_light_primaryInverse">#FFD0BCFF</color>
  <color name="md_theme_libchecker_dark_primary">#FFD0BCFF</color>
  <color name="md_theme_libchecker_dark_onPrimary">#FF380094</color>
  <color name="md_theme_libchecker_dark_primaryContainer">#FF5200CE</color>
  <color name="md_theme_libchecker_dark_onPrimaryContainer">#FFEADDFF</color>
  <color name="md_theme_libchecker_dark_secondary">#FFCBC2DC</color>
  <color name="md_theme_libchecker_dark_onSecondary">#FF332D41</color>
  <color name="md_theme_libchecker_dark_secondaryContainer">#FF4A4458</color>
  <color name="md_theme_libchecker_dark_onSecondaryContainer">#FFE8DFF8</color>
  <color name="md_theme_libchecker_dark_tertiary">#FFEFB7C8</color>
  <color name="md_theme_libchecker_dark_onTertiary">#FF492532</color>
  <color name="md_theme_libchecker_dark_tertiaryContainer">#FF633B48</color>
  <color name="md_theme_libchecker_dark_onTertiaryContainer">#FFFFD8E4</color>
  <color name="md_theme_libchecker_dark_error">#FFFFB4A9</color>
  <color name="md_theme_libchecker_dark_onError">#FF680003</color>
  <color name="md_theme_libchecker_dark_errorContainer">#FF930006</color>
  <color name="md_theme_libchecker_dark_onErrorContainer">#FFFFDAD4</color>
  <color name="md_theme_libchecker_dark_background">#FF1C1B1E</color>
  <color name="md_theme_libchecker_dark_onBackground">#FFE6E1E6</color>
  <color name="md_theme_libchecker_dark_surface">#FF1C1B1E</color>
  <color name="md_theme_libchecker_dark_onSurface">#FFCAC5CA</color>
  <color name="md_theme_libchecker_dark_surfaceVariant">#FF484649</color>
  <color name="md_theme_libchecker_dark_onSurfaceVariant">#FFCAC5CA</color>
  <color name="md_theme_libchecker_dark_outline">#FF938F94</color>
  <color name="md_theme_libchecker_dark_inverseOnSurface">#FF605D62</color>
  <color name="md_theme_libchecker_dark_inverseSurface">#FFE6E1E6</color>
  <color name="md_theme_libchecker_dark_primaryInverse">#FF6D23F8</color>
  <color name="md_theme_libchecker_palette_primary_0">#FF000000</color>
  <color name="md_theme_libchecker_palette_primary_10">#FF21005E</color>
  <color name="md_theme_libchecker_palette_primary_20">#FF380094</color>
  <color name="md_theme_libchecker_palette_primary_30">#FF5200CE</color>
  <color name="md_theme_libchecker_palette_primary_40">#FF6D23F8</color>
  <color name="md_theme_libchecker_palette_primary_50">#FF8752FF</color>
  <color name="md_theme_libchecker_palette_primary_60">#FF9F79FF</color>
  <color name="md_theme_libchecker_palette_primary_70">#FFB79BFF</color>
  <color name="md_theme_libchecker_palette_primary_80">#FFD0BCFF</color>
  <color name="md_theme_libchecker_palette_primary_90">#FFEADDFF</color>
  <color name="md_theme_libchecker_palette_primary_95">#FFF6EEFF</color>
  <color name="md_theme_libchecker_palette_primary_99">#FFFFFBFE</color>
  <color name="md_theme_libchecker_palette_primary_100">#FFFFFFFF</color>
  <color name="md_theme_libchecker_palette_secondary_0">#FF000000</color>
  <color name="md_theme_libchecker_palette_secondary_10">#FF1E192B</color>
  <color name="md_theme_libchecker_palette_secondary_20">#FF332D41</color>
  <color name="md_theme_libchecker_palette_secondary_30">#FF4A4458</color>
  <color name="md_theme_libchecker_palette_secondary_40">#FF615B70</color>
  <color name="md_theme_libchecker_palette_secondary_50">#FF7B748A</color>
  <color name="md_theme_libchecker_palette_secondary_60">#FF958DA5</color>
  <color name="md_theme_libchecker_palette_secondary_70">#FFAFA7BF</color>
  <color name="md_theme_libchecker_palette_secondary_80">#FFCBC2DC</color>
  <color name="md_theme_libchecker_palette_secondary_90">#FFE8DFF8</color>
  <color name="md_theme_libchecker_palette_secondary_95">#FFF6EEFF</color>
  <color name="md_theme_libchecker_palette_secondary_99">#FFFFFBFE</color>
  <color name="md_theme_libchecker_palette_secondary_100">#FFFFFFFF</color>
  <color name="md_theme_libchecker_palette_tertiary_0">#FF000000</color>
  <color name="md_theme_libchecker_palette_tertiary_10">#FF31101D</color>
  <color name="md_theme_libchecker_palette_tertiary_20">#FF492532</color>
  <color name="md_theme_libchecker_palette_tertiary_30">#FF633B48</color>
  <color name="md_theme_libchecker_palette_tertiary_40">#FF7D5260</color>
  <color name="md_theme_libchecker_palette_tertiary_50">#FF996A79</color>
  <color name="md_theme_libchecker_palette_tertiary_60">#FFB48392</color>
  <color name="md_theme_libchecker_palette_tertiary_70">#FFD29DAD</color>
  <color name="md_theme_libchecker_palette_tertiary_80">#FFEFB7C8</color>
  <color name="md_theme_libchecker_palette_tertiary_90">#FFFFD8E4</color>
  <color name="md_theme_libchecker_palette_tertiary_95">#FFFFECF1</color>
  <color name="md_theme_libchecker_palette_tertiary_99">#FFFCFCFC</color>
  <color name="md_theme_libchecker_palette_tertiary_100">#FFFFFFFF</color>
  <color name="md_theme_libchecker_palette_error_0">#FF000000</color>
  <color name="md_theme_libchecker_palette_error_10">#FF410001</color>
  <color name="md_theme_libchecker_palette_error_20">#FF680003</color>
  <color name="md_theme_libchecker_palette_error_30">#FF930006</color>
  <color name="md_theme_libchecker_palette_error_40">#FFBA1B1B</color>
  <color name="md_theme_libchecker_palette_error_50">#FFDD3730</color>
  <color name="md_theme_libchecker_palette_error_60">#FFFF5449</color>
  <color name="md_theme_libchecker_palette_error_70">#FFFF897A</color>
  <color name="md_theme_libchecker_palette_error_80">#FFFFB4A9</color>
  <color name="md_theme_libchecker_palette_error_90">#FFFFDAD4</color>
  <color name="md_theme_libchecker_palette_error_95">#FFFFEDE9</color>
  <color name="md_theme_libchecker_palette_error_99">#FFFCFCFC</color>
  <color name="md_theme_libchecker_palette_error_100">#FFFFFFFF</color>
  <color name="md_theme_libchecker_palette_neutral_0">#FF000000</color>
  <color name="md_theme_libchecker_palette_neutral_10">#FF1C1B1E</color>
  <color name="md_theme_libchecker_palette_neutral_20">#FF313033</color>
  <color name="md_theme_libchecker_palette_neutral_30">#FF484649</color>
  <color name="md_theme_libchecker_palette_neutral_40">#FF605D62</color>
  <color name="md_theme_libchecker_palette_neutral_50">#FF79767A</color>
  <color name="md_theme_libchecker_palette_neutral_60">#FF938F94</color>
  <color name="md_theme_libchecker_palette_neutral_70">#FFAEAAAE</color>
  <color name="md_theme_libchecker_palette_neutral_80">#FFCAC5CA</color>
  <color name="md_theme_libchecker_palette_neutral_90">#FFE6E1E6</color>
  <color name="md_theme_libchecker_palette_neutral_95">#FFF4EFF3</color>
  <color name="md_theme_libchecker_palette_neutral_99">#FFFFFBFE</color>
  <color name="md_theme_libchecker_palette_neutral_100">#FFFFFFFF</color>
  <color name="md_theme_libchecker_palette_neutral_variant_0">#FF000000</color>
  <color name="md_theme_libchecker_palette_neutral_variant_10">#FF1C1B1E</color>
  <color name="md_theme_libchecker_palette_neutral_variant_20">#FF313033</color>
  <color name="md_theme_libchecker_palette_neutral_variant_30">#FF484649</color>
  <color name="md_theme_libchecker_palette_neutral_variant_40">#FF605D62</color>
  <color name="md_theme_libchecker_palette_neutral_variant_50">#FF79767A</color>
  <color name="md_theme_libchecker_palette_neutral_variant_60">#FF938F94</color>
  <color name="md_theme_libchecker_palette_neutral_variant_70">#FFAEAAAE</color>
  <color name="md_theme_libchecker_palette_neutral_variant_80">#FFCAC5CA</color>
  <color name="md_theme_libchecker_palette_neutral_variant_90">#FFE6E1E6</color>
  <color name="md_theme_libchecker_palette_neutral_variant_95">#FFF4EFF3</color>
  <color name="md_theme_libchecker_palette_neutral_variant_99">#FFFFFBFE</color>
  <color name="md_theme_libchecker_palette_neutral_variant_100">#FFFFFFFF</color>
  <style name="Theme.Material3.Light.LibChecker" parent="Theme.Material3.Light.Rikka">
    <item name="colorPrimary">@color/md_theme_libchecker_light_primary</item>
    <item name="colorOnPrimary">@color/md_theme_libchecker_light_onPrimary</item>
    <item name="colorPrimaryContainer">@color/md_theme_libchecker_light_primaryContainer</item>
    <item name="colorOnPrimaryContainer">@color/md_theme_libchecker_light_onPrimaryContainer</item>
    <item name="colorSecondary">@color/md_theme_libchecker_light_secondary</item>
    <item name="colorOnSecondary">@color/md_theme_libchecker_light_onSecondary</item>
    <item name="colorSecondaryContainer">@color/md_theme_libchecker_light_secondaryContainer</item>
    <item name="colorOnSecondaryContainer">@color/md_theme_libchecker_light_onSecondaryContainer</item>
    <item name="colorTertiary">@color/md_theme_libchecker_light_tertiary</item>
    <item name="colorOnTertiary">@color/md_theme_libchecker_light_onTertiary</item>
    <item name="colorTertiaryContainer">@color/md_theme_libchecker_light_tertiaryContainer</item>
    <item name="colorOnTertiaryContainer">@color/md_theme_libchecker_light_onTertiaryContainer</item>
    <item name="colorError">@color/md_theme_libchecker_light_error</item>
    <item name="colorErrorContainer">@color/md_theme_libchecker_light_errorContainer</item>
    <item name="colorOnError">@color/md_theme_libchecker_light_onError</item>
    <item name="colorOnErrorContainer">@color/md_theme_libchecker_light_onErrorContainer</item>
    <item name="android:colorBackground">@color/md_theme_libchecker_light_surface</item>
    <item name="colorOnBackground">@color/md_theme_libchecker_light_onBackground</item>
    <item name="colorSurface">@color/md_theme_libchecker_light_surface</item>
    <item name="colorOnSurface">@color/md_theme_libchecker_light_onSurface</item>
    <item name="colorSurfaceVariant">@color/md_theme_libchecker_light_surfaceVariant</item>
    <item name="colorOnSurfaceVariant">@color/md_theme_libchecker_light_onSurfaceVariant</item>
    <item name="colorOutline">@color/md_theme_libchecker_light_outline</item>
    <item name="colorOnSurfaceInverse">@color/md_theme_libchecker_light_inverseOnSurface</item>
    <item name="colorSurfaceInverse">@color/md_theme_libchecker_light_inverseSurface</item>
    <item name="colorPrimaryInverse">@color/md_theme_libchecker_light_primaryInverse</item>
    <item name="palettePrimary0">@color/md_theme_libchecker_palette_primary_0</item>
    <item name="palettePrimary10">@color/md_theme_libchecker_palette_primary_10</item>
    <item name="palettePrimary20">@color/md_theme_libchecker_palette_primary_20</item>
    <item name="palettePrimary30">@color/md_theme_libchecker_palette_primary_30</item>
    <item name="palettePrimary40">@color/md_theme_libchecker_palette_primary_40</item>
    <item name="palettePrimary50">@color/md_theme_libchecker_palette_primary_50</item>
    <item name="palettePrimary60">@color/md_theme_libchecker_palette_primary_60</item>
    <item name="palettePrimary70">@color/md_theme_libchecker_palette_primary_70</item>
    <item name="palettePrimary80">@color/md_theme_libchecker_palette_primary_80</item>
    <item name="palettePrimary90">@color/md_theme_libchecker_palette_primary_90</item>
    <item name="palettePrimary95">@color/md_theme_libchecker_palette_primary_95</item>
    <item name="palettePrimary99">@color/md_theme_libchecker_palette_primary_99</item>
    <item name="palettePrimary100">@color/md_theme_libchecker_palette_primary_100</item>
    <item name="paletteSecondary0">@color/md_theme_libchecker_palette_secondary_0</item>
    <item name="paletteSecondary10">@color/md_theme_libchecker_palette_secondary_10</item>
    <item name="paletteSecondary20">@color/md_theme_libchecker_palette_secondary_20</item>
    <item name="paletteSecondary30">@color/md_theme_libchecker_palette_secondary_30</item>
    <item name="paletteSecondary40">@color/md_theme_libchecker_palette_secondary_40</item>
    <item name="paletteSecondary50">@color/md_theme_libchecker_palette_secondary_50</item>
    <item name="paletteSecondary60">@color/md_theme_libchecker_palette_secondary_60</item>
    <item name="paletteSecondary70">@color/md_theme_libchecker_palette_secondary_70</item>
    <item name="paletteSecondary80">@color/md_theme_libchecker_palette_secondary_80</item>
    <item name="paletteSecondary90">@color/md_theme_libchecker_palette_secondary_90</item>
    <item name="paletteSecondary95">@color/md_theme_libchecker_palette_secondary_95</item>
    <item name="paletteSecondary99">@color/md_theme_libchecker_palette_secondary_99</item>
    <item name="paletteSecondary100">@color/md_theme_libchecker_palette_secondary_100</item>
    <item name="paletteTertiary0">@color/md_theme_libchecker_palette_tertiary_0</item>
    <item name="paletteTertiary10">@color/md_theme_libchecker_palette_tertiary_10</item>
    <item name="paletteTertiary20">@color/md_theme_libchecker_palette_tertiary_20</item>
    <item name="paletteTertiary30">@color/md_theme_libchecker_palette_tertiary_30</item>
    <item name="paletteTertiary40">@color/md_theme_libchecker_palette_tertiary_40</item>
    <item name="paletteTertiary50">@color/md_theme_libchecker_palette_tertiary_50</item>
    <item name="paletteTertiary60">@color/md_theme_libchecker_palette_tertiary_60</item>
    <item name="paletteTertiary70">@color/md_theme_libchecker_palette_tertiary_70</item>
    <item name="paletteTertiary80">@color/md_theme_libchecker_palette_tertiary_80</item>
    <item name="paletteTertiary90">@color/md_theme_libchecker_palette_tertiary_90</item>
    <item name="paletteTertiary95">@color/md_theme_libchecker_palette_tertiary_95</item>
    <item name="paletteTertiary99">@color/md_theme_libchecker_palette_tertiary_99</item>
    <item name="paletteTertiary100">@color/md_theme_libchecker_palette_tertiary_100</item>
    <item name="paletteError0">@color/md_theme_libchecker_palette_error_0</item>
    <item name="paletteError10">@color/md_theme_libchecker_palette_error_10</item>
    <item name="paletteError20">@color/md_theme_libchecker_palette_error_20</item>
    <item name="paletteError30">@color/md_theme_libchecker_palette_error_30</item>
    <item name="paletteError40">@color/md_theme_libchecker_palette_error_40</item>
    <item name="paletteError50">@color/md_theme_libchecker_palette_error_50</item>
    <item name="paletteError60">@color/md_theme_libchecker_palette_error_60</item>
    <item name="paletteError70">@color/md_theme_libchecker_palette_error_70</item>
    <item name="paletteError80">@color/md_theme_libchecker_palette_error_80</item>
    <item name="paletteError90">@color/md_theme_libchecker_palette_error_90</item>
    <item name="paletteError95">@color/md_theme_libchecker_palette_error_95</item>
    <item name="paletteError99">@color/md_theme_libchecker_palette_error_99</item>
    <item name="paletteError100">@color/md_theme_libchecker_palette_error_100</item>
    <item name="paletteNeutral0">@color/md_theme_libchecker_palette_neutral_0</item>
    <item name="paletteNeutral10">@color/md_theme_libchecker_palette_neutral_10</item>
    <item name="paletteNeutral20">@color/md_theme_libchecker_palette_neutral_20</item>
    <item name="paletteNeutral30">@color/md_theme_libchecker_palette_neutral_30</item>
    <item name="paletteNeutral40">@color/md_theme_libchecker_palette_neutral_40</item>
    <item name="paletteNeutral50">@color/md_theme_libchecker_palette_neutral_50</item>
    <item name="paletteNeutral60">@color/md_theme_libchecker_palette_neutral_60</item>
    <item name="paletteNeutral70">@color/md_theme_libchecker_palette_neutral_70</item>
    <item name="paletteNeutral80">@color/md_theme_libchecker_palette_neutral_80</item>
    <item name="paletteNeutral90">@color/md_theme_libchecker_palette_neutral_90</item>
    <item name="paletteNeutral95">@color/md_theme_libchecker_palette_neutral_95</item>
    <item name="paletteNeutral99">@color/md_theme_libchecker_palette_neutral_99</item>
    <item name="paletteNeutral100">@color/md_theme_libchecker_palette_neutral_100</item>
    <item name="paletteNeutralVariant0">@color/md_theme_libchecker_palette_neutral_variant_0</item>
    <item name="paletteNeutralVariant10">@color/md_theme_libchecker_palette_neutral_variant_10</item>
    <item name="paletteNeutralVariant20">@color/md_theme_libchecker_palette_neutral_variant_20</item>
    <item name="paletteNeutralVariant30">@color/md_theme_libchecker_palette_neutral_variant_30</item>
    <item name="paletteNeutralVariant40">@color/md_theme_libchecker_palette_neutral_variant_40</item>
    <item name="paletteNeutralVariant50">@color/md_theme_libchecker_palette_neutral_variant_50</item>
    <item name="paletteNeutralVariant60">@color/md_theme_libchecker_palette_neutral_variant_60</item>
    <item name="paletteNeutralVariant70">@color/md_theme_libchecker_palette_neutral_variant_70</item>
    <item name="paletteNeutralVariant80">@color/md_theme_libchecker_palette_neutral_variant_80</item>
    <item name="paletteNeutralVariant90">@color/md_theme_libchecker_palette_neutral_variant_90</item>
    <item name="paletteNeutralVariant95">@color/md_theme_libchecker_palette_neutral_variant_95</item>
    <item name="paletteNeutralVariant99">@color/md_theme_libchecker_palette_neutral_variant_99</item>
    <item name="paletteNeutralVariant100">@color/md_theme_libchecker_palette_neutral_variant_100</item>
  </style>
  <style name="Theme.Material3.Dark.LibChecker" parent="Theme.Material3.Dark.Rikka">
    <item name="colorPrimary">@color/md_theme_libchecker_dark_primary</item>
    <item name="colorOnPrimary">@color/md_theme_libchecker_dark_onPrimary</item>
    <item name="colorPrimaryContainer">@color/md_theme_libchecker_dark_primaryContainer</item>
    <item name="colorOnPrimaryContainer">@color/md_theme_libchecker_dark_onPrimaryContainer</item>
    <item name="colorSecondary">@color/md_theme_libchecker_dark_secondary</item>
    <item name="colorOnSecondary">@color/md_theme_libchecker_dark_onSecondary</item>
    <item name="colorSecondaryContainer">@color/md_theme_libchecker_dark_secondaryContainer</item>
    <item name="colorOnSecondaryContainer">@color/md_theme_libchecker_dark_onSecondaryContainer</item>
    <item name="colorTertiary">@color/md_theme_libchecker_dark_tertiary</item>
    <item name="colorOnTertiary">@color/md_theme_libchecker_dark_onTertiary</item>
    <item name="colorTertiaryContainer">@color/md_theme_libchecker_dark_tertiaryContainer</item>
    <item name="colorOnTertiaryContainer">@color/md_theme_libchecker_dark_onTertiaryContainer</item>
    <item name="colorError">@color/md_theme_libchecker_dark_error</item>
    <item name="colorErrorContainer">@color/md_theme_libchecker_dark_errorContainer</item>
    <item name="colorOnError">@color/md_theme_libchecker_dark_onError</item>
    <item name="colorOnErrorContainer">@color/md_theme_libchecker_dark_onErrorContainer</item>
    <item name="android:colorBackground">@color/md_theme_libchecker_dark_surface</item>
    <item name="colorOnBackground">@color/md_theme_libchecker_dark_onBackground</item>
    <item name="colorSurface">@color/md_theme_libchecker_dark_surface</item>
    <item name="colorOnSurface">@color/md_theme_libchecker_dark_onSurface</item>
    <item name="colorSurfaceVariant">@color/md_theme_libchecker_dark_surfaceVariant</item>
    <item name="colorOnSurfaceVariant">@color/md_theme_libchecker_dark_onSurfaceVariant</item>
    <item name="colorOutline">@color/md_theme_libchecker_dark_outline</item>
    <item name="colorOnSurfaceInverse">@color/md_theme_libchecker_dark_inverseOnSurface</item>
    <item name="colorSurfaceInverse">@color/md_theme_libchecker_dark_inverseSurface</item>
    <item name="colorPrimaryInverse">@color/md_theme_libchecker_dark_primaryInverse</item>
    <item name="palettePrimary0">@color/md_theme_libchecker_palette_primary_0</item>
    <item name="palettePrimary10">@color/md_theme_libchecker_palette_primary_10</item>
    <item name="palettePrimary20">@color/md_theme_libchecker_palette_primary_20</item>
    <item name="palettePrimary30">@color/md_theme_libchecker_palette_primary_30</item>
    <item name="palettePrimary40">@color/md_theme_libchecker_palette_primary_40</item>
    <item name="palettePrimary50">@color/md_theme_libchecker_palette_primary_50</item>
    <item name="palettePrimary60">@color/md_theme_libchecker_palette_primary_60</item>
    <item name="palettePrimary70">@color/md_theme_libchecker_palette_primary_70</item>
    <item name="palettePrimary80">@color/md_theme_libchecker_palette_primary_80</item>
    <item name="palettePrimary90">@color/md_theme_libchecker_palette_primary_90</item>
    <item name="palettePrimary95">@color/md_theme_libchecker_palette_primary_95</item>
    <item name="palettePrimary99">@color/md_theme_libchecker_palette_primary_99</item>
    <item name="palettePrimary100">@color/md_theme_libchecker_palette_primary_100</item>
    <item name="paletteSecondary0">@color/md_theme_libchecker_palette_secondary_0</item>
    <item name="paletteSecondary10">@color/md_theme_libchecker_palette_secondary_10</item>
    <item name="paletteSecondary20">@color/md_theme_libchecker_palette_secondary_20</item>
    <item name="paletteSecondary30">@color/md_theme_libchecker_palette_secondary_30</item>
    <item name="paletteSecondary40">@color/md_theme_libchecker_palette_secondary_40</item>
    <item name="paletteSecondary50">@color/md_theme_libchecker_palette_secondary_50</item>
    <item name="paletteSecondary60">@color/md_theme_libchecker_palette_secondary_60</item>
    <item name="paletteSecondary70">@color/md_theme_libchecker_palette_secondary_70</item>
    <item name="paletteSecondary80">@color/md_theme_libchecker_palette_secondary_80</item>
    <item name="paletteSecondary90">@color/md_theme_libchecker_palette_secondary_90</item>
    <item name="paletteSecondary95">@color/md_theme_libchecker_palette_secondary_95</item>
    <item name="paletteSecondary99">@color/md_theme_libchecker_palette_secondary_99</item>
    <item name="paletteSecondary100">@color/md_theme_libchecker_palette_secondary_100</item>
    <item name="paletteTertiary0">@color/md_theme_libchecker_palette_tertiary_0</item>
    <item name="paletteTertiary10">@color/md_theme_libchecker_palette_tertiary_10</item>
    <item name="paletteTertiary20">@color/md_theme_libchecker_palette_tertiary_20</item>
    <item name="paletteTertiary30">@color/md_theme_libchecker_palette_tertiary_30</item>
    <item name="paletteTertiary40">@color/md_theme_libchecker_palette_tertiary_40</item>
    <item name="paletteTertiary50">@color/md_theme_libchecker_palette_tertiary_50</item>
    <item name="paletteTertiary60">@color/md_theme_libchecker_palette_tertiary_60</item>
    <item name="paletteTertiary70">@color/md_theme_libchecker_palette_tertiary_70</item>
    <item name="paletteTertiary80">@color/md_theme_libchecker_palette_tertiary_80</item>
    <item name="paletteTertiary90">@color/md_theme_libchecker_palette_tertiary_90</item>
    <item name="paletteTertiary95">@color/md_theme_libchecker_palette_tertiary_95</item>
    <item name="paletteTertiary99">@color/md_theme_libchecker_palette_tertiary_99</item>
    <item name="paletteTertiary100">@color/md_theme_libchecker_palette_tertiary_100</item>
    <item name="paletteError0">@color/md_theme_libchecker_palette_error_0</item>
    <item name="paletteError10">@color/md_theme_libchecker_palette_error_10</item>
    <item name="paletteError20">@color/md_theme_libchecker_palette_error_20</item>
    <item name="paletteError30">@color/md_theme_libchecker_palette_error_30</item>
    <item name="paletteError40">@color/md_theme_libchecker_palette_error_40</item>
    <item name="paletteError50">@color/md_theme_libchecker_palette_error_50</item>
    <item name="paletteError60">@color/md_theme_libchecker_palette_error_60</item>
    <item name="paletteError70">@color/md_theme_libchecker_palette_error_70</item>
    <item name="paletteError80">@color/md_theme_libchecker_palette_error_80</item>
    <item name="paletteError90">@color/md_theme_libchecker_palette_error_90</item>
    <item name="paletteError95">@color/md_theme_libchecker_palette_error_95</item>
    <item name="paletteError99">@color/md_theme_libchecker_palette_error_99</item>
    <item name="paletteError100">@color/md_theme_libchecker_palette_error_100</item>
    <item name="paletteNeutral0">@color/md_theme_libchecker_palette_neutral_0</item>
    <item name="paletteNeutral10">@color/md_theme_libchecker_palette_neutral_10</item>
    <item name="paletteNeutral20">@color/md_theme_libchecker_palette_neutral_20</item>
    <item name="paletteNeutral30">@color/md_theme_libchecker_palette_neutral_30</item>
    <item name="paletteNeutral40">@color/md_theme_libchecker_palette_neutral_40</item>
    <item name="paletteNeutral50">@color/md_theme_libchecker_palette_neutral_50</item>
    <item name="paletteNeutral60">@color/md_theme_libchecker_palette_neutral_60</item>
    <item name="paletteNeutral70">@color/md_theme_libchecker_palette_neutral_70</item>
    <item name="paletteNeutral80">@color/md_theme_libchecker_palette_neutral_80</item>
    <item name="paletteNeutral90">@color/md_theme_libchecker_palette_neutral_90</item>
    <item name="paletteNeutral95">@color/md_theme_libchecker_palette_neutral_95</item>
    <item name="paletteNeutral99">@color/md_theme_libchecker_palette_neutral_99</item>
    <item name="paletteNeutral100">@color/md_theme_libchecker_palette_neutral_100</item>
    <item name="paletteNeutralVariant0">@color/md_theme_libchecker_palette_neutral_variant_0</item>
    <item name="paletteNeutralVariant10">@color/md_theme_libchecker_palette_neutral_variant_10</item>
    <item name="paletteNeutralVariant20">@color/md_theme_libchecker_palette_neutral_variant_20</item>
    <item name="paletteNeutralVariant30">@color/md_theme_libchecker_palette_neutral_variant_30</item>
    <item name="paletteNeutralVariant40">@color/md_theme_libchecker_palette_neutral_variant_40</item>
    <item name="paletteNeutralVariant50">@color/md_theme_libchecker_palette_neutral_variant_50</item>
    <item name="paletteNeutralVariant60">@color/md_theme_libchecker_palette_neutral_variant_60</item>
    <item name="paletteNeutralVariant70">@color/md_theme_libchecker_palette_neutral_variant_70</item>
    <item name="paletteNeutralVariant80">@color/md_theme_libchecker_palette_neutral_variant_80</item>
    <item name="paletteNeutralVariant90">@color/md_theme_libchecker_palette_neutral_variant_90</item>
    <item name="paletteNeutralVariant95">@color/md_theme_libchecker_palette_neutral_variant_95</item>
    <item name="paletteNeutralVariant99">@color/md_theme_libchecker_palette_neutral_variant_99</item>
    <item name="paletteNeutralVariant100">@color/md_theme_libchecker_palette_neutral_variant_100</item>
  </style>
</resources>
