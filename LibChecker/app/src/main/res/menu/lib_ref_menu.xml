<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools">

  <item
    android:id="@+id/search"
    android:icon="@drawable/ic_search"
    android:title="@string/menu_search"
    app:actionViewClass="androidx.appcompat.widget.SearchView"
    app:showAsAction="always|collapseActionView"
    tools:ignore="AlwaysShowAction" />

  <item
    android:id="@+id/chart"
    android:icon="@drawable/ic_chart"
    android:title="@string/tab_chart"
    app:showAsAction="always" />

  <item
    android:id="@+id/filter"
    android:icon="@drawable/ic_menu"
    android:title="@string/menu_filter"
    app:showAsAction="always" />

</menu>
