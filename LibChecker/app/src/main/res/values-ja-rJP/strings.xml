<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">アプリ</string>
  <string name="title_statistics">統計情報</string>
  <string name="title_snapshot">スナップショット</string>
  <string name="title_settings">設定</string>
  <string name="title_album">アルバム</string>
  <string name="loading">読み込み中…</string>
  <string name="channel_shoot">スナップショットを保存</string>
  <string name="noti_shoot_title">現在のスナップショットを保存中</string>
  <string name="noti_shoot_title_saved">スナップショットが保存されました</string>
  <!--  App list  -->
  <string name="menu_search">検索</string>
  <string name="menu_sort">ソート</string>
  <string name="menu_filter">フィルタ</string>
  <string name="search_hint">検索…</string>
  <string name="string_64_bit">64bit</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs">ネイティブライブラリなし</string>
  <string name="cannot_read">読み込めません</string>
  <string name="unknown">不明</string>
  <string name="empty_list">空のリスト</string>
  <string name="uncharted_territory">未知の領域</string>
  <string name="get_app_list_denied_tip">インストールされているアプリに関する情報を取得する為に\nLibCheckerの権限を許可してください。</string>
  <string name="advanced_menu">詳細メニュー</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">%s ABI のアプリ</string>
  <string name="title_statistics_dialog_no_native_libs">ネイティブライブラリのないアプリ</string>
  <string name="tab_chart">チャート</string>
  <string name="tab_lib_reference_statistics">ライブラリ参照</string>
  <string name="not_marked_lib">マークされていないライブラリ</string>
  <string name="submenu_title_component">コンポーネント</string>
  <string name="submenu_title_manifest">マニフェスト</string>
  <string name="ref_category_all">すべて</string>
  <string name="ref_category_native">ネイティブライブラリ</string>
  <string name="ref_category_service">サービス</string>
  <string name="ref_category_activity">アクティビティ</string>
  <string name="ref_category_br">ブロードキャストレシーバー</string>
  <string name="ref_category_cp">コンテンツプロバイダ</string>
  <string name="ref_category_perm">権限</string>
  <string name="ref_category_static">静的ライブラリ</string>
  <string name="ref_category_metadata">メタデータ</string>
  <string name="ref_category_package">パッケージ</string>
  <string name="ref_category_shared_uid">共有UID</string>
  <string name="ref_category_signatures">署名</string>
  <string name="ref_category_only_not_marked">マークされていないもののみ</string>
  <string name="string_kotlin_used">Kotlin使用済み</string>
  <string name="string_kotlin_unused">Kotlin未使用</string>
  <string name="string_compose_used">Jetpack Compose使用済み</string>
  <string name="string_compose_unused">Jetpack Compose未使用</string>
  <string name="android_dist_source">ソース</string>
  <string name="android_dist_title">Androidバージョン分布統計</string>
  <string name="android_dist_subtitle_format">更新時間：%s</string>
  <!--  About  -->
  <string name="settings_about">アプリについて</string>
  <string name="settings_about_summary">これはLibCheckerです！</string>
  <string name="settings_translate">翻訳に参加する</string>
  <string name="settings_translate_summary">このアプリの翻訳にご協力ください</string>
  <string name="settings_rate_us">評価する</string>
  <string name="settings_rate_us_summary">これにより、より多くの人々に見つけてもらえます</string>
  <string name="settings_get_updates">アップデートを入手</string>
  <string name="settings_get_updates_summary">より安定し、機能が充実した最新バージョンを入手してください</string>
  <string name="about_info">このアプリは、デバイス内のアプリケーションで使用されているサードパーティのライブラリを表示するために使用されます。</string>
  <string name="toolbar_rate">評価</string>
  <string name="resource_declaration">アプリケーションの一部のリソースは、以下から提供されています。</string>
  <string name="library_declaration">LibCheckerのすべてのマークされたライブラリ情報は、それに属するSDKの開発ドキュメントまたはコードリポジトリから来ています。情報が正しくない場合は、次のアドレスに連絡してください：<EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">通常</string>
  <string name="pref_group_others">その他</string>
  <string name="apk_analytics">APKアナリティクス</string>
  <string name="colorful_icon">カラフルなアイコン</string>
  <string name="rules_repo_title">ルールリポジトリ</string>
  <string name="lib_ref_threshold">ライブラリ参照閾値</string>
  <string name="languages">言語</string>
  <string name="reload_apps">アプリリストを再読み込み</string>
  <string name="help_docs">ヘルプドキュメント</string>
  <string name="join_telegram_group">コミュニケーション</string>
  <string name="anonymous_statistics">匿名統計</string>
  <string name="cloud_rules">クラウドルール</string>
  <string name="dark_mode">ダークモード</string>
  <string name="snapshot_keep">スナップショットのデフォルト保持ルール</string>
  <string name="apk_analytics_summary">APKファイルをクリックすると詳細ページが開きます</string>
  <string name="colorful_icon_summary">一部のマークされたライブラリはカラフルなロゴとして表示されます</string>
  <string name="lib_ref_threshold_summary">閾値に達したライブラリ参照数のみがリストに表示されます</string>
  <string name="reload_apps_summary">アプリリストが異常に表示された場合は、試してください</string>
  <string name="help_docs_summary">Telegramグループに参加</string>
  <string name="join_telegram_group_summary">Telegramグループに参加する</string>
  <string name="anonymous_statistics_summary">Google Firebaseを使用して、最も一般的に使用されるマークされたライブラリやいくつかの使用習慣データを匿名で送信し、LibCheckerをより実用的にしています</string>
  <string name="cloud_rules_summary">ここで最新のマークされたライブラリルールを更新してください</string>
  <string name="array_dark_mode_off">オフ</string>
  <string name="array_dark_mode_on">オン</string>
  <string name="array_dark_mode_auto">自動</string>
  <string name="array_dark_mode_system">システムの標準</string>
  <string name="array_dark_mode_battery">バッテリーセーバーによる設定</string>
  <string name="array_snapshot_default">通知</string>
  <string name="array_snapshot_keep">保持</string>
  <string name="array_snapshot_discard">破棄</string>
  <!--  Detail  -->
  <string name="detail">詳細</string>
  <string name="detail_label">アプリ詳細を表示</string>
  <string name="not_found">見つかりません</string>
  <string name="create_an_issue">情報の補足を手伝ってください</string>
  <string name="app_bundle_details">Android App Bundleは、Androidの新しい公式の公開形式で、より効率的な方法でアプリをビルドおよびリリースできます。Android App Bundleを使用すると、より小さいアプリサイズで素晴らしいエクスペリエンスを簡単に提供できるため、インストール成功が向上し、アンインストールが減少します。切り替えは簡単です。アプリのサイズを小さくするために、コードをリファクタリングする必要はありません。切り替えると、モジュラーアプリ開発とカスタマイズ可能な機能提供のメリットが得られます。</string>
  <string name="kotlin_details">Kotlinは、型推論を持つクロスプラットフォームの静的型付け汎用プログラミング言語です。KotlinはJavaと完全に相互運用可能に設計されており、Kotlinの標準ライブラリのJVMバージョンはJavaクラスライブラリに依存していますが、型推論により構文がより簡潔になります。</string>
  <string name="items_count">アイテム数：</string>
  <string name="further_operation">さらなる操作</string>
  <string name="app_info_launch">起動</string>
  <string name="app_info_settings">設定</string>
  <string name="lib_detail_dialog_title">ライブラリ詳細</string>
  <string name="lib_permission_dialog_title">権限詳細</string>
  <string name="agp_details">Android StudioのビルドシステムはGradleに基づいており、Android GradleプラグインはAndroidアプリのビルドに特化したいくつかの機能を追加します。Androidプラグインは通常、Android Studioとロックステップで更新されますが、プラグイン（およびGradleシステムの残り）はAndroid Studioから独立して実行でき、別々に更新できます。</string>
  <string name="xposed_module">Xposedモジュール</string>
  <string name="xposed_module_details">Xposedは、APKに触れることなくシステムとアプリの動作を変更することができるモジュールのフレームワークです。それは素晴らしいことです、なぜならそれはモジュールが異なるバージョンやROMで変更なしに動作できることを意味します。</string>
  <string name="play_app_signing">Playアプリの署名</string>
  <string name="play_app_signing_details">Playアプリの署名では、Googleがあなたのアプリの署名キーを管理し、保護し、それを使用して、あなたのアプリバンドルから生成された最適化された配布APKに署名します。Playアプリの署名は、あなたのアプリの署名キーをGoogleの安全なインフラストラクチャ上に保存し、セキュリティを向上させるためのアップグレードオプションを提供します。</string>
  <string name="pwa_details">プログレッシブWebアプリ（PWA）は、現代のAPIを使用して構築および強化され、高度な機能、信頼性、インストール可能性を提供しながら、単一のコードベースでどのデバイスでもどこでも誰にでもアクセスします。</string>
  <string name="jetpack_compose_details">Jetpack Composeは、AndroidのネイティブUIを構築するための現代的なツールキットです。これにより、AndroidでのUI開発が簡略化され、加速します。少ないコード、強力なツール、直感的なKotlin APIを使用して、すぐにアプリを実現します。</string>
  <string name="extract_native_libs_tip">アプリは次のものを宣言します</string>
  <string name="xml_detail">XML詳細</string>
  <string name="format_last_updated">更新日：%s</string>
  <string name="menu_process">プロセス</string>
  <string name="menu_split">分割</string>
  <string name="alternative_launch_method">代替起動方法</string>
  <string name="compare_with_current">現在のパッケージと比較</string>
  <string name="rx_detail">RxJavaは、Reactive ExtensionsのJava VM実装です。これは、観察可能なシーケンスを使用して非同期およびイベントベースのプログラムを構成するためのライブラリです。</string>
  <string name="rx_android_detail">Android向けのReactive Extensions</string>
  <string name="rx_kotlin_detail">RxJavaのKotlin Extensions</string>
  <string name="permission_not_granted">許可されていません</string>
  <string name="signature_detail">署名の詳細</string>
  <string name="signature_version">バージョン</string>
  <string name="signature_serial_number">シリアル番号</string>
  <string name="signature_issuer">発行者</string>
  <string name="signature_subject">件名</string>
  <string name="signature_validity_not_before">有効開始日</string>
  <string name="signature_validity_not_after">有効終了日</string>
  <string name="signature_public_key_format">公開鍵のフォーマット</string>
  <string name="signature_public_key_algorithm">公開鍵のアルゴリズム</string>
  <string name="signature_public_key_exponent">公開鍵の指数</string>
  <string name="signature_public_key_modulus_size">公開鍵のモジュラスサイズ</string>
  <string name="signature_public_key_modulus">公開鍵のモジュラス</string>
  <string name="signature_public_key_y">公開鍵の値Y</string>
  <string name="signature_public_key_type">公開鍵のタイプ</string>
  <string name="signature_algorithm_name">署名アルゴリズム名</string>
  <string name="signature_algorithm_oid">署名アルゴリズムOID</string>
  <string name="lib_detail_label_tip">ラベル</string>
  <string name="lib_detail_develop_team_tip">開発チーム</string>
  <string name="lib_detail_rule_contributors_tip">ルール貢献者</string>
  <string name="lib_detail_description_tip">説明</string>
  <string name="lib_detail_relative_link_tip">関連リンク</string>
  <string name="lib_detail_last_update_tip">更新日</string>
  <string name="lib_detail_app_props_title">アプリのプロパティ</string>
  <string name="lib_detail_app_props_tip">詳細情報</string>
  <string name="lib_detail_xposed_min_version">最小バージョン</string>
  <string name="lib_detail_xposed_default_scope">デフォルトのスコープ</string>
  <string name="lib_detail_xposed_init_class">初期化クラス</string>
  <string name="lib_detail_app_install_source_title">インストールソース</string>
  <string name="lib_detail_app_install_source_originating_package">インストールをリクエストしたパッケージ</string>
  <string name="lib_detail_app_install_source_installing_package">インストールを実行するパッケージ</string>
  <string name="lib_detail_app_install_source_empty">不明</string>
  <string name="lib_detail_app_install_source_empty_detail">Shellまたはアンインストール済みのアプリ</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Androidはインストールを要求したアプリを取得するAPIを制限しているため、Shizukuを使用してインストールを要求したアプリを取得します。</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizukuがインストールされていません</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">ここをクリックしてShizukuをインストール</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Shizuku API 10が必要です</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">ここをクリックしてShizukuを更新</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizukuが起動していません</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">ここをクリックしてShizukuにジャンプして起動します</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizukuが承認されていません</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">ここをクリックしてShizukuのインストールリクエスターに承認します</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">クリップボードにコピーされました</string>
  <string name="toast_not_existing_market">アプリマーケットが存在しません</string>
  <string name="toast_use_another_file_manager">他のファイルマネージャーを使用してAPKを開いてください</string>
  <string name="toast_cant_open_app">このアプリを開くことができません</string>
  <string name="toast_cloud_rules_update_error">クラウドルールの更新に失敗しました。再試行してください。</string>
  <string name="toast_not_enough_storage_space">ストレージスペースが不足しています</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">現在のスナップショットを保存</string>
  <string name="snapshot_current_timestamp">現在のスナップショットのタイムスタンプ</string>
  <string name="snapshot_apps_count">スナップショット内のアプリ数/アプリ数</string>
  <string name="comparison_snapshot_apps_count">スナップショット内のアプリ数</string>
  <string name="snapshot_indicator_added">追加されました</string>
  <string name="snapshot_indicator_removed">削除されました</string>
  <string name="snapshot_indicator_changed">変更されました</string>
  <string name="snapshot_indicator_moved">移動されました</string>
  <string name="snapshot_empty_list_title">コンポーネントの変更なし</string>
  <string name="snapshot_no_snapshot">スナップショットなし</string>
  <string name="snapshot_detail_new_install_title">このアプリは新たにインストールされました</string>
  <string name="snapshot_detail_deleted_title">このアプリは削除されました</string>
  <string name="snapshot_time_node_uninitialized">(初期化されていません)</string>
  <string name="snapshot_preinstalled_app">事前にインストールされたアプリ</string>
  <string name="snapshot_generate_text_report">テキストレポートを生成</string>
  <string name="snapshot_scheme_tip"><![CDATA[ヒント：現在、次のようにバックグラウンドでスナップショットを生成できます：<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">すべてのアプリを再読み込みしますか？</string>
  <string name="dialog_subtitle_reload_apps">これには数秒かかる場合があります</string>
  <string name="dialog_title_change_timestamp">タイムノードを変更</string>
  <string name="dialog_title_keep_previous_snapshot">注意</string>
  <string name="dialog_message_keep_previous_snapshot">前のスナップショットを保持しますか？</string>
  <string name="dialog_title_select_to_delete">スナップショットを削除する日付を選択</string>
  <string name="dialog_title_confirm_to_delete">このスナップショットを削除してもよろしいですか？</string>
  <string name="btn_keep">保持</string>
  <string name="btn_drop">破棄</string>
  <string name="dialog_title_compare_diff_apk">注意</string>
  <string name="dialog_message_compare_diff_apk">異なる2つのAPKを比較しますか？</string>
  <!--  Album  -->
  <string name="album_compare">比較</string>
  <string name="album_item_comparison_title">比較</string>
  <string name="album_item_comparison_subtitle">2つのスナップショットを比較</string>
  <string name="album_item_comparison_invalid_compare">無効な比較</string>
  <string name="album_item_comparison_invalid_shared_items">比較のために2つのAPKファイルを選択してください</string>
  <string name="album_item_comparison_choose_local_apk">ローカルのAPKを選択</string>
  <string name="album_item_management_title">管理</string>
  <string name="album_item_management_subtitle">すべてのスナップショットを管理</string>
  <string name="album_item_backup_restore_title">バックアップと復元</string>
  <string name="album_item_backup_restore_subtitle">スナップショットをバックアップ/復元します</string>
  <string name="album_item_track_title">トラック</string>
  <string name="album_item_track_subtitle">比較変更を強制するアプリを選択してください</string>
  <string name="album_backup">バックアップ</string>
  <string name="album_backup_summary">すべてのスナップショットをバックアップ</string>
  <string name="album_restore">復元</string>
  <string name="album_restore_summary">バックアップファイルからスナップショットを復元</string>
  <string name="album_restore_detail">%1$s : 合計で%2$s件のアイテム\n</string>
  <string name="album_click_to_choose">クリックで選択</string>
  <string name="album_dialog_delete_snapshot_message">処理中…</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">MonkeyKing Purifyでブロックをする</string>
  <string name="integration_monkey_king_menu_unblock">MonkeyKing Purifyでのブロックを解除</string>
  <string name="integration_blocker_menu_block">Blockerでブロックをする</string>
  <string name="integration_blocker_menu_unblock">Blockerでのブロックを解除</string>
  <string name="integration_anywhere_menu_editor">Anywhere-Editorで開く</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">ローカルリポジトリのバージョン</string>
  <string name="rules_remote_repo_version">リモートリポジトリのバージョン</string>
  <string name="rules_btn_restart_to_update">再起動して更新</string>
  <string name="rules_btn_update">更新</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">システムアプリを表示</string>
  <string name="adv_show_overlays">オーバーレイを表示</string>
  <string name="adv_show_64_bit">64bitアプリを表示</string>
  <string name="adv_show_32_bit">32bitアプリを表示</string>
  <string name="adv_sort_mode">ソートモード</string>
  <string name="adv_sort_by_time">更新時間順</string>
  <string name="adv_sort_by_target_version">ターゲットバージョン順</string>
  <string name="adv_sort_by_name">名前順</string>
  <string name="adv_show_android_version">Androidバージョンを表示</string>
  <string name="adv_show_target_version">ターゲットバージョンを表示</string>
  <string name="adv_show_min_version">最小バージョンを表示</string>
  <string name="adv_show_compile_version">コンパイルバージョンを表示</string>
  <string name="adv_tint_abi_label">ABIラベルの色付け</string>
  <string name="adv_mark_exported">エクスポートされたコンポーネントをマークする</string>
  <string name="adv_mark_disabled">無効なコンポーネントをマークする</string>
  <string name="adv_show_marked_lib">マークされたライブラリを表示</string>
  <string name="adv_show_system_framework_apps">フレームワークアプリを表示</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">更新時間を表示</string>
  <string name="snapshot_menu_hide_no_component_changes">コンポーネントの変更がないものを隠す</string>
  <string name="snapshot_menu_diff_highlight">差分のハイライト表示</string>
</resources>
