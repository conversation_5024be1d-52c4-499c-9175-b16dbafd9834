<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">التطبيقات</string>
  <string name="title_statistics">الإحصائيات</string>
  <string name="title_snapshot">نقطة استعادة أو Snapshot</string>
  <string name="title_settings">الاعدادات</string>
  <string name="title_album">ألبوم</string>
  <string name="loading">جارٍ التحميل…</string>
  <string name="channel_shoot">حفظ نِقَاط الاستعادة</string>
  <string name="noti_shoot_title">حفظ نقطة الاستعادة الحالية</string>
  <string name="noti_shoot_title_saved">تم حفظ نقطة الاستعادة</string>
  <!--  App list  -->
  <string name="menu_search">بحث</string>
  <string name="menu_sort">الفرز</string>
  <string name="menu_filter">فلتر</string>
  <string name="search_hint">البحث…</string>
  <string name="string_64_bit">64 بت</string>
  <string name="string_32_bit">32 بت</string>
  <string name="no_libs">لا توجد libs أصلية</string>
  <string name="cannot_read">لا يمكن القراءة</string>
  <string name="unknown">غير معروف</string>
  <string name="empty_list">قائمة فارغة</string>
  <string name="uncharted_territory">منطقة مجهولة</string>
  <string name="get_app_list_denied_tip">الرجاء منح إذن \n \"الحصول على معلومات حول التطبيقات المثبتة\" \n  لـLibChecker</string>
  <string name="advanced_menu">قائمة متقدمة</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">التطبيقات ذات %s ABI</string>
  <string name="title_statistics_dialog_no_native_libs">تطبيقات بدون مكتبات أصلية</string>
  <string name="tab_chart">جدول</string>
  <string name="tab_lib_reference_statistics">مرجع المكتبة</string>
  <string name="not_marked_lib">مكتبة بدون علامات</string>
  <string name="submenu_title_component">مكون</string>
  <string name="submenu_title_manifest">Manifest</string>
  <string name="ref_category_all">الكل</string>
  <string name="ref_category_native">المكتبات الأصلية</string>
  <string name="ref_category_service">الخدمات</string>
  <string name="ref_category_activity">الأنشطة</string>
  <string name="ref_category_br">أجهزة استقبال البث</string>
  <string name="ref_category_cp">موفري المحتوى</string>
  <string name="ref_category_perm">الأذونات</string>
  <string name="ref_category_static">المكتبات الثابتة</string>
  <string name="ref_category_metadata">البيانات الوصفية</string>
  <string name="ref_category_package">الحزمة</string>
  <string name="ref_category_shared_uid">UID المشتركة</string>
  <string name="ref_category_signatures">التواقيع</string>
  <string name="ref_category_only_not_marked">فقط لم يتم وضع علامة</string>
  <string name="string_kotlin_used">يستخدم  كوتلين</string>
  <string name="string_kotlin_unused">لا يستخدم كوتلين</string>
  <string name="string_compose_used">يستخدم Jetpack Compose</string>
  <string name="string_compose_unused">لا يستخدم Jetpack Compose</string>
  <string name="android_dist_source">المصدر</string>
  <string name="android_dist_title">إحصائيات توزيع إصدار الأندرويد</string>
  <string name="android_dist_subtitle_format">وقت التحديث: %s</string>
  <!--  About  -->
  <string name="settings_about">حول</string>
  <string name="settings_about_summary">هذا هو LibChecker!</string>
  <string name="settings_translate">المشاركة في الترجمة</string>
  <string name="settings_translate_summary">ساعدنا في ترجمة هذا التطبيق</string>
  <string name="settings_rate_us">قيّمنا</string>
  <string name="settings_rate_us_summary">هذا يمكن أن يزيد شعبيته عند المزيد من الناس</string>
  <string name="settings_get_updates">الحصول على التحديث</string>
  <string name="settings_get_updates_summary">الحصول على أحدث إصدار أكثر استقراراً ومعبأ بالمميزات</string>
  <string name="about_info">يستخدم هذا التطبيق لعرض مكتبات الجهات الخارجية التي تستخدمها التطبيقات في جهازك.</string>
  <string name="toolbar_rate">تقييم</string>
  <string name="resource_declaration">جزء من الموارد في التطبيق يأتي من: </string>
  <string name="library_declaration">تأتي جميع معلومات المكتبات المميزة في LibChecker من وثائق التطوير أو مستودع التعليمات البرمجية ل SDK الذي تنتمي إليه. إذا كانت المعلومات غير صحيحة ، يرجى الاتصال بـ: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">عادي</string>
  <string name="pref_group_others">الاُخْرى</string>
  <string name="apk_analytics">تحليلات APK</string>
  <string name="colorful_icon">أيقونة ملونة</string>
  <string name="rules_repo_title">مستودع القواعد</string>
  <string name="lib_ref_threshold">حد مرجعية المكتبة</string>
  <string name="languages">اللغات</string>
  <string name="reload_apps">إعادة تحميل قائمة التطبيقات</string>
  <string name="help_docs">مستندات المساعدة</string>
  <string name="join_telegram_group">المجتمع</string>
  <string name="anonymous_statistics">إحصائيات مجهولة المصدر</string>
  <string name="cloud_rules">قواعد السحابة</string>
  <string name="dark_mode">الوضع الداكن</string>
  <string name="snapshot_keep">نقطة استعادة قاعدة الاحتفاظ الافتراضية</string>
  <string name="apk_analytics_summary">افتح صفحة التفاصيل عند النقر فوق ملف APK</string>
  <string name="colorful_icon_summary">سيظهر جزء من المكتبات المميزة كشعار ملون</string>
  <string name="lib_ref_threshold_summary">يتم عرض المكتبات التي يصل عدد مراجع المكتبة فيها إلى الحد الأدنى فقط في القائمة</string>
  <string name="reload_apps_summary">جربه عندما تظهر قائمة التطبيقات بشكل غير طبيعي</string>
  <string name="help_docs_summary">تعرف على كيفية استخدام LibChecker</string>
  <string name="join_telegram_group_summary">انضم إلى مجموعة Telegram الخاصة بنا</string>
  <string name="anonymous_statistics_summary">نحن نستخدم Google Firebase لإرسال المكتبات المميزة الأكثر استخداما وبعض بيانات عادات الاستخدام بشكل مجهول لجعل LibChecker أكثر عملية</string>
  <string name="cloud_rules_summary">قم بتحديث أحدث قواعد المكتبة المميزة هنا</string>
  <string name="array_dark_mode_off">إيقاف</string>
  <string name="array_dark_mode_on">تشغيل</string>
  <string name="array_dark_mode_auto">تلقائي</string>
  <string name="array_dark_mode_system">النظام الافتراضي</string>
  <string name="array_dark_mode_battery">نمط توفير البطارية</string>
  <string name="array_snapshot_default">إشعار</string>
  <string name="array_snapshot_keep">حفظ</string>
  <string name="array_snapshot_discard">تجاهل</string>
  <!--  Detail  -->
  <string name="detail">التفاصيل</string>
  <string name="detail_label">عرض تفاصيل التطبيق</string>
  <string name="not_found">غير موجود</string>
  <string name="create_an_issue">مساعدتنا في استكمال المعلومات</string>
  <string name="app_bundle_details">حزمة تطبيقات Android أو (Bundle) هي تنسيق النشر الرسمي الجديد لنظام Android والذي يوفر طريقة أكثر فاعلية لإنشاء تطبيقك وإصداره. تتيح لك حزمة تطبيقات Android تقديم تجربة رائعة بسهولة أكبر بحجم تطبيق أصغر، مما يمكن أن يحسن نجاح التثبيت ويقلل من عمليات إلغاء التثبيت. من السهل التبديل. لا تحتاج إلى إعادة بناء التعليمات البرمجية الخاصة بك لبدء الاستفادة من تطبيق أصغر. وبمجرد التبديل ، ستستفيد من تطوير التطبيقات المعيارية وتسليم الميزات القابلة للتخصيص.</string>
  <string name="kotlin_details">Kotlin هي لغة برمجة للأغراض العامة عبر الأنظمة الأساسية ، مكتوبة بشكل ثابت ، مع استدلال النوع. تم تصميم Kotlin للتفاعل بشكل كامل مع Java ، ويعتمد إصدار JVM من مكتبة Kotlin القياسية على مكتبة فئة Java ، لكن استدلال النوع يسمح ببناء الجملة الخاص به ليكون أكثر إيجازا أو اختصارا.</string>
  <string name="items_count">عدد العناصر: </string>
  <string name="further_operation">مزيد من العمليات</string>
  <string name="app_info_launch">تشغيل</string>
  <string name="app_info_settings">الاعدادات</string>
  <string name="lib_detail_dialog_title">تفاصيل المكتبة</string>
  <string name="lib_permission_dialog_title">تفاصيل الإذن</string>
  <string name="agp_details">يعتمد نظام بناء Android Studio على Gradle, ويضيف المكون الإضافي Android Gradle العديد من الميزات الخاصة بإنشاء تطبيقات Android. على الرغم من أن المكون الإضافي لنظام Android يتم تحديثه عادة في خطوة قفل مع Android Studio ، إلا أن المكون الإضافي (وبقية نظام Gradle) يمكن تشغيله بشكل مستقل عن Android Studio ويتم تحديثه بشكل منفصل.</string>
  <string name="xposed_module">وحدة Xposed</string>
  <string name="xposed_module_details">Xposed هو إطار عمل للوحدات النمطية التي يمكنها تغيير سلوك النظام والتطبيقات دون لمس أي ملفات APK. هذا رائع لأنه يعني أن الوحدات يمكن أن تعمل مع إصدارات مختلفة وحتى ذاكرة القراءة فقط دون أي تغييرات.</string>
  <string name="play_app_signing">تشغيل توقيع التطبيق</string>
  <string name="play_app_signing_details">باستخدام ميزة \"توقيع تطبيقات Play\"، تدير Google مفتاح توقيع تطبيقك وتحميه نيابة عنك وتستخدمه لتوقيع ملفات APK المحسنة والموزعة التي يتم إنشاؤها من حزم تطبيقاتك. يخزن Play App Signing مفتاح توقيع التطبيق على البنية الأساسية الآمنة من Google ويوفر خيارات ترقية لزيادة الأمان.</string>
  <string name="pwa_details">تم إنشاء تطبيقات الويب التقدمية (PWA) وتحسينها باستخدام واجهات برمجة التطبيقات الحديثة لتوفير إمكانات محسنة وموثوقية وإمكانية تثبيت أثناء الوصول إلى أي شخص في أي مكان وعلى أي جهاز باستخدام قاعدة بيانات واحدة.</string>
  <string name="jetpack_compose_details">(Jetpack Compose) هي مجموعة أدوات Android الحديثة لبناء واجهة مستخدم أصلية. إنه يبسط ويسرع تطوير واجهة المستخدم على Android. اجعل تطبيقك ينبض بالحياة بسرعة باستخدام تعليمات برمجية أقل وأدوات قوية وواجهات برمجة تطبيقات Kotlin سهلة الاستخدام.</string>
  <string name="extract_native_libs_tip">تصريح التطبيق </string>
  <string name="xml_detail">تفاصيل XML</string>
  <string name="format_last_updated">تم التحديث في: %s</string>
  <string name="menu_process">العمليات</string>
  <string name="menu_split">مقسم</string>
  <string name="alternative_launch_method">طريقة التشغيل البديلة</string>
  <string name="compare_with_current">مقارنة مع الحزمة الحالية</string>
  <string name="rx_detail">\"RxJava\" هو تطبيق Java VM للملحقات التفاعلية: مكتبة لإنشاء برامج غير متزامنة وقائمة على الأحداث باستخدام تسلسلات يمكن ملاحظتها.</string>
  <string name="rx_android_detail">ملحقات تفاعلية لنظام Android</string>
  <string name="rx_kotlin_detail">ملحقات Kotlin لـRxJava</string>
  <string name="permission_not_granted">لم يتم المنح</string>
  <string name="signature_detail">تفاصيل التوقيع</string>
  <string name="signature_version">النسخة</string>
  <string name="signature_serial_number">الرقم التسلسلي</string>
  <string name="signature_issuer">المصدر</string>
  <string name="signature_subject">موضوع</string>
  <string name="signature_validity_not_before">الصحة ليست قبل</string>
  <string name="signature_validity_not_after">الصحة ليست بعد</string>
  <string name="signature_public_key_format">تنسيق المفتاح العام</string>
  <string name="signature_public_key_algorithm">خوارزمية المفتاح العام</string>
  <string name="signature_public_key_exponent">أس المفتاح العام</string>
  <string name="signature_public_key_modulus_size">حجم معامل المفتاح العام</string>
  <string name="signature_public_key_modulus">معامل المفتاح العام</string>
  <string name="signature_public_key_y">قيمة المفتاح العام Y</string>
  <string name="signature_public_key_type">نوع المفتاح العام</string>
  <string name="signature_algorithm_name">اسم خوارزمية التوقيع</string>
  <string name="signature_algorithm_oid">خوارزمية التوقيع OID</string>
  <string name="lib_detail_label_tip">تسمية</string>
  <string name="lib_detail_develop_team_tip">فريق التطوير</string>
  <string name="lib_detail_rule_contributors_tip">المساهم (المساهمون) في القاعدة</string>
  <string name="lib_detail_description_tip">الوصف</string>
  <string name="lib_detail_relative_link_tip">رابط نسبي</string>
  <string name="lib_detail_last_update_tip">تم التحديث في</string>
  <string name="lib_detail_app_props_title">خصائص التطبيق</string>
  <string name="lib_detail_app_props_tip">المزيد من المعلومات</string>
  <string name="lib_detail_xposed_min_version">الحد الأدنى للإصدار</string>
  <string name="lib_detail_xposed_default_scope">النطاق الافتراضي</string>
  <string name="lib_detail_xposed_init_class">فئة النشاط</string>
  <string name="lib_detail_app_install_source_title">مصدر التثبيت</string>
  <string name="lib_detail_app_install_source_originating_package">مقدم التثبيت</string>
  <string name="lib_detail_app_install_source_installing_package">منفذ التثبيت</string>
  <string name="lib_detail_app_install_source_empty">غير معروف</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell أو تطبيق غير مثبت</string>
  <string name="lib_detail_app_install_source_shizuku_usage">نظرا لأن Android يقيد واجهة برمجة التطبيقات للحصول على مقدم التثبيت ، فإننا نستخدم Shizuku للحصول على طالب التثبيت.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">شيزوكو غير مثبت</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">انقر هنا لتثبيت شيزوكو</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">يتطلب شيزوكو API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">انقر هنا لتحديث شيزوكو</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">شيزوكو لا يعمل</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">انقر هنا للانتقال إلى شيزوكو لبدء تشغيله</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">شيزوكو غير مصرح به</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">انقر هنا للحصول على إذن Shizuku مقدم التثبيت</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">نسخ إلى الحافظة</string>
  <string name="toast_not_existing_market">لا يوجد أي سوق تطبيقات موجود</string>
  <string name="toast_use_another_file_manager">الرجاء استخدام مدير ملفات آخر لفتح APK</string>
  <string name="toast_cant_open_app">لا يمكن فتح هذا التطبيق</string>
  <string name="toast_cloud_rules_update_error">فشل تحديث قواعد السحابة. يرجى المحاولة مرة أخرى.</string>
  <string name="toast_not_enough_storage_space">لا توجد مساحة تخزين كافية</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">حفظ نقطة الاستعادة الحالية</string>
  <string name="snapshot_current_timestamp">الطابع الزمني الحالي لنقطة الاستعادة</string>
  <string name="snapshot_apps_count">عدد التطبيقات في نقطة الاستعادة / عدد التطبيقات</string>
  <string name="comparison_snapshot_apps_count">عدد التطبيقات في نقطة الاستعادة</string>
  <string name="snapshot_indicator_added">تمت الاضافة</string>
  <string name="snapshot_indicator_removed">تمت الإزالة</string>
  <string name="snapshot_indicator_changed">تم التغيير</string>
  <string name="snapshot_indicator_moved">تم النقل</string>
  <string name="snapshot_empty_list_title">لا تغييرات في المكونات</string>
  <string name="snapshot_no_snapshot">نقطة استعادة أو Snapshot</string>
  <string name="snapshot_detail_new_install_title">تم تثبيت هذا التطبيق حديثا</string>
  <string name="snapshot_detail_deleted_title">تمت إزالة هذا التطبيق</string>
  <string name="snapshot_time_node_uninitialized">(غير مهيأ)</string>
  <string name="snapshot_preinstalled_app">التطبيق المثبت مسبقا</string>
  <string name="snapshot_generate_text_report">إنشاء تقرير نصي</string>
  <string name="snapshot_scheme_tip"><![CDATA[نصيحة: يمكنك الآن إنشاء نقاط استعادة في الخلفية عبر:<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">هل أنت متأكد من إعادة تحميل جميع التطبيقات؟</string>
  <string name="dialog_subtitle_reload_apps">قد يستغرق هذا بضع ثوان</string>
  <string name="dialog_title_change_timestamp">تغيير عقدة الوقت</string>
  <string name="dialog_title_keep_previous_snapshot">انتباه</string>
  <string name="dialog_message_keep_previous_snapshot">هل تريد الاحتفاظ بنقطة الاستعادة السابقة؟</string>
  <string name="dialog_title_select_to_delete">اختر تاريخا لحذف نقطة إستعادة</string>
  <string name="dialog_title_confirm_to_delete">هل أنت متأكد من حذف نقطة الاستعادة هذه؟</string>
  <string name="btn_keep">حفظ</string>
  <string name="btn_drop">تجاهل</string>
  <string name="dialog_title_compare_diff_apk">انتباه</string>
  <string name="dialog_message_compare_diff_apk">هل تريد مقارنة اثنين من ملفات APK مختلفة؟</string>
  <!--  Album  -->
  <string name="album_compare">مقارنة</string>
  <string name="album_item_comparison_title">المقارنة</string>
  <string name="album_item_comparison_subtitle">مقارنة مع نقطتي الاستعادة</string>
  <string name="album_item_comparison_invalid_compare">المقارنة غير صحيحة</string>
  <string name="album_item_comparison_invalid_shared_items">يرجى تحديد ملفين APK للمقارنة</string>
  <string name="album_item_comparison_choose_local_apk">اختر APK المحلي</string>
  <string name="album_item_management_title">إدارة</string>
  <string name="album_item_management_subtitle">إدارة جميع نقاط الاستعادة</string>
  <string name="album_item_backup_restore_title">النسخ الاحتياطي &amp; الاستعادة</string>
  <string name="album_item_backup_restore_subtitle">النسخ الاحتياطي واستعادة نقاط الاستعادة</string>
  <string name="album_item_track_title">تتبّع</string>
  <string name="album_item_track_subtitle">حدد التطبيقات لفرض تغيير المقارنة</string>
  <string name="album_backup">النسخ الاحتياطي</string>
  <string name="album_backup_summary">إدارة جميع نقاط الاستعادة</string>
  <string name="album_restore">استعادة</string>
  <string name="album_restore_summary">استعادة نقاط الاستعادة من ملف النسخ الاحتياطي</string>
  <string name="album_restore_detail">%1$s : %2$s العناصر في المجموع\n</string>
  <string name="album_click_to_choose">انقر للاختيار</string>
  <string name="album_dialog_delete_snapshot_message">جارٍ المعالجة…</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">حظره عبر \"MonkeyKing Purify\"</string>
  <string name="integration_monkey_king_menu_unblock">إلغاء حظره عبر \"MonkeyKing Purify\"</string>
  <string name="integration_blocker_menu_block">حظره عبر \"Blocker\"</string>
  <string name="integration_blocker_menu_unblock">إلغاء حظره عبر \"Blocker\"</string>
  <string name="integration_anywhere_menu_editor">افتحه في \"محرر في أي مكان\"</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">إصدار المستودع المحلي</string>
  <string name="rules_remote_repo_version">إصدار المستودع البعيد</string>
  <string name="rules_btn_restart_to_update">إعادة التشغيل للتحديث</string>
  <string name="rules_btn_update">تحديث</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">إظهار تطبيقات النظام</string>
  <string name="adv_show_overlays">إظهار التراكبات</string>
  <string name="adv_show_64_bit">إظهار تطبيقات 64-بت</string>
  <string name="adv_show_32_bit">إظهار تطبيقات 32-بت</string>
  <string name="adv_sort_mode">وضع الفرز</string>
  <string name="adv_sort_by_time">وقت التحديث</string>
  <string name="adv_sort_by_target_version">الإصدار المستهدف</string>
  <string name="adv_sort_by_name">الاسم</string>
  <string name="adv_show_android_version">عرض نسخة أندرويد</string>
  <string name="adv_show_target_version">إظهار الإصدار المستهدف</string>
  <string name="adv_show_min_version">عرض الحد الأدنى للإصدار</string>
  <string name="adv_show_compile_version">إظهار إصدار التجميع</string>
  <string name="adv_tint_abi_label">تسمية ABI تينت</string>
  <string name="adv_mark_exported">وضع علامة على المكون المصدّر</string>
  <string name="adv_mark_disabled">وضع علامة على مكون المعطل</string>
  <string name="adv_show_marked_lib">إظهار المكتبات المميزة</string>
  <string name="adv_show_system_framework_apps">إظهار تطبيقات إطار العمل</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">إظهار وقت التحديث</string>
  <string name="snapshot_menu_hide_no_component_changes">إخفاء عدم وجود تغييرات في المكونات</string>
  <string name="snapshot_menu_diff_highlight">تحديد الفروق</string>
</resources>
