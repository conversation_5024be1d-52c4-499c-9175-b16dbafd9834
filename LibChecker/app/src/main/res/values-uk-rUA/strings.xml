<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Приложения</string>
  <string name="title_statistics">Статистика</string>
  <string name="title_snapshot">Снимок</string>
  <string name="title_settings">Налаштування</string>
  <string name="title_album">Альбом</string>
  <string name="loading">Завантаження…</string>
  <string name="channel_shoot">Зберегти</string>
  <string name="noti_shoot_title">Збереження поточного знімка</string>
  <string name="noti_shoot_title_saved">Знімок збережено</string>
  <!--  App list  -->
  <string name="menu_search">Пошук</string>
  <string name="menu_sort">Сортування</string>
  <string name="menu_filter">Фільтр</string>
  <string name="search_hint">Пошук…</string>
  <string name="no_libs">Немає нативных бібліотек</string>
  <string name="cannot_read">Неможливо прочитати</string>
  <string name="unknown">Невідомо</string>
  <string name="empty_list">Список порожній</string>
  <string name="uncharted_territory">Недосліджена територія</string>
  <string name="get_app_list_denied_tip">Будь ласка надайте\nLibChecker дозвіл отримати\nінформацію про встановлені додатки</string>
  <string name="advanced_menu">Дополнительное меню</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">Додатки з %s ABI</string>
  <string name="title_statistics_dialog_no_native_libs">Додатки без нативных бібліотек</string>
  <string name="tab_chart">Діаграма</string>
  <string name="tab_lib_reference_statistics">Посилання на бібліотеки</string>
  <string name="not_marked_lib">Не позначена бібліотека</string>
  <string name="submenu_title_component">Компонент</string>
  <string name="submenu_title_manifest">Манифест</string>
  <string name="ref_category_all">Всі</string>
  <string name="ref_category_native">Нативні бібліотеки</string>
  <string name="ref_category_service">Служби</string>
  <string name="ref_category_activity">Activity</string>
  <string name="ref_category_br">Ресівери</string>
  <string name="ref_category_cp">Провайдери</string>
  <string name="ref_category_perm">Дозволи</string>
  <string name="ref_category_static">Статические библиотеки</string>
  <string name="ref_category_metadata">Метаданные</string>
  <string name="ref_category_package">Пакет</string>
  <string name="ref_category_shared_uid">Общий UID</string>
  <string name="ref_category_signatures">підпис</string>
  <string name="ref_category_only_not_marked">Только не отмеченные</string>
  <string name="string_kotlin_used">Використовується Kotlin</string>
  <string name="string_kotlin_unused">Kotlin не використовується</string>
  <string name="string_compose_used">Використовується Jetpack Compose</string>
  <string name="string_compose_unused">Jetpack Compose не використовується</string>
  <string name="android_dist_source">Исходный код</string>
  <string name="android_dist_title">Статистика распределения версий Android</string>
  <string name="android_dist_subtitle_format">Время обновления: %s</string>
  <!--  About  -->
  <string name="settings_about">Про додаток</string>
  <string name="settings_about_summary">Це LibChecker!</string>
  <string name="settings_translate">Участвуйте в переводе</string>
  <string name="settings_translate_summary">Помогите нам перевести это приложение</string>
  <string name="settings_rate_us">Оцінити додаток</string>
  <string name="settings_rate_us_summary">Це може зробити нас доступнішим для більшого числа людей</string>
  <string name="settings_get_updates">Обновления</string>
  <string name="settings_get_updates_summary">Получить последнюю версию, которая является более стабильной и имеет больше функционала</string>
  <string name="about_info">Ця програма використовується для перегляду сторонніх бібліотек, котрі використовуються додатками на вашому пристрої.</string>
  <string name="toolbar_rate">Оцінка</string>
  <string name="resource_declaration">Частина ресурсів в додатку надходить з: </string>
  <string name="library_declaration">Вся інформація про позначені бібліотеки в LibChecker береться з документації для розробників або зі сховищ коду SDK, якому він належить. Якщо інформація невірна, будь ласка, звертайтеся: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Загальні</string>
  <string name="pref_group_others">Інші</string>
  <string name="apk_analytics">Аналізування АРК</string>
  <string name="colorful_icon">Кольорові іконки</string>
  <string name="rules_repo_title">Сховище правил</string>
  <string name="lib_ref_threshold">Поріг числа посилань на бібліотеку</string>
  <string name="languages">Мова</string>
  <string name="reload_apps">Оновити список додатків</string>
  <string name="help_docs">Довідкова документація</string>
  <string name="join_telegram_group">Звязок</string>
  <string name="anonymous_statistics">Анонімна статистика</string>
  <string name="cloud_rules">Правила в хмарі</string>
  <string name="dark_mode">Темний режим</string>
  <string name="snapshot_keep">Правило збереження за умовчанням знімка</string>
  <string name="apk_analytics_summary">Відкриває сторінку подробиць при натисканні на APK-файл</string>
  <string name="colorful_icon_summary">Частина позначених бібліотек буде відображатися у вигляді кольорових іконок.</string>
  <string name="lib_ref_threshold_summary">Відображаються лише ті бібліотеки, число посилань на які перевищує заданий поріг</string>
  <string name="reload_apps_summary">Спробуйте, якщо список додатків відображається неправильно</string>
  <string name="help_docs_summary">Ознайомтесь, як використовувати LibChecker</string>
  <string name="join_telegram_group_summary">Приєднуйтесь до нашої групи в Telegram</string>
  <string name="anonymous_statistics_summary">Ми використовуємо Google Firebase для анонімної відправки найбільш часто використовуваних позначених бібліотек і деяких даних про звички використання, щоб зробити LibChecker більш корисним.</string>
  <string name="cloud_rules_summary">Оновіть останні позначені правила бібліотеки тут</string>
  <string name="array_dark_mode_off">Выкл</string>
  <string name="array_dark_mode_on">Вкл</string>
  <string name="array_dark_mode_auto">Авто</string>
  <string name="array_dark_mode_system">Система</string>
  <string name="array_dark_mode_battery">На основе энергосбережения</string>
  <string name="array_snapshot_default">запитати</string>
  <string name="array_snapshot_keep">резерв</string>
  <string name="array_snapshot_discard">викинути</string>
  <!--  Detail  -->
  <string name="detail">подробности</string>
  <string name="detail_label">Перегляд подробиць про додаток</string>
  <string name="not_found">Не знайдено</string>
  <string name="create_an_issue">Допоможіть нам доповнити інформацію</string>
  <string name="app_bundle_details">Android App Bundle - це новий офіційний формат публікації додатків в ОС Android, який пропонує більш ефективний спосіб створення і випуску вашого додатка. Android App Bundle дозволяє випускати додатки меншого розміру, що може підвищити успішність установки і скоротити кількість видалень. Переключитися на App Bundle легко . Вам не потрібно проводити рефакторинг коду, щоб отримати користь від невеликого розміру програми. Після переходу ви отримаєте переваги в вигляді розробки модульних додатків і функції налаштовуваної доставки потрібних компонентів.</string>
  <string name="kotlin_details">Kotlin - це крос-платформна, статично набрана мова загального призначення з програмуванням типу. Kotlin розроблений для повноцінної взаємодії з Java, і версія JVM стандартної бібліотеки Kotlin залежить від бібліотеки класів Java, але вивід типу дозволяє його синтаксису бути більш стислим.</string>
  <string name="items_count">Кіл-ть пунктів: </string>
  <string name="further_operation">Додаткові опції</string>
  <string name="app_info_launch">Запустити</string>
  <string name="app_info_settings">Налаштування</string>
  <string name="lib_detail_dialog_title">Информация о библиотеке</string>
  <string name="lib_permission_dialog_title">Информация о разрешениях</string>
  <string name="agp_details">Ситема сборки Android Studio основана на Gradle, а Android Gradle плагин добавляет некоторые функции, специфичные для Android приложений. Хотя обычно плагин обыновляется вместе с Android Studio, сам плагин (и вся остальная система Gradle), может работать независимо от Android Studio и обновляться отдельно.</string>
  <string name="xposed_module_details">Xposed - это фреймворк для модулей, которые могут изменить поведение системы и приложений без вмешательства в APK-файлы. Это великолепно, поскольку это означает, что модули могут работать на разных версиях и даже прошивках без каких-либо изменений.</string>
  <string name="play_app_signing_details">С подписью Play App Signing Google управляет и защищает ключ подписи Вашего приложения и использует его для оптимизации и распространения APK-файлов, создаваемых из Ваших пакетов приложения. Play App Signing хранит ключ подписи Вашего приложения в защищенной инфраструктуре Google и предлагает варианты обновления для повышения безопасности.</string>
  <string name="pwa_details">Progressive Web Apps (PWA) are built and enhanced with modern APIs to deliver enhanced capabilities, reliability, and installability while reaching anyone, anywhere, on any device with a single codebase. </string>
  <string name="jetpack_compose_details">Jetpack Compose — это современный инструментарий для Android для создания собственного UI. Он упрощает и ускоряет разработку UI на Android. Вы можете быстро создать ваше приложение с меньшим кодом, мощным инструментарием и интуитивно понятным Kotlin API.</string>
  <string name="extract_native_libs_tip">Объявление приложения </string>
  <string name="xml_detail">XML Деталь</string>
  <string name="format_last_updated">Обновлено: %s</string>
  <string name="menu_process">Приложение</string>
  <string name="menu_split">Разделить</string>
  <string name="alternative_launch_method">Альтернативный метод запуска</string>
  <string name="compare_with_current">Сравнить с текущим пакетом</string>
  <string name="rx_detail">RxJava — це реалізація віртуальної машини Java Reactive Extensions: бібліотека для створення асинхронних програм і програм на основі подій за допомогою спостережуваних послідовностей.</string>
  <string name="rx_android_detail">Reactive Extensions для Android</string>
  <string name="rx_kotlin_detail">Розширення Kotlin для RxJava</string>
  <string name="permission_not_granted">Не предоставлено</string>
  <string name="signature_detail">Сведения о подписи</string>
  <string name="signature_version">Версия</string>
  <string name="signature_serial_number">Серийный номер</string>
  <string name="signature_issuer">Издатель</string>
  <string name="signature_subject">Тема</string>
  <string name="signature_validity_not_before">Срок действия не ранее</string>
  <string name="signature_validity_not_after">Срок действия не позже</string>
  <string name="signature_public_key_format">Формат публичного ключа</string>
  <string name="signature_public_key_algorithm">Алгоритм публичного ключа</string>
  <string name="signature_public_key_exponent">Экспонента публичного ключа</string>
  <string name="signature_public_key_modulus_size">Размер модуля публичного ключа</string>
  <string name="signature_public_key_modulus">Модуль публичного ключа</string>
  <string name="signature_public_key_y">Значение Y публичного ключа</string>
  <string name="signature_public_key_type">Тип публичного ключа</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Скопійовано до буферу обміну</string>
  <string name="toast_not_existing_market">Немає магазину додатків</string>
  <string name="toast_use_another_file_manager">Будь ласка використовуйте інший файловий менеджер, щоб відкрити АРК</string>
  <string name="toast_cant_open_app">Не вдається відкрити цей додаток</string>
  <string name="toast_cloud_rules_update_error">Не вдалося оновити хмарні правила. Будь ласка, спробуйте ще раз.</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Зберегти поточний знімок</string>
  <string name="snapshot_current_timestamp">Відмітка часу поточного знімка</string>
  <string name="snapshot_apps_count">Кількість додатків в знімку / Количество заявок</string>
  <string name="comparison_snapshot_apps_count">Кількість додатків в знімку</string>
  <string name="snapshot_indicator_added">Додано</string>
  <string name="snapshot_indicator_removed">Видалено</string>
  <string name="snapshot_indicator_changed">Змінено</string>
  <string name="snapshot_indicator_moved">Переміщено</string>
  <string name="snapshot_empty_list_title">Немає змін</string>
  <string name="snapshot_no_snapshot">Немає знімка</string>
  <string name="snapshot_detail_new_install_title">Цей додаток нещодавно встановлено</string>
  <string name="snapshot_detail_deleted_title">Цей додаток був видалений</string>
  <string name="snapshot_time_node_uninitialized">(неініціалізований)</string>
  <string name="snapshot_scheme_tip"><![CDATA[Совет: Теперь вы можете генерировать снимки в фоновом режиме через:<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Ви впевнені, що хочете перезавантажити весь список додатків?</string>
  <string name="dialog_subtitle_reload_apps">Це може зайняти кілька секунд</string>
  <string name="dialog_title_change_timestamp">Вибір часу</string>
  <string name="dialog_title_keep_previous_snapshot">Увага</string>
  <string name="dialog_message_keep_previous_snapshot">Ви хочете зберегти попередній знімок?</string>
  <string name="dialog_title_select_to_delete">Выберите дату для удаления снимка</string>
  <string name="btn_keep">Залишити</string>
  <string name="btn_drop">Видалити</string>
  <!--  Album  -->
  <string name="album_compare">Порівняти</string>
  <string name="album_item_comparison_title">Порівняння</string>
  <string name="album_item_comparison_subtitle">Порівняння двох знімків</string>
  <string name="album_item_comparison_invalid_compare">Недійсне порівняння</string>
  <string name="album_item_management_title">Менеджер</string>
  <string name="album_item_management_subtitle">Керування усіма знімками</string>
  <string name="album_item_backup_restore_title">Бекап і відновлення</string>
  <string name="album_item_backup_restore_subtitle">Бекап і відновлення знімків</string>
  <string name="album_item_track_title">Відслідковування</string>
  <string name="album_item_track_subtitle">Виберіть додатки для примусового порівняння змін</string>
  <string name="album_backup">Бекап</string>
  <string name="album_backup_summary">Бекап усіх знімків</string>
  <string name="album_restore">Відновлення</string>
  <string name="album_restore_summary">Відновлення знімків з бекапу</string>
  <string name="album_click_to_choose">Натисніть для вибору</string>
  <string name="album_dialog_delete_snapshot_message">Обробка…</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Заблокувати з допомогою \"MonkeyKing Purify\"</string>
  <string name="integration_monkey_king_menu_unblock">Розблокувати з допомогою \"MonkeyKing Purify\"</string>
  <string name="integration_blocker_menu_block">Заблокувати через \"Blocker\"</string>
  <string name="integration_blocker_menu_unblock">Розблокуйте його через \"Blocker\"</string>
  <string name="integration_anywhere_menu_editor">Відкрити в \"Anywhere- Editor\"</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Версія локального сховища</string>
  <string name="rules_remote_repo_version">Версия віддаленого сховища</string>
  <string name="rules_btn_restart_to_update">Перезагрузите, чтобы обновить</string>
  <string name="rules_btn_update">поновити</string>
  <!-- Advanced menu -->
  <string name="adv_mark_disabled">Выделить отключенные компоненты</string>
  <string name="adv_show_marked_lib">Показать отмеченные библиотеки</string>
  <string name="adv_show_system_framework_apps">Показать приложения фреймворка</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Показать время обновления</string>
  <string name="snapshot_menu_hide_no_component_changes">Скрыть компоненты без изменений</string>
  <string name="snapshot_menu_diff_highlight">Показать различия</string>
</resources>
