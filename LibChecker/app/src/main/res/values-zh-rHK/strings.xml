<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">應用程式</string>
  <string name="title_statistics">統計資料</string>
  <string name="title_snapshot">快照</string>
  <string name="title_settings">設定</string>
  <string name="title_album">相簿</string>
  <string name="loading">載入中…</string>
  <string name="channel_shoot">儲存快照</string>
  <string name="noti_shoot_title">正在儲存目前快照</string>
  <string name="noti_shoot_title_saved">快照已儲存</string>
  <!--  App list  -->
  <string name="menu_search">搜尋</string>
  <string name="menu_sort">排序</string>
  <string name="menu_filter">過濾</string>
  <string name="search_hint">搜尋…</string>
  <string name="string_64_bit">64 位元</string>
  <string name="string_32_bit">32 位元</string>
  <string name="no_libs">無原生庫</string>
  <string name="cannot_read">無法讀取</string>
  <string name="unknown">不明</string>
  <string name="empty_list">空白清單</string>
  <string name="uncharted_territory">未知領域</string>
  <string name="get_app_list_denied_tip">請授予 LibChecker\n「讀取應用程式清單」\n權限</string>
  <string name="advanced_menu">進階選單</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">%s架構的應用程式</string>
  <string name="title_statistics_dialog_no_native_libs">無原生庫的應用程式</string>
  <string name="tab_chart">圖表</string>
  <string name="tab_lib_reference_statistics">庫參考資料</string>
  <string name="not_marked_lib">未標示的庫</string>
  <string name="submenu_title_component">元件</string>
  <string name="submenu_title_manifest">資訊清單</string>
  <string name="ref_category_all">全部</string>
  <string name="ref_category_native">原生庫</string>
  <string name="ref_category_service">服務</string>
  <string name="ref_category_activity">活動</string>
  <string name="ref_category_br">廣播接收器</string>
  <string name="ref_category_cp">内容提供者</string>
  <string name="ref_category_perm">權限</string>
  <string name="ref_category_static">靜態庫</string>
  <string name="ref_category_metadata">中繼資料</string>
  <string name="ref_category_package">套件</string>
  <string name="ref_category_shared_uid">共用的 UID</string>
  <string name="ref_category_signatures">簽章</string>
  <string name="ref_category_only_not_marked">僅未標示的項目</string>
  <string name="string_kotlin_used">已使用 Kotlin</string>
  <string name="string_kotlin_unused">未使用 Kotlin</string>
  <string name="string_compose_used">已使用 Jetpack Compose</string>
  <string name="string_compose_unused">未使用 Jetpack Compose</string>
  <string name="android_dist_label">分布</string>
  <string name="android_dist_source">來源</string>
  <string name="android_dist_title">Android 版本發佈統計資料</string>
  <string name="android_dist_subtitle_format">更新時間：%s</string>
  <string name="chart_abi_detailed">詳細</string>
  <string name="chart_abi_concise">簡潔</string>
  <string name="chart_item_not_support">不支持</string>
  <!--  About  -->
  <string name="settings_about">關於</string>
  <string name="settings_about_summary">這裡是 LibChecker！</string>
  <string name="settings_translate">參與翻譯</string>
  <string name="settings_translate_summary">協助翻譯這個應用程式</string>
  <string name="settings_rate_us">給我們評分</string>
  <string name="settings_rate_us_summary">這可以使我們被更多的人發現</string>
  <string name="settings_get_updates">取得更新</string>
  <string name="settings_get_updates_summary">取得更加穩定並具有更多功能的最新版本</string>
  <string name="settings_get_updates_in_app_chip_stable">穩定版</string>
  <string name="about_info">本應用程式用於檢視裝置上的應用程式使用第三方程式庫的狀況。</string>
  <string name="toolbar_rate">評分</string>
  <string name="resource_declaration">應用程式中的部分資源來自於： </string>
  <string name="library_declaration">LibChecker 中所有標示的程式庫資訊均來自於其所屬的 SDK 的開發文件或程式碼存放庫，如果資訊有有誤，請聯絡：<EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">一般</string>
  <string name="pref_group_others">其他</string>
  <string name="apk_analytics">APK 分析</string>
  <string name="colorful_icon">多彩圖示</string>
  <string name="rules_repo_title">規則存放庫</string>
  <string name="lib_ref_threshold">庫參考閾值</string>
  <string name="languages">語言</string>
  <string name="reload_apps">重載應用程式清單</string>
  <string name="help_docs">說明文件</string>
  <string name="join_telegram_group">聯絡</string>
  <string name="anonymous_statistics">匿名統計資料</string>
  <string name="cloud_rules">雲端規則</string>
  <string name="dark_mode">深色模式</string>
  <string name="snapshot_keep">快照預設保留規則</string>
  <string name="apk_analytics_summary">按下 APK 檔案時開啟詳細資料頁面</string>
  <string name="colorful_icon_summary">部分標示庫會呈現為彩色標誌</string>
  <string name="lib_ref_threshold_summary">只有庫參考數量達到閾值的庫才顯示在清單中</string>
  <string name="reload_apps_summary">應用程式清單顯示不正常時嘗試此選項</string>
  <string name="help_docs_summary">學習如何使用 LibChecker</string>
  <string name="join_telegram_group_summary">一起來閒聊</string>
  <string name="anonymous_statistics_summary">我們使用 Google Firebase 來匿名傳送最常被人們使用的標示庫和一些使用習慣資料以使得 LibChecker 變得更加實用</string>
  <string name="cloud_rules_summary">在此更新最新的標示庫</string>
  <string name="array_dark_mode_off">關閉</string>
  <string name="array_dark_mode_on">開啟</string>
  <string name="array_dark_mode_auto">自動</string>
  <string name="array_dark_mode_system">系統預設</string>
  <string name="array_dark_mode_battery">由省電模式設定</string>
  <string name="array_snapshot_default">詢問</string>
  <string name="array_snapshot_keep">保留</string>
  <string name="array_snapshot_discard">捨棄</string>
  <!--  Detail  -->
  <string name="detail">詳細資料</string>
  <string name="detail_label">檢視應用程式詳細資料</string>
  <string name="not_found">未找到</string>
  <string name="create_an_issue">協助我們來補充資訊</string>
  <string name="app_bundle_details">Android App Bundle 是 Android 新推出的一種官方發佈格式，可讓您以更高效的方式開發並發佈應用程式。借助 Android App Bundle，您可以更輕鬆地以更小的應用程式提供優質的使用體驗，從而提升安裝成功率並減少解除安裝次數。轉換過程輕鬆便捷。您無需重構程式碼即可開始獲享較小應用程式的優勢。改用這種格式後，您可以體驗模塊化應用程式開發並自訂功能交付，並從中受益。</string>
  <string name="kotlin_details">Kotlin 是一種在 Java 虛擬機上執行的靜態類型程式設計語言。雖然與 Java 語法並不相容，但在 JVM 環境中 Kotlin 被設計成可以和 Java 程式碼相互運作，並可以重複使用如 Java 集合架構等的現有 Java 參考的函式庫。</string>
  <string name="items_count">項數：</string>
  <string name="further_operation">進一步作業</string>
  <string name="app_info_launch">啟動</string>
  <string name="app_info_settings">設定</string>
  <string name="lib_detail_dialog_title">庫詳細資料</string>
  <string name="lib_permission_dialog_title">權限詳細資料</string>
  <string name="agp_details">Android Studio 建置系統以 Gradle 為基礎，並且 Android Gradle 外掛程式新增了幾項專用於建置 Android 應用程式的功能。雖然 Android 外掛程式通常會與 Android Studio 的更新步調保持一致，但外掛程式 (以及 Gradle 系統的其餘部分) 可獨立於 Android Studio 執行並單獨更新。</string>
  <string name="xposed_module">Xposed 模組</string>
  <string name="xposed_module_details">Xposed 是一個模組架構，可以在不接觸任何 APK 的狀況下變更系統和應用程式的行為。這很棒，因為這意味著模組可以在不同版本甚至 ROM 上運作而無需任何變更。</string>
  <string name="play_app_signing">Play 應用程式簽署</string>
  <string name="play_app_signing_details">借助 Play 應用程式簽署，Google 可以為您管理並保護您應用程式的簽署金鑰，並使用它對從您的 App bundle 產生的最佳化分配 APK 進行簽署。Play App Signing 將您的應用程式簽署金鑰儲存在 Google 的安全基礎結構中，並提供升級選項以提高安全性。</string>
  <string name="pwa_details">漸進式 Web 應用程式 (PWA) 使用現代 API 建置並增強，以提供增強的功能、可靠性和可安裝性，同時使用單個程式碼存放庫在任何裝置上存取任何人、任何位置。</string>
  <string name="jetpack_compose_details">Jetpack Compose 是 Android 的新型工具組，用於建置原生 UI。可簡化並加快 Android 平台上的 UI 開發作業。透過較少的程式碼、強大的工具和直觀的 Kotlin API，快速將應用程式上架。</string>
  <string name="extract_native_libs_tip">此應用程式宣告了 </string>
  <string name="xml_detail">XML 詳細資料</string>
  <string name="format_last_updated">最近更新：%s</string>
  <string name="menu_process">處理程序</string>
  <string name="menu_split">分割</string>
  <string name="alternative_launch_method">替代啟動方法</string>
  <string name="compare_with_current">與目前套件比較</string>
  <string name="rx_detail">RxJava 是 Reactive Extensions 的 Java VM 實作：一個使用可觀察序列組成異步和基於活動的程式的庫。</string>
  <string name="rx_android_detail">適用於 Android 的 Reactive Extensions</string>
  <string name="rx_kotlin_detail">RxJava 的 Kotlin 擴展</string>
  <string name="permission_not_granted">未授予</string>
  <string name="signature_detail">簽章詳細資料</string>
  <string name="signature_version">版本</string>
  <string name="signature_serial_number">序號</string>
  <string name="signature_issuer">簽發者</string>
  <string name="signature_subject">主體</string>
  <string name="signature_validity_not_before">有效期始</string>
  <string name="signature_validity_not_after">有效期至</string>
  <string name="signature_public_key_format">公開金鑰格式</string>
  <string name="signature_public_key_algorithm">公開金鑰演算法</string>
  <string name="signature_public_key_exponent">公開金鑰指數</string>
  <string name="signature_public_key_modulus_size">公開金鑰模數大小</string>
  <string name="signature_public_key_modulus">公開金鑰模數</string>
  <string name="signature_public_key_y">公開金鑰值 Y</string>
  <string name="signature_public_key_type">公開金鑰類型</string>
  <string name="signature_algorithm_name">簽章演算法名稱</string>
  <string name="signature_algorithm_oid">簽章演算法 OID</string>
  <string name="lib_detail_label_tip">標籤</string>
  <string name="lib_detail_develop_team_tip">開發團隊</string>
  <string name="lib_detail_rule_contributors_tip">規則貢獻者</string>
  <string name="lib_detail_description_tip">描述</string>
  <string name="lib_detail_relative_link_tip">相對連結</string>
  <string name="lib_detail_last_update_tip">更新於</string>
  <string name="lib_detail_app_props_title">應用程式屬性</string>
  <string name="lib_detail_app_props_tip">更多資訊</string>
  <string name="lib_detail_xposed_min_version">Min 版本</string>
  <string name="lib_detail_xposed_default_scope">預設範圍</string>
  <string name="lib_detail_xposed_init_class">初始類別</string>
  <string name="lib_detail_app_install_source_title">安裝來源</string>
  <string name="lib_detail_app_install_source_originating_package">安裝要求者</string>
  <string name="lib_detail_app_install_source_installing_package">安裝執行程式</string>
  <string name="lib_detail_app_installed_time">安裝時間</string>
  <string name="lib_detail_app_install_source_empty">未知</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell 或已解除安裝的應用程式</string>
  <string name="lib_detail_app_install_source_shizuku_usage">因為 Android 限制了取得安裝要求者的 API，我們使用 Shizuku 以取得安裝要求者。</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku 尚未安裝</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">按一下這裡以安裝 Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">需要 Shizuku API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">按一下這裡以更新 Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku 未在執行</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">按一下這裡以跳轉到 Shizuku 以將其啟動</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku 未經授權</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">按一下這裡進行 Shizuku 授權以取得安裝要求者</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">已複製到剪貼簿</string>
  <string name="toast_not_existing_market">裝置中不存在應用市集</string>
  <string name="toast_use_another_file_manager">請使用其它檔案管理員開啟此 APK</string>
  <string name="toast_cant_open_app">無法啟動此應用程式</string>
  <string name="toast_cloud_rules_update_error">無法更新雲端規則，請再試一次</string>
  <string name="toast_not_enough_storage_space">儲存空間不足</string>
  <string name="toast_downloading_app">已要求下載</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">儲存目前快照</string>
  <string name="snapshot_current_timestamp">目前快照時間戳記</string>
  <string name="snapshot_apps_count">快照應用程式數量 / 應用程式數量</string>
  <string name="comparison_snapshot_apps_count">快照應用程式數量</string>
  <string name="snapshot_indicator_added">新增</string>
  <string name="snapshot_indicator_removed">移除</string>
  <string name="snapshot_indicator_changed">變更</string>
  <string name="snapshot_indicator_moved">移動</string>
  <string name="snapshot_empty_list_title">沒有元件變更</string>
  <string name="snapshot_no_snapshot">沒有快照</string>
  <string name="snapshot_detail_new_install_title">這是一個全新的應用程式</string>
  <string name="snapshot_detail_deleted_title">此應用程式已被移除</string>
  <string name="snapshot_time_node_uninitialized">(未初始化)</string>
  <string name="snapshot_preinstalled_app">內建應用程式</string>
  <string name="snapshot_generate_text_report">產生文字報告</string>
  <string name="snapshot_scheme_tip"><![CDATA[提示：現在您可以透過<br><b>%s</b><br>在背景產生快照。]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">確定要重新載入應用程式清單嗎？</string>
  <string name="dialog_subtitle_reload_apps">這可能會花費幾秒鐘的時間</string>
  <string name="dialog_title_change_timestamp">變更時間節點</string>
  <string name="dialog_title_keep_previous_snapshot">注意</string>
  <string name="dialog_message_keep_previous_snapshot">是否保留上一張快照？</string>
  <string name="dialog_title_select_to_delete">選擇日期以刪除快照</string>
  <string name="dialog_title_confirm_to_delete">您確定要刪除這個快照嗎？</string>
  <string name="btn_keep">保留</string>
  <string name="btn_drop">捨棄</string>
  <string name="dialog_title_compare_diff_apk">注意</string>
  <string name="dialog_message_compare_diff_apk">要比較兩個不同的 APK 嗎？</string>
  <!--  Album  -->
  <string name="album_compare">比較</string>
  <string name="album_item_comparison_title">比較</string>
  <string name="album_item_comparison_subtitle">比較兩個快照</string>
  <string name="album_item_comparison_invalid_compare">無效的比較</string>
  <string name="album_item_comparison_invalid_shared_items">請選取兩個 APK 檔案進行比較</string>
  <string name="album_item_comparison_choose_local_apk">選擇本機 APK</string>
  <string name="album_item_management_title">管理</string>
  <string name="album_item_management_subtitle">管理所有快照</string>
  <string name="album_item_backup_restore_title">備份與還原</string>
  <string name="album_item_backup_restore_subtitle">備份與還原快照</string>
  <string name="album_item_track_title">追蹤</string>
  <string name="album_item_track_subtitle">選取應用程式強制進行比較變更</string>
  <string name="album_backup">備份</string>
  <string name="album_backup_summary">備份所有快照</string>
  <string name="album_restore">還原</string>
  <string name="album_restore_summary">從備份檔案還原快照</string>
  <string name="album_restore_detail">%1$s：總計 %2$s 個項目\n</string>
  <string name="album_click_to_choose">按下以選擇</string>
  <string name="album_dialog_delete_snapshot_message">處理中…</string>
  <string name="album_snapshot_top_apps_not_initialized">尚未加载</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">使用「大聖淨化」封鎖此元件</string>
  <string name="integration_monkey_king_menu_unblock">使用「大聖淨化」解除封鎖此元件</string>
  <string name="integration_blocker_menu_block">使用「Blocker」封鎖此元件</string>
  <string name="integration_blocker_menu_unblock">使用「Blocker」解除封鎖此元件</string>
  <string name="integration_anywhere_menu_editor">在「Anywhere- 編輯器」中開啟</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">本機存放庫版本</string>
  <string name="rules_remote_repo_version">遠端存放庫版本</string>
  <string name="rules_btn_restart_to_update">重啟以更新</string>
  <string name="rules_btn_update">更新</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">顯示系統應用程式</string>
  <string name="adv_show_overlays">顯示 Overlay</string>
  <string name="adv_show_64_bit">顯示 64 位元應用程式</string>
  <string name="adv_show_32_bit">顯示 32 位元應用程式</string>
  <string name="adv_sort_mode">排序模式</string>
  <string name="adv_sort_by_time">更新時間</string>
  <string name="adv_sort_by_target_version">Target 版本</string>
  <string name="adv_sort_by_name">名稱</string>
  <string name="adv_show_android_version">顯示 Android 版本</string>
  <string name="adv_show_target_version">顯示 Target 版本</string>
  <string name="adv_show_min_version">顯示 Min 版本</string>
  <string name="adv_show_compile_version">顯示 Compile 版本</string>
  <string name="adv_tint_abi_label">ABI 標籤染色</string>
  <string name="adv_mark_exported">標示已匯出的元件</string>
  <string name="adv_mark_disabled">標示已停用的元件</string>
  <string name="adv_show_marked_lib">顯示已標示的庫</string>
  <string name="adv_show_system_framework_apps">顯示架構應用程式</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">顯示更新時間</string>
  <string name="snapshot_menu_hide_no_component_changes">隱藏沒有元件變更</string>
  <string name="snapshot_menu_diff_highlight">重點標示差異</string>
</resources>
