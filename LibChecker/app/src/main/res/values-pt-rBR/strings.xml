<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Aplicativos</string>
  <string name="title_statistics">Estatísticas</string>
  <string name="title_snapshot">Captura de tela</string>
  <string name="title_settings">Configurações</string>
  <string name="title_album">Álbum</string>
  <string name="loading">Carregando!!!</string>
  <string name="channel_shoot">Salvar fotos</string>
  <string name="noti_shoot_title">Salvando snapshot atual</string>
  <string name="noti_shoot_title_saved">Um snapshot foi salvo</string>
  <!--  App list  -->
  <string name="menu_search">Procurar</string>
  <string name="menu_sort">Classificar</string>
  <string name="menu_filter">Filtrar</string>
  <string name="search_hint">Procurar…</string>
  <string name="string_64_bit">64 bits</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs">Sem bibliotecas nativas</string>
  <string name="cannot_read">Não é possível ler</string>
  <string name="unknown">Desconhecido</string>
  <string name="empty_list">Lista vazia</string>
  <string name="uncharted_territory">Território desconhecido</string>
  <string name="get_app_list_denied_tip">Por favor, conceda a\n\"Obter informação de aplicativos instalados\"\npermissão para LibChecker</string>
  <string name="advanced_menu">Menu Avançado</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">Apps com ABI %s</string>
  <string name="title_statistics_dialog_no_native_libs">Apps sem bibliotecas nativas</string>
  <string name="tab_chart">Plano</string>
  <string name="tab_lib_reference_statistics">Lib reference</string>
  <string name="not_marked_lib">Biblioteca Desmarcada</string>
  <string name="submenu_title_component">Componente</string>
  <string name="submenu_title_manifest">Manifesto</string>
  <string name="ref_category_all">Todas as opções</string>
  <string name="ref_category_native">Bibliotecas Nativas</string>
  <string name="ref_category_service">Serviços</string>
  <string name="ref_category_activity">Evento</string>
  <string name="ref_category_br">Transmitir Recepdores</string>
  <string name="ref_category_cp">Provedores de conteúdo</string>
  <string name="ref_category_perm">Permissões</string>
  <string name="ref_category_static">Bibliotecas Estáticas</string>
  <string name="ref_category_metadata">Metadados</string>
  <string name="ref_category_package">Pacote</string>
  <string name="ref_category_shared_uid">UID compartilhado</string>
  <string name="ref_category_signatures">Assinaturas</string>
  <string name="ref_category_only_not_marked">Só não marcado</string>
  <string name="string_kotlin_used">Kotlin usado</string>
  <string name="string_kotlin_unused">Kotlin não usado</string>
  <string name="string_compose_used">Compor Jetpack usado</string>
  <string name="string_compose_unused">Compor Jetpack não utilizado</string>
  <string name="android_dist_label">Distribuição</string>
  <string name="android_dist_source">Fonte</string>
  <string name="android_dist_title">Estatísticas de Distribuição de Versão Android</string>
  <string name="android_dist_subtitle_format">Tempo de atualização: %s</string>
  <string name="chart_abi_detailed">Detalhado</string>
  <string name="chart_abi_concise">Conciso</string>
  <!--  About  -->
  <string name="settings_about">Sobre</string>
  <string name="settings_about_summary">Isto é LibChecker!</string>
  <string name="settings_translate">Participar na tradução</string>
  <string name="settings_translate_summary">Ajude-nos a traduzir este app</string>
  <string name="settings_rate_us">Avalie-nos</string>
  <string name="settings_rate_us_summary">Isso pode nos fazer encontrar por mais pessoas</string>
  <string name="settings_get_updates">Receber Atualizações</string>
  <string name="settings_get_updates_summary">Obtenha a última versão que é mais estável e empacotado com recursos</string>
  <string name="settings_get_updates_in_app">No aplicativo</string>
  <string name="settings_get_updates_in_app_chip_stable">Estável</string>
  <string name="settings_get_updates_in_app_chip_ci">IC</string>
  <string name="about_info">Este aplicativo é usado para ver as bibliotecas de terceiros usadas por aplicativos no seu dispositivo.</string>
  <string name="toolbar_rate">Avalie</string>
  <string name="resource_declaration">Parte dos recursos do aplicativo vem de: </string>
  <string name="library_declaration">Todas as informações de bibliotecas marcadas no LibChecker vêm da documentação de desenvolvimento ou do repositório de código do SDK ao qual pertence. Se a informação estiver incorreta, por favor contacte: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Normal</string>
  <string name="pref_group_others">Outros</string>
  <string name="apk_analytics">Análise do APK</string>
  <string name="colorful_icon">Ícones Coloridos</string>
  <string name="rules_repo_title">Repositório de regras</string>
  <string name="lib_ref_threshold">Limite de Referências na Biblioteca</string>
  <string name="languages">Idiomas</string>
  <string name="reload_apps">Recarregar lista de aplicativos</string>
  <string name="help_docs">Documentos de ajuda</string>
  <string name="join_telegram_group">Comunicação</string>
  <string name="anonymous_statistics">Estatísticas anônimas</string>
  <string name="cloud_rules">Regras da nuvem</string>
  <string name="dark_mode">Modo Escuro</string>
  <string name="snapshot_keep">Regra padrão de snapshot de manutenção</string>
  <string name="apk_analytics_summary">Abrir a página de detalhes quando clicar em um arquivo APK</string>
  <string name="colorful_icon_summary">Parte das bibliotecas marcadas aparecerá como um logotipo colorido</string>
  <string name="lib_ref_threshold_summary">Apenas bibliotecas com o número de referências de biblioteca que chegam ao limite são exibidas na lista</string>
  <string name="reload_apps_summary">Experimente quando a lista de aplicativos é mostrada anormalmente</string>
  <string name="help_docs_summary">Aprenda a usar o LibChecker</string>
  <string name="join_telegram_group_summary">Junte-se ao nosso grupo do Telegram</string>
  <string name="anonymous_statistics_summary">Usamos o Google Firebase para enviar anonimamente as bibliotecas marcadas mais usadas e alguns dados de uso para tornar o LibChecker mais prático</string>
  <string name="cloud_rules_summary">Atualizar as últimas regras da biblioteca marcada aqui</string>
  <string name="array_dark_mode_off">Desativada</string>
  <string name="array_dark_mode_on">Ativada</string>
  <string name="array_dark_mode_auto">Automático</string>
  <string name="array_dark_mode_system">Padrão do sistema</string>
  <string name="array_dark_mode_battery">Definido pela Economia de Bateria</string>
  <string name="array_snapshot_default">Notificar</string>
  <string name="array_snapshot_keep">Manter</string>
  <string name="array_snapshot_discard">Descartar</string>
  <!--  Detail  -->
  <string name="detail">Detalhes</string>
  <string name="detail_label">Ver detalhes do aplicativo</string>
  <string name="not_found">Não encontrado</string>
  <string name="create_an_issue">Ajude-nos a complementar a informação</string>
  <string name="app_bundle_details">O Android App Bundle é o novo formato de publicação oficial do Android que oferece uma maneira mais eficiente de construir e lançar o seu aplicativo. O Pacote de Aplicativos Android permite que você ofereça uma ótima experiência em um tamanho de aplicativos menor, o que pode melhorar o sucesso de instalação e reduzir as desinstalações. É fácil mudar. Você não precisa refatorar seu código para começar a se beneficiar de um aplicativo menor. E depois de mudar, você se beneficiará do desenvolvimento de aplicativos modulares e da entrega de recursos personalizáveis.</string>
  <string name="kotlin_details">Kotlin é uma linguagem de programação multiplataforma, digitada estatisticamente, de uso geral com inferência de tipo. O Kotlin foi projetado para interoperar totalmente com Java, e a versão JVM da biblioteca padrão do Kotlin depende da biblioteca da Java Class Library, mas a inferência de tipo permite que sua sintaxe seja mais concisa.</string>
  <string name="items_count">Contagem de itens: </string>
  <string name="further_operation">Operações adicionais</string>
  <string name="app_info_launch">Executar</string>
  <string name="app_info_settings">Configurações</string>
  <string name="lib_detail_dialog_title">Detalhes de biblioteca</string>
  <string name="lib_permission_dialog_title">Detalhes da permissão</string>
  <string name="agp_details">O sistema de construção do Android Studio é baseado no Gradle e o plugin Android Gradle adiciona vários recursos específicos para a construção de aplicativos Android. Embora o plugin Android seja normalmente atualizado na etapa de bloqueio do Android Studio, o plugin (e o resto do sistema Gradle) pode rodar independente do Android Studio e ser atualizado separadamente.</string>
  <string name="xposed_module">Módulo Xposed</string>
  <string name="xposed_module_details">Xposed é uma estrutura para módulos que podem alterar o comportamento do sistema e de aplicativos sem tocar em nenhum APK. Isso é ótimo porque significa que os módulos podem funcionar para diferentes versões e até mesmo para ROMs sem quaisquer alterações.</string>
  <string name="play_app_signing">Iniciar assinatura do aplicativo</string>
  <string name="play_app_signing_details">Com a assinatura do Play App, o Google gerencia e protege a chave de assinatura do seu aplicativo para você e o usa para assinar otimizado, APKs de distribuição gerados a partir de pacotes de aplicações. O Play App Signing armazena sua chave de assinatura na infraestrutura segura do Google e oferece opções para aumentar a segurança.</string>
  <string name="pwa_details">Aplicativos Web Progressive (PWA) são construídos e aprimorados com APIs modernas para fornecer recursos aprimorados, confiabilidade e instalação enquanto chega a qualquer um, em qualquer lugar, em qualquer dispositivo com um único código.</string>
  <string name="jetpack_compose_details">Jetpack Compose é o moderno kit de ferramentas do Android para a construção de interface nativa. Ele simplifica e acelera o desenvolvimento de interface do usuário no Android. Rapidamente dê vida ao seu app com menos código, ferramentas poderosas e APIs intuitivas do Kotlin.</string>
  <string name="extract_native_libs_tip">O aplicativo declara </string>
  <string name="xml_detail">Detalhes do XML</string>
  <string name="lib_detail_dialog_title_16kb_page_size">Tamanho da página 16 KB</string>
  <string name="lib_detail_dialog_content_16kb_page_size">Historicamente, o Android só suporta páginas com tamanho de 4 KB de memória, o que optimiza a performance do sistema de memória para a quantia normal de da memória total que os dispositivos Android normalmente tem. Começando com o Android 15, AOSP suporta dispositivos que estão configurados para utilizar o tamanho de página de 16 KB (dispositivos de 16 KB). Se o seu app utiliza qualquer biblioteca NDK, tanto direta quando indiretamente através de um SDK, então você irá precisar reconstruir seu app para isso funcionar nesses dispositivos de 16 KB.</string>
  <string name="format_last_updated">Atualizado em: %s</string>
  <string name="menu_process">Processo</string>
  <string name="menu_split">Separar em categorias</string>
  <string name="alternative_launch_method">Método de Início Alternativo</string>
  <string name="compare_with_current">Compare com o pacote atual</string>
  <string name="rx_detail">RxJava é uma implementação Java VM de Extensões Reativas: uma biblioteca para a composição de programas assíncronos e baseados em eventos usando sequências observáveis.</string>
  <string name="rx_android_detail">Extensões Reativas para Android</string>
  <string name="rx_kotlin_detail">Extensões do Kotlin para RxJava</string>
  <string name="permission_not_granted">Não concedido</string>
  <string name="signature_detail">Detalhes da Assinatura</string>
  <string name="signature_version">Versão</string>
  <string name="signature_serial_number">Número de série</string>
  <string name="signature_issuer">Emissor</string>
  <string name="signature_subject">Assunto</string>
  <string name="signature_validity_not_before">Validade não anterior</string>
  <string name="signature_validity_not_after">Validade não após</string>
  <string name="signature_public_key_format">Formato de chave pública</string>
  <string name="signature_public_key_algorithm">Algorítimo de chave pública</string>
  <string name="signature_public_key_exponent">Chave pública Exponente</string>
  <string name="signature_public_key_modulus_size">Tamanho do módulo de chave pública</string>
  <string name="signature_public_key_modulus">Módulo de chave pública</string>
  <string name="signature_public_key_y">Valor da chave pública Y</string>
  <string name="signature_public_key_type">Tipo de chave pública</string>
  <string name="signature_algorithm_name">Nome do Algoritmo da Assinatura</string>
  <string name="signature_algorithm_oid">OID Algoritmo da assinatura</string>
  <string name="lib_detail_label_tip">Descrição</string>
  <string name="lib_detail_develop_team_tip">Equipe de Desenvolvedores</string>
  <string name="lib_detail_rule_contributors_tip">Colaborador(es) de regras</string>
  <string name="lib_detail_description_tip">Descriçâo</string>
  <string name="lib_detail_relative_link_tip">Link relativo</string>
  <string name="lib_detail_last_update_tip">Atualizado em</string>
  <string name="lib_detail_app_props_title">Propriedades do aplicativo</string>
  <string name="lib_detail_app_props_tip">Mais informações</string>
  <string name="lib_detail_xposed_min_version">Versão Mínima</string>
  <string name="lib_detail_xposed_default_scope">Escopo padrão</string>
  <string name="lib_detail_xposed_init_class">Iniciar Classe</string>
  <string name="lib_detail_app_install_source_title">Origem da Instalação</string>
  <string name="lib_detail_app_install_source_originating_package">Solicitante de instalação</string>
  <string name="lib_detail_app_install_source_installing_package">Executar Instalação</string>
  <string name="lib_detail_app_install_source_empty">Desconhecido</string>
  <string name="lib_detail_app_install_source_empty_detail">aplicativo adb ou desinstalado</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Como o Android restringe a API para obter o solicitante de instalação, usamos Shizuku para obter o solicitante de instalação.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku não instalada</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">Clique aqui para instalar Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Requer Shizuku API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">Clique aqui para atualizar o Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku não está rodando</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">Clique aqui para pular para o Shizuku para começa-lo</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku não autorizado</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">Clique aqui para autorização Shizuku para requisitador de instalação</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Copiado para área de transferência</string>
  <string name="toast_not_existing_market">Não há nenhum mercado de aplicativos existente</string>
  <string name="toast_use_another_file_manager">Por favor, utilize outro administrador de arquivos para abrir o APK</string>
  <string name="toast_cant_open_app">Não é possível abrir este app</string>
  <string name="toast_cloud_rules_update_error">Falha ao atualizar as regras da nuvem. Por favor, tente novamente.</string>
  <string name="toast_not_enough_storage_space">Não há espaço suficiente no armazenamento</string>
  <string name="toast_downloading_app">Download solicitado</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Salve a snapshot atual</string>
  <string name="snapshot_current_timestamp">Timestamp atual de Snapshot</string>
  <string name="snapshot_apps_count">Contagem de Apps em Snapshot / Contagem de Apps</string>
  <string name="comparison_snapshot_apps_count">Contagem de Apps em Snapshot</string>
  <string name="snapshot_indicator_added">Adicionado</string>
  <string name="snapshot_indicator_removed">Removido</string>
  <string name="snapshot_indicator_changed">Alterado</string>
  <string name="snapshot_indicator_moved">Movido</string>
  <string name="snapshot_empty_list_title">Sem mudanças de componentes</string>
  <string name="snapshot_no_snapshot">Sem Snapshot</string>
  <string name="snapshot_detail_new_install_title">Esse aplicativo foi instalado recentemente</string>
  <string name="snapshot_detail_deleted_title">Esse aplicativo foi removido</string>
  <string name="snapshot_time_node_uninitialized">(Não inicializado)</string>
  <string name="snapshot_preinstalled_app">Aplicativo pré instalado</string>
  <string name="snapshot_generate_text_report">Gerar relatório de texto</string>
  <string name="snapshot_scheme_tip"><![CDATA[Agora você pode gerar snapshots no plano de fundo via:<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Tem certeza que quer recarregar todos os apps?</string>
  <string name="dialog_subtitle_reload_apps">Isso pode levar alguns segundos</string>
  <string name="dialog_title_change_timestamp">Alterar nó de hora</string>
  <string name="dialog_title_keep_previous_snapshot">Atenção</string>
  <string name="dialog_message_keep_previous_snapshot">Você gostaria de manter a snapshot anterior?</string>
  <string name="dialog_title_select_to_delete">Encontre uma data para deletar a Snapshot</string>
  <string name="dialog_title_confirm_to_delete">Você tem certeza que gostaria de deletar essa snapshot?</string>
  <string name="btn_keep">Manter</string>
  <string name="btn_drop">Discartar</string>
  <string name="dialog_title_compare_diff_apk">Atenção</string>
  <string name="dialog_message_compare_diff_apk">Você gostaria de comparar os dois diferentes APKs?</string>
  <!--  Album  -->
  <string name="album_compare">Comparar</string>
  <string name="album_item_comparison_title">Compararão</string>
  <string name="album_item_comparison_subtitle">Compare como duas snapshots</string>
  <string name="album_item_comparison_invalid_compare">Comparação invalida</string>
  <string name="album_item_comparison_invalid_shared_items">Por favor, selecione os dois arquivos APK para comparação</string>
  <string name="album_item_comparison_choose_local_apk">Escolha um APK local</string>
  <string name="album_item_management_title">Gerenciamento</string>
  <string name="album_item_management_subtitle">Gerencie todas as snapshots</string>
  <string name="album_item_backup_restore_title">Backup &amp; Restaurar</string>
  <string name="album_item_backup_restore_subtitle">Fazer backup e restaurar snapshots</string>
  <string name="album_item_track_title">Rastrear</string>
  <string name="album_item_track_subtitle">Selecionar aplicativos para forçar uma mudança de comparação</string>
  <string name="album_backup">Cópia de Segurança</string>
  <string name="album_backup_summary">Backup de todos os instantâneos</string>
  <string name="album_restore">Restaurar</string>
  <string name="album_restore_summary">Restaurar instantâneos do arquivo de backup</string>
  <string name="album_restore_detail">%1$s : %2$s itens no total\n</string>
  <string name="album_click_to_choose">Clique para Selecionar</string>
  <string name="album_dialog_delete_snapshot_message">Processando…</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Bloqueie através do \"Rei Purificado\"</string>
  <string name="integration_monkey_king_menu_unblock">Desbloqueie através do \"Rei Purificado\"</string>
  <string name="integration_blocker_menu_block">Bloqueie através do \"Bloqueador\"</string>
  <string name="integration_blocker_menu_unblock">Desbloquear através de \"Bloqueador\"</string>
  <string name="integration_anywhere_menu_editor">Abra-o em \"Em todo lugar- Editor\"</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Versão local do repo</string>
  <string name="rules_remote_repo_version">Versão remota do repo</string>
  <string name="rules_btn_restart_to_update">Reiniciar para Atualizar</string>
  <string name="rules_btn_update">Atualizar</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">Mostrar Aplicativos do Sistema</string>
  <string name="adv_show_overlays">Mostrar camadas</string>
  <string name="adv_show_64_bit">Mostrar aplicativos 64-bit</string>
  <string name="adv_show_32_bit">Mostrar aplicativos 32-bit</string>
  <string name="adv_sort_mode">Ordenar Campos</string>
  <string name="adv_sort_by_time">Atualizado Time</string>
  <string name="adv_sort_by_target_version">Versão Alvo</string>
  <string name="adv_sort_by_name">Nome</string>
  <string name="adv_show_android_version">Mostrar Versão do Android</string>
  <string name="adv_show_target_version">Mostrar Versão Alvo</string>
  <string name="adv_show_min_version">Mostrar Versão Mínima</string>
  <string name="adv_show_compile_version">Mostre versão compilada</string>
  <string name="adv_tint_abi_label">Tintura ABI Rótulo</string>
  <string name="adv_mark_exported">Marcar componente exportado</string>
  <string name="adv_mark_disabled">Marcar Componente Desabilitado</string>
  <string name="adv_show_marked_lib">Mostrar bibliotecas marcadas</string>
  <string name="adv_show_system_framework_apps">Mostrar Aplicativos Framework</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Mostrar data de atualização</string>
  <string name="snapshot_menu_hide_no_component_changes">Não ocultar nenhuma alteração de componente</string>
  <string name="snapshot_menu_diff_highlight">Realce do diff</string>
</resources>
