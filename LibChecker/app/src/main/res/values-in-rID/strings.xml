<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Aplikasi</string>
  <string name="title_statistics">Statistik</string>
  <string name="title_snapshot">Snapshot</string>
  <string name="title_settings">Setelan</string>
  <string name="title_album">Album</string>
  <string name="loading">Memuat…</string>
  <string name="channel_shoot">Simpan snapshot</string>
  <string name="noti_shoot_title">Menyimpan snapshot terkini</string>
  <string name="noti_shoot_title_saved">Snapshot telah disimpan</string>
  <!--  App list  -->
  <string name="menu_search">Cari</string>
  <string name="menu_sort">Urutan</string>
  <string name="menu_filter">Saring</string>
  <string name="search_hint">Cari…</string>
  <string name="string_64_bit">64bit</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs">Tak ada lib native</string>
  <string name="cannot_read">Tak dapat membaca</string>
  <string name="unknown">Tidak diketahui</string>
  <string name="empty_list">Daftar kosong</string>
  <string name="uncharted_territory">Territory tidak terpetakan</string>
  <string name="get_app_list_denied_tip">Tolong izinkan\n\"Dapatkan info tentang aplikasi yang terinstal\"\nizin untuk LibChecker</string>
  <string name="advanced_menu">Menu Lanjutan</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">Aplikasi dengan ABI %s</string>
  <string name="title_statistics_dialog_no_native_libs">Aplikasi tanpa library native</string>
  <string name="tab_chart">Chart</string>
  <string name="tab_lib_reference_statistics">Referensi lib</string>
  <string name="not_marked_lib">Library tak ditandai</string>
  <string name="submenu_title_component">Component</string>
  <string name="submenu_title_manifest">Manifest</string>
  <string name="ref_category_all">Semua</string>
  <string name="ref_category_native">Library Native</string>
  <string name="ref_category_service">Layanan</string>
  <string name="ref_category_activity">Aktivitas</string>
  <string name="ref_category_br">Receiver Broadcast</string>
  <string name="ref_category_cp">Provider Konten</string>
  <string name="ref_category_perm">Perizinan</string>
  <string name="ref_category_static">Library Statik</string>
  <string name="ref_category_metadata">Meta Data</string>
  <string name="ref_category_package">Paket</string>
  <string name="ref_category_shared_uid">UID Shared</string>
  <string name="ref_category_signatures">Tandatangan</string>
  <string name="ref_category_only_not_marked">Hanya Tidak Ditandai</string>
  <string name="string_kotlin_used">Menggunakan Kotlin</string>
  <string name="string_kotlin_unused">Tak menggunakan Kotlin</string>
  <string name="string_compose_used">Menggunakan Jetpack Compose</string>
  <string name="string_compose_unused">Tak menggunakan Jetpack Compose</string>
  <string name="android_dist_source">Sumber</string>
  <string name="android_dist_title">Statistik Distribusi Versi Android</string>
  <string name="android_dist_subtitle_format">Waktu Pembaruan: %s</string>
  <!--  About  -->
  <string name="settings_about">Tentang</string>
  <string name="settings_about_summary">Inilah LibChecker!</string>
  <string name="settings_translate">Partisipasi dalam penejermahan</string>
  <string name="settings_translate_summary">Bantu kami menerjemahkan aplikasi ini</string>
  <string name="settings_rate_us">Nilai kami</string>
  <string name="settings_rate_us_summary">Ini dapat membuat kami dikenal lebih banyak orang</string>
  <string name="settings_get_updates">Dapatkan Pembaruan</string>
  <string name="settings_get_updates_summary">Dapatkan versi terbaru yang lebih stabil dan penuh dengan fitur</string>
  <string name="about_info">Aplikasi ini digunakan untuk melihat library pihak ketiga yang digunakan oleh aplikasi di perangkat Anda.</string>
  <string name="toolbar_rate">Nilai</string>
  <string name="resource_declaration">Sebagian resource aplikasi berasal dari: </string>
  <string name="library_declaration">Semua informasi library yang ditandai di LibChecker berasal dari dokumentasi pengembangan atau repositori kode dari SDK yang digunakan. Jika informasinya salah, silakan hubungi: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Normal</string>
  <string name="pref_group_others">Lainnya</string>
  <string name="apk_analytics">APK Analytics</string>
  <string name="colorful_icon">Ikon Berwarna</string>
  <string name="rules_repo_title">Rules Repo</string>
  <string name="lib_ref_threshold">Batas Referensi Library</string>
  <string name="languages">Bahasa</string>
  <string name="reload_apps">Muat Ulang Daftar Aplikasi</string>
  <string name="help_docs">Dok. Bantuan</string>
  <string name="join_telegram_group">Komunikasi</string>
  <string name="anonymous_statistics">Statistik Anonim</string>
  <string name="cloud_rules">Cloud Rules</string>
  <string name="dark_mode">Mode Gelap</string>
  <string name="snapshot_keep">Snapshot Default Keep Rule</string>
  <string name="apk_analytics_summary">Buka halaman detail saat mengklik file APK</string>
  <string name="colorful_icon_summary">Bagian dari library yang ditandai akan tampil dalam logo berwarna</string>
  <string name="lib_ref_threshold_summary">Hanya library dengan jumlah referensi library mencapai ambang batas yang ditampilkan dalam daftar</string>
  <string name="reload_apps_summary">Mencoba ketika daftar aplikasi ditampilkan secara tidak normal</string>
  <string name="help_docs_summary">Belajar cara menggunakan LibChecker</string>
  <string name="join_telegram_group_summary">Bergabung dengan grup Telegram kami</string>
  <string name="anonymous_statistics_summary">Kami menggunakan Google Firebase untuk mengirim secara anonim library yang paling sering digunakan dan beberapa data pola penggunaan untuk membuat LibChecker lebih fungsional</string>
  <string name="cloud_rules_summary">Perbarui rule library yang ditandai terbaru di sini</string>
  <string name="array_dark_mode_off">Mati</string>
  <string name="array_dark_mode_on">Hidup</string>
  <string name="array_dark_mode_auto">Otomatis</string>
  <string name="array_dark_mode_system">Bawaan Sistem</string>
  <string name="array_dark_mode_battery">Diatur oleh Penghemat Baterai</string>
  <string name="array_snapshot_default">Beritahu</string>
  <string name="array_snapshot_keep">Tetap</string>
  <string name="array_snapshot_discard">Buang</string>
  <!--  Detail  -->
  <string name="detail">Detail</string>
  <string name="detail_label">Lihat Detail Aplikasi</string>
  <string name="not_found">Tidak ditemukan</string>
  <string name="create_an_issue">Bantu kami untuk melengkapi informasi</string>
  <string name="app_bundle_details">Android App Bundle adalah format penerbitan resmi baru Android yang menawarkan cara yang lebih efisien untuk membuat dan merilis aplikasi Anda. Android App Bundle memungkinkan Anda memberikan pengalaman yang luar biasa dengan lebih mudah dalam ukuran aplikasi yang lebih kecil, yang dapat meningkatkan keberhasilan pemasangan dan mengurangi pencopotan pemasangan. Sangat mudah untuk beralih. Anda tidak perlu merefaktor kode Anda untuk mulai mendapatkan manfaat dari aplikasi yang lebih kecil. Dan setelah Anda beralih, Anda akan mendapatkan manfaat dari pengembangan aplikasi modular dan pengiriman fitur yang dapat disesuaikan.</string>
  <string name="kotlin_details">Kotlin adalah bahasa pemrograman lintas platform, diketik secara statis, dan memiliki inferensi tipe. Kotlin didesain untuk dapat beroperasi secara penuh dengan Java, dan versi JVM dari library standar Kotlin bergantung pada Java Class Library, tetapi inferensi tipe memungkinkan sintaksnya menjadi lebih ringkas.</string>
  <string name="items_count">Jumlah item: </string>
  <string name="further_operation">Operasi Lebih Lanjut</string>
  <string name="app_info_launch">Luncurkan</string>
  <string name="app_info_settings">Setelan</string>
  <string name="lib_detail_dialog_title">Detail Library</string>
  <string name="lib_permission_dialog_title">Detail Perizinan</string>
  <string name="agp_details">Sistem pembuatan Android Studio didasarkan pada Gradle, dan plugin Android Gradle menambahkan beberapa fitur khusus untuk membangun aplikasi Android. Meskipun plugin Android biasanya diperbarui secara bersamaan dengan Android Studio, plugin (dan sistem Gradle lainnya) dapat berjalan secara independen dari Android Studio dan diperbarui secara terpisah.</string>
  <string name="xposed_module">Modul Xposed</string>
  <string name="xposed_module_details">Xposed adalah kerangka kerja untuk modul yang dapat mengubah perilaku sistem dan aplikasi tanpa mempengaruhi APK apa pun. Ini sangat bagus karena berarti modul dapat bekerja pada versi yang berbeda dan bahkan ROM tanpa perubahan apa pun.</string>
  <string name="play_app_signing">Play App Signing</string>
  <string name="play_app_signing_details">Dengan Play App Signing, Google mengelola dan melindungi kunci penandatanganan aplikasi Anda untuk Anda dan menggunakannya untuk menandatangani APK yang dioptimalkan dan didistribusikan yang dihasilkan dari bundel aplikasi Anda. Play App Signing menyimpan kunci penandatanganan aplikasi Anda di infrastruktur Google yang aman dan menawarkan opsi peningkatan untuk meningkatkan keamanan.</string>
  <string name="pwa_details">Progressive Web Apps (PWA) dibangun dan disempurnakan dengan API modern untuk memberikan kemampuan, keandalan, dan kemudahan pemasangan yang lebih baik sekaligus menjangkau siapa pun, di mana pun, di perangkat apa pun dengan basis kode tunggal.</string>
  <string name="jetpack_compose_details">Jetpack Compose adalah toolkit modern Android untuk membangun UI native. Ini menyederhanakan dan mempercepat pengembangan UI di Android. Dengan cepat mewujudkan aplikasi Anda dengan kode yang lebih sedikit, alat yang powerful, dan API Kotlin yang intuitif.</string>
  <string name="extract_native_libs_tip">Aplikasi menyatakan </string>
  <string name="xml_detail">Detail XML</string>
  <string name="format_last_updated">Diperbarui pada: %s</string>
  <string name="menu_process">Proses</string>
  <string name="menu_split">Pisah</string>
  <string name="alternative_launch_method">Metode Peluncuran Alternatif</string>
  <string name="compare_with_current">Bandingkan dengan paket saat ini</string>
  <string name="rx_detail">RxJava adalah implementasi Java VM untuk Reactive Extensions: sebuah library untuk membuat program asynchronous dan event-based dengan menggunakan urutan yang dapat diobservasi.</string>
  <string name="rx_android_detail">Reactive Extensions for Android</string>
  <string name="rx_kotlin_detail">Kotlin Extensions for RxJava</string>
  <string name="permission_not_granted">Tidak Diberikan</string>
  <string name="signature_detail">Detail Tanda Tangan</string>
  <string name="signature_version">Versi</string>
  <string name="signature_serial_number">Nomor Seri</string>
  <string name="signature_issuer">Penerbit</string>
  <string name="signature_subject">Subjek</string>
  <string name="signature_validity_not_before">Validitas Tidak Sebelum</string>
  <string name="signature_validity_not_after">Validitas Tidak Setelah</string>
  <string name="signature_public_key_format">Format kunci publik</string>
  <string name="signature_public_key_algorithm">Algoritma kunci publik</string>
  <string name="signature_public_key_exponent">Eksponen kunci publik</string>
  <string name="signature_public_key_modulus_size">Ukuran Modulus kunci publik</string>
  <string name="signature_public_key_modulus">Modulus kunci publik</string>
  <string name="signature_public_key_y">Nilai kunci publik Y</string>
  <string name="signature_public_key_type">Tipe kunci publik</string>
  <string name="signature_algorithm_name">Nama Algoritma Tanda Tangan</string>
  <string name="signature_algorithm_oid">Algoritma Tanda Tangan OID</string>
  <string name="lib_detail_label_tip">Lebel</string>
  <string name="lib_detail_develop_team_tip">Tim Pengembang</string>
  <string name="lib_detail_rule_contributors_tip">Aturan Kontributor</string>
  <string name="lib_detail_description_tip">Deskripsi</string>
  <string name="lib_detail_relative_link_tip">Link Relatif</string>
  <string name="lib_detail_last_update_tip">Diperbarui pada</string>
  <string name="lib_detail_app_props_title">Properti Aplikasi</string>
  <string name="lib_detail_app_props_tip">Informasi lebih lanjut</string>
  <string name="lib_detail_xposed_min_version">Versi Min.</string>
  <string name="lib_detail_xposed_default_scope">Default Scope</string>
  <string name="lib_detail_xposed_init_class">Init Class</string>
  <string name="lib_detail_app_install_source_title">Sumber Instalasi</string>
  <string name="lib_detail_app_install_source_originating_package">Pengaju instalasi</string>
  <string name="lib_detail_app_install_source_installing_package">Eksekutor Instalasi</string>
  <string name="lib_detail_app_install_source_empty">Tidak diketahui</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell atau aplikasi yang dihapus instalasinya</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Karena Android membatasi API untuk mendapatkan pengaju instalasi, kami menggunakan Shizuku untuk mendapatkan pengaju instalasi.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku belum terpasang</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">Klik di sini untuk menginstal Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Memerlukan Shizuku API 10</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">Klik di sini untuk memperbarui Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku tidak berjalan</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">Klik di sini untuk beralih ke Shizuku dan memulainya</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Tidak diizinkan Shizuku</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">Klik di sini untuk permintaan otorisasi Shizuku untuk pengajuan instalasi</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Tersalin ke papan klip</string>
  <string name="toast_not_existing_market">Tidak ada di market aplikasi apa pun</string>
  <string name="toast_use_another_file_manager">Silakan gunakan File Manager lain untuk membuka APK</string>
  <string name="toast_cant_open_app">Tidak dapat membuka aplikasi ini</string>
  <string name="toast_cloud_rules_update_error">Gagal memperbarui rule cloud. Silakan coba lagi.</string>
  <string name="toast_not_enough_storage_space">Tidak cukup ruang penyimpanan</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Simpan snapshot terkini</string>
  <string name="snapshot_current_timestamp">Timestamp Snapshot Terkini</string>
  <string name="snapshot_apps_count">Jumlah Aplikasi dalam Snapshot / Jumlah dari Aplikasi</string>
  <string name="comparison_snapshot_apps_count">Jumlah Aplikasi dalam Snapshot</string>
  <string name="snapshot_indicator_added">Ditambahkan</string>
  <string name="snapshot_indicator_removed">Dihapus</string>
  <string name="snapshot_indicator_changed">Diubah</string>
  <string name="snapshot_indicator_moved">Dipindahkan</string>
  <string name="snapshot_empty_list_title">Tidak Ada Perubahan Komponen</string>
  <string name="snapshot_no_snapshot">Tidak ada Snapshot</string>
  <string name="snapshot_detail_new_install_title">Aplikasi ini baru saja dipasang</string>
  <string name="snapshot_detail_deleted_title">Aplikasi ini telah dihapus</string>
  <string name="snapshot_time_node_uninitialized">(Tidak diinisialisasi)</string>
  <string name="snapshot_preinstalled_app">Prainstal apl.</string>
  <string name="snapshot_generate_text_report">Buat Catatan Laporan</string>
  <string name="snapshot_scheme_tip"><![CDATA[Tip: Anda sekarang dapat menghasilkan snapshot di latar belakang via:<br><b>%s</b>]]></string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Apakah Anda yakin untuk memuat ulang semua aplikasi?</string>
  <string name="dialog_subtitle_reload_apps">Ini mungkin memerlukan beberapa detik</string>
  <string name="dialog_title_change_timestamp">Ubah Time Node</string>
  <string name="dialog_title_keep_previous_snapshot">Perhatian</string>
  <string name="dialog_message_keep_previous_snapshot">Apakah Anda ingin menyimpan snapshot sebelumnya?</string>
  <string name="dialog_title_select_to_delete">Pilih Tanggal untuk Menghapus Snapshot</string>
  <string name="dialog_title_confirm_to_delete">Apakah Anda yakin untuk menghapus snapshot ini?</string>
  <string name="btn_keep">Simpan</string>
  <string name="btn_drop">Buang</string>
  <string name="dialog_title_compare_diff_apk">Perhatian</string>
  <string name="dialog_message_compare_diff_apk">Apakah Anda ingin membandingkan dua APK yang berbeda?</string>
  <!--  Album  -->
  <string name="album_compare">Bandingkan</string>
  <string name="album_item_comparison_title">Perbandingan</string>
  <string name="album_item_comparison_subtitle">Bandingkan dengan dua snapshot</string>
  <string name="album_item_comparison_invalid_compare">Perbandingan tidak valid</string>
  <string name="album_item_comparison_invalid_shared_items">Pilih dua file APK untuk perbandingan</string>
  <string name="album_item_comparison_choose_local_apk">Pilih APK Lokal</string>
  <string name="album_item_management_title">Manajemen</string>
  <string name="album_item_management_subtitle">Kelola semua snapshot</string>
  <string name="album_item_backup_restore_title">Pencadangan &amp; Pemulihan</string>
  <string name="album_item_backup_restore_subtitle">Cadangkan dan pulihkan snapshot</string>
  <string name="album_item_track_title">Track</string>
  <string name="album_item_track_subtitle">Pilih aplikasi untuk menentukan perbandingan secara paksa</string>
  <string name="album_backup">Cadangkan</string>
  <string name="album_backup_summary">Cadangkan semua snapshot</string>
  <string name="album_restore">Pulihkan</string>
  <string name="album_restore_summary">Pulihkan snapshot dari file cadangan</string>
  <string name="album_restore_detail">%1$s: %2$s item secara total\n</string>
  <string name="album_click_to_choose">Klik untuk Memilih</string>
  <string name="album_dialog_delete_snapshot_message">Memproses…</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">Blokir via \"MonkeyKing Purify\"</string>
  <string name="integration_monkey_king_menu_unblock">Buka blokir via \"MonkeyKing Purify\"</string>
  <string name="integration_blocker_menu_block">Blokir via \"Blocker\"</string>
  <string name="integration_blocker_menu_unblock">Buka blokir via \"Blocker\"</string>
  <string name="integration_anywhere_menu_editor">Buka dalam \"Anywhere- Editor\"</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Versi repo lokal</string>
  <string name="rules_remote_repo_version">Versi repo remote</string>
  <string name="rules_btn_restart_to_update">Restart untuk Perbarui</string>
  <string name="rules_btn_update">Perbarui</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">Tampilkan Aplikasi sistem</string>
  <string name="adv_show_overlays">Tampilkan Overlay</string>
  <string name="adv_show_64_bit">Tampilkan Aplikasi 64-bit</string>
  <string name="adv_show_32_bit">Tampilkan Aplikasi 32-bit</string>
  <string name="adv_sort_mode">Mode Urutan</string>
  <string name="adv_sort_by_time">Waktu Diperbarui</string>
  <string name="adv_sort_by_target_version">Versi Target</string>
  <string name="adv_sort_by_name">Nama</string>
  <string name="adv_show_android_version">Tampilkan Versi Android</string>
  <string name="adv_show_target_version">Tampilkan Versi Target</string>
  <string name="adv_show_min_version">Tampilkan Versi Min.</string>
  <string name="adv_show_compile_version">Tampilkan Versi Kompiler</string>
  <string name="adv_tint_abi_label">Warnai Label ABI</string>
  <string name="adv_mark_exported">Tandai Komponen Ekspor</string>
  <string name="adv_mark_disabled">Tandai Komponen Dinonaktifkan</string>
  <string name="adv_show_marked_lib">Tampilkan Library Ditandai</string>
  <string name="adv_show_system_framework_apps">Tampilkan Aplikasi Framework</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Tampilkan Waktu Pembaruan</string>
  <string name="snapshot_menu_hide_no_component_changes">Sembunyikan Komponen Tanpa Perubahan</string>
  <string name="snapshot_menu_diff_highlight">Diff Highlight</string>
</resources>
