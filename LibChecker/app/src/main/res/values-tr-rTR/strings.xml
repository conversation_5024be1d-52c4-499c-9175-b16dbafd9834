<?xml version="1.0" encoding="utf-8"?>
<resources>
  <!--  General  -->
  <string name="title_app_list">Uygulamalar</string>
  <string name="title_statistics">İstatistikler</string>
  <string name="title_snapshot"><PERSON><PERSON><PERSON><PERSON>ü<PERSON></string>
  <string name="title_settings">Ayarlar</string>
  <string name="title_album">Albüm</string>
  <string name="loading">Yükleniyor…</string>
  <string name="channel_shoot">An<PERSON><PERSON>k görüntüleri kaydet</string>
  <string name="noti_shoot_title">Mevcut anlık görüntü kaydediliyor</string>
  <string name="noti_shoot_title_saved">Bir anlık görüntü kaydedildi</string>
  <!--  App list  -->
  <string name="menu_search">Ara</string>
  <string name="menu_sort">Sırala</string>
  <string name="menu_filter">Filtrele</string>
  <string name="search_hint">Ara…</string>
  <string name="string_64_bit">64bit</string>
  <string name="string_32_bit">32bit</string>
  <string name="no_libs"><PERSON><PERSON> kütüphane yok</string>
  <string name="cannot_read">Okunamıyor</string>
  <string name="unknown">Bilinmiyor</string>
  <string name="empty_list">Boş liste</string>
  <string name="uncharted_territory">Keşfedilmemiş bölge</string>
  <string name="get_app_list_denied_tip">Lütfen LibChecker\'a\n\"Yüklü uygulamalar hakkında bilgi al\"\niznini verin</string>
  <string name="advanced_menu">Gelişmiş Menü</string>
  <string name="archived_app">Arşivlenmiş uygulama</string>
  <!-- Statistics   -->
  <string name="title_statistics_dialog">%s ABI\'ye sahip uygulamalar</string>
  <string name="title_statistics_dialog_no_native_libs">Yerel kütüphanesi olmayan uygulamalar</string>
  <string name="tab_chart">Grafik</string>
  <string name="tab_lib_reference_statistics">Kütüphane Referansı</string>
  <string name="not_marked_lib">İşaretlenmemiş kütüphane</string>
  <string name="submenu_title_component">Bileşen</string>
  <string name="submenu_title_manifest">Manifest</string>
  <string name="ref_category_all">Tümü</string>
  <string name="ref_category_native">Yerel Kütüphaneler</string>
  <string name="ref_category_service">Servisler</string>
  <string name="ref_category_activity">Aktiviteler</string>
  <string name="ref_category_br">Yayın Alıcılar</string>
  <string name="ref_category_cp">İçerik Sağlayıcılar</string>
  <string name="ref_category_perm">İzinler</string>
  <string name="ref_category_static">Statik Kütüphaneler</string>
  <string name="ref_category_metadata">Meta Veriler</string>
  <string name="ref_category_package">Paket</string>
  <string name="ref_category_shared_uid">Paylaşılan UID</string>
  <string name="ref_category_signatures">İmzalar</string>
  <string name="ref_category_only_not_marked">Sadece İşaretlenmemişler</string>
  <string name="ref_category_action">Eylem</string>
  <string name="string_kotlin_used">Kotlin Kullanılmış</string>
  <string name="string_kotlin_unused">Kotlin Kullanılmamış</string>
  <string name="string_compose_used">Jetpack Compose Kullanılmış</string>
  <string name="string_compose_unused">Jetpack Compose Kullanılmamış</string>
  <string name="android_dist_label">Dağıtım</string>
  <string name="android_dist_source">Kaynak</string>
  <string name="android_dist_title">Android Sürüm Dağılımı İstatistikleri</string>
  <string name="android_dist_subtitle_format">Güncelleme Zamanı: %s</string>
  <string name="chart_abi_detailed">Detaylı</string>
  <string name="chart_abi_concise">Kısa ve öz</string>
  <string name="chart_item_not_support">Desteklenmiyor</string>
  <!--  About  -->
  <string name="settings_about">Hakkında</string>
  <string name="settings_about_summary">Bu LibChecker!</string>
  <string name="settings_translate">Çeviriye Katılın</string>
  <string name="settings_translate_summary">Bu uygulamayı çevirmemize yardımcı olun</string>
  <string name="settings_rate_us">Bizi Değerlendirin</string>
  <string name="settings_rate_us_summary">Bu, daha fazla kişi tarafından bulunmamızı sağlayabilir</string>
  <string name="settings_get_updates">Güncellemeleri Alın</string>
  <string name="settings_get_updates_summary">Daha kararlı ve özelliklerle dolu en son sürümü edinin</string>
  <string name="settings_get_updates_in_app">Uygulama içi</string>
  <string name="settings_get_updates_in_app_chip_stable">Kararlı</string>
  <string name="settings_get_updates_in_app_chip_ci">CI</string>
  <string name="about_info">Bu uygulama, cihazınızdaki uygulamaların kullandığı üçüncü taraf kütüphaneleri görüntülemek için kullanılır.</string>
  <string name="toolbar_rate">Değerlendir</string>
  <string name="resource_declaration">Uygulamadaki kaynakların bir kısmı şuradan gelir: </string>
  <string name="library_declaration">LibChecker\'daki tüm işaretli kütüphane bilgileri, ait oldukları SDK\'nın geliştirme belgelerinden veya kod deposundan gelir. Bilgiler yanlışsa, lütfen iletişime geçin: <EMAIL></string>
  <!--  Settings  -->
  <string name="pref_group_normal">Normal</string>
  <string name="pref_group_others">Diğerleri</string>
  <string name="apk_analytics">APK Analizi</string>
  <string name="colorful_icon">Renkli Simge</string>
  <string name="rules_repo_title">Kural Deposu</string>
  <string name="lib_ref_threshold">Kütüphane Referans Eşiği</string>
  <string name="languages">Diller</string>
  <string name="reload_apps">Uygulama Listesini Yeniden Yükle</string>
  <string name="help_docs">Yardım Belgeleri</string>
  <string name="join_telegram_group">İletişim</string>
  <string name="anonymous_statistics">Anonim İstatistikler</string>
  <string name="cloud_rules">Bulut Kuralları</string>
  <string name="dark_mode">Karanlık Mod</string>
  <string name="snapshot_keep">Anlık Görüntü Varsayılan Saklama Kuralı</string>
  <string name="apk_analytics_summary">APK dosyasına tıkladığınızda ayrıntı sayfasını açar</string>
  <string name="colorful_icon_summary">İşaretlenmiş kütüphanelerin bir kısmı renkli logo olarak görünecek</string>
  <string name="lib_ref_threshold_summary">Sadece kütüphane referans sayısı eşiğe ulaşan kütüphaneler listede gösterilir</string>
  <string name="reload_apps_summary">Uygulama listesi anormal görüntülendiğinde deneyin</string>
  <string name="help_docs_summary">LibChecker\'ı nasıl kullanacağınızı öğrenin</string>
  <string name="join_telegram_group_summary">Telegram grubumuza katılın</string>
  <string name="anonymous_statistics_summary">LibChecker\'ı daha pratik hale getirmek için en sık kullanılan işaretli kütüphaneleri ve bazı kullanım alışkanlıkları verilerini anonim olarak göndermek için Google Firebase kullanıyoruz</string>
  <string name="cloud_rules_summary">En son işaretlenmiş kütüphane kurallarını buradan güncelleyin</string>
  <string name="array_dark_mode_off">Kapalı</string>
  <string name="array_dark_mode_on">Açık</string>
  <string name="array_dark_mode_auto">Otomatik</string>
  <string name="array_dark_mode_system">Sistem Varsayılanı</string>
  <string name="array_dark_mode_battery">Pil Tasarrufu Tarafından Ayarlanmış</string>
  <string name="array_snapshot_default">Bildir</string>
  <string name="array_snapshot_keep">Sakla</string>
  <string name="array_snapshot_discard">At</string>
  <!--  Detail  -->
  <string name="detail">Ayrıntılar</string>
  <string name="detail_label">Uygulama Ayrıntılarını Görüntüle</string>
  <string name="not_found">Bulunamadı</string>
  <string name="create_an_issue">Bilgileri tamamlamamıza yardımcı olun</string>
  <string name="app_bundle_details">Android Uygulama Paketi, uygulamanızı oluşturmak ve yayınlamak için Android\'in yeni, resmi yayınlama formatıdır. Android Uygulama Paketi, daha küçük bir uygulama boyutunda harika bir deneyim sunmanızı sağlar, bu da yükleme başarısını artırabilir ve kaldırmaları azaltabilir. Değiştirmek kolaydır. Daha küçük bir uygulamanın avantajlarından yararlanmaya başlamak için kodunuzu yeniden düzenlemeniz gerekmez. Ve değiştirdikten sonra, modüler uygulama geliştirme ve özelleştirilebilir özellik dağıtımından yararlanacaksınız.</string>
  <string name="kotlin_details">Kotlin, JVM, Android, JavaScript, Wasm ve Native\'i hedefleyen açık kaynaklı statik olarak yazılan bir programlama dilidir. JetBrains tarafından geliştirilmiştir. Kotlin, geliştiricileri daha mutlu etmek için tasarlanmış modern ancak zaten olgun bir programlama dilidir. Özlü, güvenli, Java ve diğer dillerle birlikte çalışabilir ve üretken programlama için birden fazla platform arasında kodu yeniden kullanmanın birçok yolunu sunar.</string>
  <string name="items_count">Öğe sayısı: </string>
  <string name="further_operation">İleri İşlem</string>
  <string name="app_info_launch">Başlat</string>
  <string name="app_info_settings">Ayarlar</string>
  <string name="lib_detail_dialog_title">Kütüphane Ayrıntıları</string>
  <string name="lib_permission_dialog_title">İzin Ayrıntısı</string>
  <string name="agp_details">Android Studio derleme sistemi Gradle\'a dayanmaktadır ve Android Gradle eklentisi, Android uygulamaları oluşturmaya özgü birkaç özellik ekler. Android eklentisi genellikle Android Studio ile eşzamanlı olarak güncellense de, eklenti (ve Gradle sisteminin geri kalanı) Android Studio\'dan bağımsız olarak çalışabilir ve ayrı olarak güncellenebilir.</string>
  <string name="xposed_module">Xposed Modülü</string>
  <string name="xposed_module_details">Xposed, herhangi bir APK\'ya dokunmadan sistem ve uygulamaların davranışını değiştirebilen modüller için bir çerçevedir. Bu harikadır çünkü modüllerin herhangi bir değişiklik yapılmadan farklı sürümler ve hatta ROM\'lar için çalışabileceği anlamına gelir.</string>
  <string name="play_app_signing">Play Uygulama İmzalama</string>
  <string name="play_app_signing_details">Play Uygulama İmzalama ile Google, uygulamanızın imzalama anahtarını sizin için yönetir ve korur ve uygulama paketlerinizden oluşturulan optimize edilmiş dağıtım APK\'larını imzalamak için kullanır. Play Uygulama İmzalama, uygulama imzalama anahtarınızı Google\'ın güvenli altyapısında saklar ve güvenliği artırmak için yükseltme seçenekleri sunar.</string>
  <string name="pwa_details">Progressive Web Apps (PWA\'lar), gelişmiş yetenekler, güvenilirlik ve yüklenebilirlik sunmak için modern API\'lerle oluşturulur ve geliştirilir ve tek bir kod tabanıyla herhangi bir cihazda herkese ulaşır.</string>
  <string name="jetpack_compose_details">Jetpack Compose, Android\'in yerel kullanıcı arayüzü oluşturmak için modern araç setidir. Android\'de kullanıcı arayüzü geliştirmeyi basitleştirir ve hızlandırır. Daha az kod, güçlü araçlar ve sezgisel Kotlin API\'leri ile uygulamanızı hızlıca hayata geçirin.</string>
  <string name="jetbrain_compose_multiplatform_details">Compose Multiplatform, JetBrains tarafından geliştirilen modern, deklaratif ve tepkisel bir UI (kullanıcı arayüzü) framework\'üdür. Az miktarda Kotlin kodu ile kullanıcı arayüzleri oluşturmanın basit bir yolunu sunar. Ayrıca, kullanıcı arayüzünüzü bir kez yazıp desteklenen tüm platformlarda – iOS, Android, masaüstü (Windows, macOS, Linux) ve web – çalıştırmanıza olanak tanır.</string>
  <string name="multi_arch_dialog_details"><![CDATA[Bir uygulama 32 bit veya 64 bit olabilen diğer uygulamalara bir API sunuyorsa, olası hataları önlemek için uygulamanın manifestosunda <b>android:multiarch</b> özelliğinin true olarak ayarlanmış olması gerekir.]]></string>
  <string name="extract_native_libs_tip">Uygulama şunu beyan eder: </string>
  <string name="xml_detail">XML Ayrıntısı</string>
  <string name="lib_detail_dialog_title_16kb_page_size">16 KB Sayfa Boyutu</string>
  <string name="lib_detail_dialog_content_16kb_page_size">Tarihsel olarak, Android yalnızca 4 KB bellek sayfa boyutlarını desteklemiş ve bu da Android cihazların tipik olarak sahip olduğu ortalama toplam bellek miktarı için sistem belleği performansını optimize etmiştir. Android 15\'ten itibaren AOSP, 16 KB sayfa boyutu (16 KB cihazlar) kullanacak şekilde yapılandırılmış cihazları desteklemektedir. Uygulamanız doğrudan veya dolaylı olarak bir SDK aracılığıyla herhangi bir NDK kütüphanesi kullanıyorsa, uygulamanızı bu 16 KB cihazlarda çalışması için yeniden oluşturmanız gerekecektir.</string>
  <string name="lib_detail_dialog_title_16kb_page_size_compat">16 KB Backcompat</string>
  <string name="lib_detail_dialog_content_16kb_page_size_compat"><![CDATA[Android 15, platformun performansını optimize etmek için 16 KB bellek sayfaları desteğini getirdi. Android 16 ise bir uyumluluk modu ekleyerek, 4 KB bellek sayfaları için oluşturulmuş bazı uygulamaların 16 KB bellek sayfalarıyla yapılandırılmış bir cihazda çalışmasına olanak tanır.\n\nEğer Android, uygulamanızın 4 KB hizalanmış bellek sayfalarına sahip olduğunu algılarsa, otomatik olarak uyumluluk modunu kullanır ve kullanıcıya bir bildirim penceresi gösterir. Uygulamanız başlatıldığında bu pencerenin görüntülenmesini önlemek için <b>AndroidManifest.xml</b> dosyasında <b>android:pageSizeCompat</b> özelliğini ayarlayarak geriye dönük uyumluluk modunu etkinleştirebilirsiniz. En iyi performans, güvenilirlik ve kararlılık için uygulamanızın yine de 16 KB hizalanmış olması önerilir.]]></string>
  <string name="format_last_updated">Güncellenme zamanı: %s</string>
  <string name="menu_process">İşlem</string>
  <string name="menu_split">Böl</string>
  <string name="alternative_launch_method">Alternatif Başlatma Yöntemi</string>
  <string name="compare_with_current">Mevcut paket ile karşılaştır</string>
  <string name="rx_detail">RxJava, Reactive Extensions\'ın Java VM uygulamasıdır: gözlemlenebilir dizileri kullanarak asenkron ve olay tabanlı programlar oluşturmak için bir kütüphanedir.</string>
  <string name="rx_android_detail">Android için Reactive Extensions</string>
  <string name="rx_kotlin_detail">RxJava için Kotlin Uzantıları</string>
  <string name="permission_not_granted">Verilmedi</string>
  <string name="signature_detail">İmza Ayrıntısı</string>
  <string name="signature_scheme_version">İmza Şeması</string>
  <string name="signature_version">Sürüm</string>
  <string name="signature_serial_number">Seri Numarası</string>
  <string name="signature_issuer">Veren</string>
  <string name="signature_subject">Konu</string>
  <string name="signature_validity_not_before">Geçerlilik Başlangıcı</string>
  <string name="signature_validity_not_after">Geçerlilik Sonu</string>
  <string name="signature_public_key_format">Açık Anahtar Formatı</string>
  <string name="signature_public_key_algorithm">Açık Anahtar Algoritması</string>
  <string name="signature_public_key_exponent">Açık Anahtar Üssü</string>
  <string name="signature_public_key_modulus_size">Açık Anahtar Modülüs Boyutu</string>
  <string name="signature_public_key_modulus">Açık Anahtar Modülüsü</string>
  <string name="signature_public_key_y">Açık Anahtar Y Değeri</string>
  <string name="signature_public_key_type">Açık Anahtar Tipi</string>
  <string name="signature_algorithm_name">İmza Algoritması Adı</string>
  <string name="signature_algorithm_oid">İmza Algoritması OID</string>
  <string name="lib_detail_label_tip">Etiket</string>
  <string name="lib_detail_develop_team_tip">Geliştirme Ekibi</string>
  <string name="lib_detail_rule_contributors_tip">Kural Katkıda Bulunan(lar)</string>
  <string name="lib_detail_description_tip">Açıklama</string>
  <string name="lib_detail_relative_link_tip">İlgili Bağlantı</string>
  <string name="lib_detail_last_update_tip">Güncellenme zamanı</string>
  <string name="lib_detail_app_props_title">Uygulama Özellikleri</string>
  <string name="lib_detail_app_props_tip">Daha fazla bilgi</string>
  <string name="lib_detail_xposed_min_version">Minimum Sürüm</string>
  <string name="lib_detail_xposed_default_scope">Varsayılan Kapsam</string>
  <string name="lib_detail_xposed_init_class">Başlatma Sınıfı</string>
  <string name="lib_detail_app_install_source_title">Yükleme Kaynağı</string>
  <string name="lib_detail_app_install_source_originating_package">Yükleme isteyen</string>
  <string name="lib_detail_app_install_source_installing_package">Yükleme Yürütücüsü</string>
  <string name="lib_detail_app_installed_time">Kurulum Süresi</string>
  <string name="lib_detail_app_first_installed_time">İlk kurulum zamanı: </string>
  <string name="lib_detail_app_last_updated_time">Son kurulum zamanı: </string>
  <string name="lib_detail_app_install_source_empty">Bilinmiyor</string>
  <string name="lib_detail_app_install_source_empty_detail">Shell veya kaldırılmış uygulama</string>
  <string name="lib_detail_app_install_source_shizuku_usage">Android, yükleme isteyicisini almak için API\'yi kısıtladığından, yükleme isteyicisini almak için Shizuku kullanıyoruz.</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled">Shizuku yüklü değil</string>
  <string name="lib_detail_app_install_source_shizuku_uninstalled_detail">Shizuku\'yu yüklemek için buraya tıklayın</string>
  <string name="lib_detail_app_install_source_shizuku_low_version">Shizuku API 10 gerekiyor</string>
  <string name="lib_detail_app_install_source_shizuku_low_version_detail">Shizuku\'yu güncellemek için buraya tıklayın</string>
  <string name="lib_detail_app_install_source_shizuku_not_running">Shizuku çalışmıyor</string>
  <string name="lib_detail_app_install_source_shizuku_not_running_detail">Başlatmak için Shizuku\'ya atlamak için buraya tıklayın</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted">Shizuku yetkilendirilmedi</string>
  <string name="lib_detail_app_install_source_shizuku_permission_not_granted_detail">Yükleme isteyicisi için Shizuku yetkilendirmesi için buraya tıklayın</string>
  <string name="lib_detail_elf_info">Ayrıntılı Bilgi</string>
  <string name="lib_detail_dependency_tip">Bağımlılıklar</string>
  <string name="lib_detail_entry_points_tip">Giriş Noktaları</string>
  <!--  Toast  -->
  <string name="toast_copied_to_clipboard">Panoya kopyalandı</string>
  <string name="toast_not_existing_market">Mevcut bir uygulama mağazası yok</string>
  <string name="toast_use_another_file_manager">Lütfen APK\'yı açmak için başka bir Dosya Yöneticisi kullanın</string>
  <string name="toast_cant_open_app">Bu uygulama açılamıyor</string>
  <string name="toast_cloud_rules_update_error">Bulut kuralları güncellenemedi. Lütfen tekrar deneyin.</string>
  <string name="toast_not_enough_storage_space">Yeterli depolama alanı yok</string>
  <string name="toast_downloading_app">İndirme talep edildi</string>
  <!--  Snapshot  -->
  <string name="snapshot_btn_save_current">Mevcut anlık görüntüyü kaydet</string>
  <string name="snapshot_current_timestamp">Mevcut Anlık Görüntü Zaman Damgası</string>
  <string name="snapshot_apps_count">Anlık Görüntüdeki Uygulama Sayısı / Uygulama Sayısı</string>
  <string name="comparison_snapshot_apps_count">Anlık Görüntüdeki Uygulama Sayısı</string>
  <string name="snapshot_indicator_added">Eklendi</string>
  <string name="snapshot_indicator_removed">Kaldırıldı</string>
  <string name="snapshot_indicator_changed">Değişti</string>
  <string name="snapshot_indicator_moved">Taşındı</string>
  <string name="snapshot_empty_list_title">Bileşen Değişikliği Yok</string>
  <string name="snapshot_no_snapshot">Anlık Görüntü Yok</string>
  <string name="snapshot_detail_new_install_title">Bu Uygulama yeni yüklendi</string>
  <string name="snapshot_detail_deleted_title">Bu Uygulama kaldırıldı</string>
  <string name="snapshot_time_node_uninitialized">(Başlatılmamış)</string>
  <string name="snapshot_preinstalled_app">Önceden yüklenmiş uygulama</string>
  <string name="snapshot_generate_text_report">Metin Raporu Oluştur</string>
  <string name="snapshot_scheme_tip"><![CDATA[İpucu: Artık arka planda şu yolla anlık görüntüler oluşturabilirsiniz:<br><b>%s</b>]]></string>
  <string name="snapshot_build_id">Yapı Kimliği</string>
  <string name="snapshot_build_security_patch">Güvenlik Yaması</string>
  <!--  Dialog  -->
  <string name="dialog_title_reload_apps">Tüm uygulamaları yeniden yüklemek istediğinizden emin misiniz?</string>
  <string name="dialog_subtitle_reload_apps">Bu işlem birkaç saniye sürebilir</string>
  <string name="dialog_title_change_timestamp">Zaman Düğümünü Değiştir</string>
  <string name="dialog_title_keep_previous_snapshot">Dikkat</string>
  <string name="dialog_message_keep_previous_snapshot">Önceki anlık görüntüyü saklamak istiyor musunuz?</string>
  <string name="dialog_title_select_to_delete">Silinecek Anlık Görüntü için Bir Tarih Seçin</string>
  <string name="dialog_title_confirm_to_delete">Bu anlık görüntüyü silmek istediğinizden emin misiniz?</string>
  <string name="btn_keep">Sakla</string>
  <string name="btn_drop">At</string>
  <string name="dialog_title_compare_diff_apk">Dikkat</string>
  <string name="dialog_message_compare_diff_apk">İki farklı APK\'yı karşılaştırmak istiyor musunuz?</string>
  <!--  Album  -->
  <string name="album_compare">Karşılaştır</string>
  <string name="album_item_comparison_title">Karşılaştırma</string>
  <string name="album_item_comparison_subtitle">İki anlık görüntüyü karşılaştır</string>
  <string name="album_item_comparison_invalid_compare">Geçersiz karşılaştırma</string>
  <string name="album_item_comparison_invalid_shared_items">Lütfen karşılaştırma için iki APK dosyası seçin</string>
  <string name="album_item_comparison_choose_local_apk">Yerel APK Seç</string>
  <string name="album_item_management_title">Yönetim</string>
  <string name="album_item_management_subtitle">Tüm anlık görüntüleri yönet</string>
  <string name="album_item_management_snapshot_auto_remove_default_title">Yalnızca En Son Anlık Görüntüleri Sakla</string>
  <string name="album_item_management_snapshot_auto_remove_specific_title">Yalnızca %d En Son Öğeleri Sakla</string>
  <string name="album_item_management_snapshot_auto_remove_desc">“%s” öğesine dokunulmasından itibaren, yalnızca en son anlık görüntüler saklanacak ve eski anlık görüntüler otomatik olarak silinecektir.</string>
  <string name="album_item_backup_restore_title">Yedekleme ve Geri Yükleme</string>
  <string name="album_item_backup_restore_subtitle">Anlık görüntüleri yedekle ve geri yükle</string>
  <string name="album_item_track_title">Takip Et</string>
  <string name="album_item_track_subtitle">Zorla karşılaştırma değişikliği yapmak için uygulamaları seçin</string>
  <string name="album_backup">Yedekle</string>
  <string name="album_backup_summary">Tüm anlık görüntüleri yedekle</string>
  <string name="album_restore">Geri Yükle</string>
  <string name="album_restore_summary">Yedekleme dosyasından anlık görüntüleri geri yükle</string>
  <string name="album_restore_detail">%1$s : toplam %2$s öğe\n</string>
  <string name="album_click_to_choose">Seçmek için Tıkla</string>
  <string name="album_dialog_delete_snapshot_message">İşleniyor…</string>
  <string name="album_snapshot_top_apps_not_initialized">Henüz yüklenmedi</string>
  <!--  Integration  -->
  <string name="integration_monkey_king_menu_block">\"MonkeyKing Purify\" ile engelle</string>
  <string name="integration_monkey_king_menu_unblock">\"MonkeyKing Purify\" ile engeli kaldır</string>
  <string name="integration_blocker_menu_block">\"Blocker\" ile engelle</string>
  <string name="integration_blocker_menu_unblock">\"Blocker\" ile engeli kaldır</string>
  <string name="integration_anywhere_menu_editor">\"Anywhere- Editor\"de aç</string>
  <!--  Cloud rules  -->
  <string name="rules_local_repo_version">Yerel depo sürümü</string>
  <string name="rules_remote_repo_version">Uzak depo sürümü</string>
  <string name="rules_btn_restart_to_update">Güncellemek için Yeniden Başlat</string>
  <string name="rules_btn_update">Güncelle</string>
  <!-- Advanced menu -->
  <string name="adv_show_system_apps">Sistem Uygulamalarını Göster</string>
  <string name="adv_show_overlays">Katmanları Göster</string>
  <string name="adv_show_64_bit">64-bit Uygulamaları Göster</string>
  <string name="adv_show_32_bit">32-bit Uygulamaları Göster</string>
  <string name="adv_sort_mode">Sıralama Modu</string>
  <string name="adv_sort_by_time">Güncellenme Zamanı</string>
  <string name="adv_sort_by_target_version">Hedef Sürüm</string>
  <string name="adv_sort_by_name">İsim</string>
  <string name="adv_show_android_version">Android Sürümünü Göster</string>
  <string name="adv_show_target_version">Hedef Sürümü Göster</string>
  <string name="adv_show_min_version">Minimum Sürümü Göster</string>
  <string name="adv_show_compile_version">Derleme Sürümünü Göster</string>
  <string name="adv_tint_abi_label">ABI Etiketini Renklendir</string>
  <string name="adv_mark_exported">Dışa Aktarılmış Bileşeni İşaretle</string>
  <string name="adv_mark_disabled">Devre Dışı Bırakılmış Bileşeni İşaretle</string>
  <string name="adv_show_marked_lib">İşaretlenmiş Kütüphaneleri Göster</string>
  <string name="adv_show_system_framework_apps">Framework Uygulamalarını Göster</string>
  <!--  Snapshot menu  -->
  <string name="snapshot_menu_show_update_time">Güncelleme Zamanını Göster</string>
  <string name="snapshot_menu_hide_no_component_changes">Bileşen Değişikliği Olmayanları Gizle</string>
  <string name="snapshot_menu_diff_highlight">Fark Vurgusu</string>
  <string name="snapshot_menu_use_iec_units">IEC Birimlerini Kullan</string>
</resources>
