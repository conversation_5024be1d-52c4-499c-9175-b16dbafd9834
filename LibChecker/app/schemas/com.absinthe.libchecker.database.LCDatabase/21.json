{"formatVersion": 1, "database": {"version": 21, "identityHash": "c21f13d0765bd917a0c28357a4cb0f84", "entities": [{"tableName": "item_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`packageName` TEXT NOT NULL, `label` TEXT NOT NULL, `versionName` TEXT NOT NULL, `versionCode` INTEGER NOT NULL, `installedTime` INTEGER NOT NULL, `lastUpdatedTime` INTEGER NOT NULL, `isSystem` INTEGER NOT NULL, `abi` INTEGER NOT NULL, `features` INTEGER NOT NULL, `targetApi` INTEGER NOT NULL, `variant` INTEGER NOT NULL, PRIMARY KEY(`packageName`))", "fields": [{"fieldPath": "packageName", "columnName": "packageName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "label", "columnName": "label", "affinity": "TEXT", "notNull": true}, {"fieldPath": "versionName", "columnName": "versionName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "versionCode", "columnName": "versionCode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "installedTime", "columnName": "installedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdatedTime", "columnName": "lastUpdatedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSystem", "columnName": "isSystem", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "abi", "columnName": "abi", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "features", "columnName": "features", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetApi", "columnName": "targetApi", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "variant", "columnName": "variant", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["packageName"]}, "indices": [], "foreignKeys": []}, {"tableName": "snapshot_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `packageName` TEXT NOT NULL, `timeStamp` INTEGER NOT NULL, `label` TEXT NOT NULL, `versionName` TEXT NOT NULL, `versionCode` INTEGER NOT NULL, `installedTime` INTEGER NOT NULL, `lastUpdatedTime` INTEGER NOT NULL, `isSystem` INTEGER NOT NULL, `abi` INTEGER NOT NULL, `targetApi` INTEGER NOT NULL, `nativeLibs` TEXT NOT NULL, `services` TEXT NOT NULL, `activities` TEXT NOT NULL, `receivers` TEXT NOT NULL, `providers` TEXT NOT NULL, `permissions` TEXT NOT NULL, `metadata` TEXT NOT NULL, `packageSize` INTEGER NOT NULL, `compileSdk` INTEGER NOT NULL, `minSdk` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "packageName", "columnName": "packageName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeStamp", "columnName": "timeStamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "label", "columnName": "label", "affinity": "TEXT", "notNull": true}, {"fieldPath": "versionName", "columnName": "versionName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "versionCode", "columnName": "versionCode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "installedTime", "columnName": "installedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUpdatedTime", "columnName": "lastUpdatedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSystem", "columnName": "isSystem", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "abi", "columnName": "abi", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetApi", "columnName": "targetApi", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nativeLibs", "columnName": "nativeLibs", "affinity": "TEXT", "notNull": true}, {"fieldPath": "services", "columnName": "services", "affinity": "TEXT", "notNull": true}, {"fieldPath": "activities", "columnName": "activities", "affinity": "TEXT", "notNull": true}, {"fieldPath": "receivers", "columnName": "receivers", "affinity": "TEXT", "notNull": true}, {"fieldPath": "providers", "columnName": "providers", "affinity": "TEXT", "notNull": true}, {"fieldPath": "permissions", "columnName": "permissions", "affinity": "TEXT", "notNull": true}, {"fieldPath": "metadata", "columnName": "metadata", "affinity": "TEXT", "notNull": true}, {"fieldPath": "packageSize", "columnName": "packageSize", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "compileSdk", "columnName": "compileSdk", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "minSdk", "columnName": "minSdk", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "timestamp_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`timestamp` INTEGER NOT NULL, `topApps` TEXT, PRIMARY KEY(`timestamp`))", "fields": [{"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "topApps", "columnName": "topApps", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["timestamp"]}, "indices": [], "foreignKeys": []}, {"tableName": "track_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`packageName` TEXT NOT NULL, PRIMARY KEY(`packageName`))", "fields": [{"fieldPath": "packageName", "columnName": "packageName", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["packageName"]}, "indices": [], "foreignKeys": []}, {"tableName": "diff_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`packageName` TEXT NOT NULL, `lastUpdatedTime` INTEGER NOT NULL, `diffContent` TEXT NOT NULL, PRIMARY KEY(`packageName`))", "fields": [{"fieldPath": "packageName", "columnName": "packageName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastUpdatedTime", "columnName": "lastUpdatedTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "diffContent", "columnName": "diffContent", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["packageName"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c21f13d0765bd917a0c28357a4cb0f84')"]}}