{"formatVersion": 1, "database": {"version": 1, "identityHash": "878dff329d1d60c12c9240751ae84dec", "entities": [{"tableName": "rules_table", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `label` TEXT NOT NULL, `type` INTEGER NOT NULL, `iconIndex` INTEGER NOT NULL, `isRegexRule` INTEGER NOT NULL, `regexName` TEXT, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "label", "columnName": "label", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iconIndex", "columnName": "iconIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRegexRule", "columnName": "isRegexRule", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "regexName", "columnName": "regexName", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '878dff329d1d60c12c9240751ae84dec')"]}}