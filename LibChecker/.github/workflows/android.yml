name: Android CI

on:
  push:
    branches:
      - master
    paths-ignore:
      - '.idea/**'
      - '.gitattributes'
      - '.github/**.json'
      - '.gitignore'
      - '.gitmodules'
      - '**.md'
      - 'LICENSE'
      - 'NOTICE'
  pull_request:
    paths-ignore:
      - '.idea/**'
      - '.gitattributes'
      - '.github/**.json'
      - '.gitignore'
      - '.gitmodules'
      - '**.md'
      - 'LICENSE'
      - 'NOTICE'
  workflow_dispatch:

env:
  APP_CENTER_SECRET: ${{secrets.APP_CENTER_SECRET}}
  FORCE_JAVASCRIPT_ACTIONS_TO_NODE20: true

jobs:
  check:
    name: Check
    runs-on: self-hosted
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - name: Skip duplicate actions
        uses: fkirc/skip-duplicate-actions@v5
        with:
          cancel_others: true
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 24
      - uses: gradle/actions/setup-gradle@v4
      - run: ./gradlew spotlessCheck

  build:
    name: Build
    runs-on: self-hosted

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: 24
      - uses: gradle/actions/setup-gradle@v4
        with:
          cache-encryption-key: Da25KUVSE5jbGds2zXmfXw==
      - name: Retrieve version
        run: |
          echo VERSION=$(echo ${{ github.event.head_commit.id }} | head -c 10) >> $GITHUB_ENV
      - name: Write sign info
        if: github.event_name != 'pull_request' && github.ref == 'refs/heads/master' && github.repository == 'LibChecker/LibChecker'
        run: |
          if [ ! -z "${{ secrets.SIGNING_KEY }}" ]; then
            echo releaseStorePassword='${{ secrets.KEY_STORE_PASSWORD }}' >> gradle.properties
            echo releaseKeyAlias='${{ secrets.ALIAS }}' >> gradle.properties
            echo releaseKeyPassword='${{ secrets.KEY_PASSWORD }}' >> gradle.properties
            echo releaseStoreFile='${{ github.workspace }}/key.jks' >> gradle.properties
            echo ${{ secrets.SIGNING_KEY }} | base64 --decode > ${{ github.workspace }}/key.jks
          fi
      - name: Build with Gradle
        run: ./gradlew -PappVerName=${{ env.VERSION }} app:assembleRelease
      - name: Upload built Foss apk
        if: success() && github.event_name != 'pull_request' && github.repository == 'LibChecker/LibChecker'
        uses: actions/upload-artifact@v4
        with:
          name: Foss
          path: ${{ github.workspace }}/app/build/outputs/apk/foss/release
      - name: Upload built Market apk
        if: success() && github.event_name != 'pull_request' && github.repository == 'LibChecker/LibChecker'
        uses: actions/upload-artifact@v4
        with:
          name: Market
          path: ${{ github.workspace }}/app/build/outputs/apk/market/release
      - name: Upload mappings
        if: success() && github.event_name != 'pull_request' && github.ref == 'refs/heads/master'
        uses: actions/upload-artifact@v4
        with:
          name: mappings
          path: "app/build/outputs/mapping/marketRelease"
      - name: Set apk path
        id: apk-path
        run: |
          foss_path=$(find **/build/outputs/apk/foss -name '*.apk' -type f | head -1)
          echo "foss_path=$foss_path" >> $GITHUB_OUTPUT
          market_path=$(find **/build/outputs/apk/market -name '*.apk' -type f | head -1)
          echo "market_path=$market_path" >> $GITHUB_OUTPUT
      - name: Get apk info
        if: success() && github.event_name != 'pull_request' && github.repository == 'LibChecker/LibChecker'
        id: apk-info
        uses: zhaobozhen/apk-info-action@v1.1.3
        with:
          apk-path: ${{ steps.apk-path.outputs.foss_path }}
      - name: Send commit to telegram
        if: success() && github.repository == 'LibChecker/LibChecker' && github.event_name != 'pull_request' && github.ref == 'refs/heads/master'
        env:
          CHANNEL_ID: ${{ secrets.TELEGRAM_TO }}
          BOT_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
          FOSS_FILE: ${{ steps.apk-path.outputs.foss_path }}
          MARKET_FILE: ${{ steps.apk-path.outputs.market_path }}
          COMMIT_MESSAGE: |+
            New push to github\!
            ```
            ${{ github.event.head_commit.message }}
            ```by `${{ github.event.head_commit.author.name }}`
            See commit detail [here](${{ github.event.head_commit.url }})
            Snapshot apk is attached
        run: |
          ESCAPED=`python3 -c 'import json,os,urllib.parse; print(urllib.parse.quote(json.dumps(os.environ["COMMIT_MESSAGE"])))'`
          curl -v "https://api.telegram.org/bot${BOT_TOKEN}/sendMediaGroup?chat_id=${CHANNEL_ID}&media=%5B%7B%22type%22%3A%22document%22%2C%20%22media%22%3A%22attach%3A%2F%2FmarketRelease%22%2C%22parse_mode%22%3A%22MarkdownV2%22%2C%22caption%22:${ESCAPED}%7D%5D" -F marketRelease="@$MARKET_FILE"
      - name: Commit and push artifacts to assets repo
        if: success() && github.event_name != 'pull_request' && github.repository == 'LibChecker/LibChecker'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FOSS_FILE: ${{ github.workspace }}/${{ steps.apk-path.outputs.foss_path }}
        run: |
            # Define the directory where your build artifacts are located
            ARTIFACTS_DIR=${FOSS_FILE}

            # Define the target repository and branch
            TARGET_REPO=LibChecker/assets
            TARGET_BRANCH=ci

            # Authenticate GitHub CLI
            # echo $GITHUB_TOKEN | gh auth login --with-token

            # Clone the target repository
            # gh repo clone $TARGET_REPO target-repo

            #Locate the target repository
            cd ~/Projects/assets

            # Create a new branch or checkout the existing branch
            git fetch origin
            git checkout -B $TARGET_BRANCH
            git rebase origin $TARGET_BRANCH
            git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
            git config --local user.name "github-actions[bot]"

            # Copy artifacts to the repository
            cp -f $ARTIFACTS_DIR ./app-release.apk

            # Commit and push changes
            git add app-release.apk
            git commit -m "Add build artifacts"
            git push origin $TARGET_BRANCH --force
      - name: Create JSON file
        if: success() && github.event_name != 'pull_request' && github.repository == 'LibChecker/LibChecker'
        env:
          FOSS_FILE: ${{ github.workspace }}/${{ steps.apk-path.outputs.foss_path }}
        run: |
          cd ~/Projects/assets
          git checkout main
          git rebase origin main
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"

          echo '{
            "app": {
              "version": "${{ steps.apk-info.outputs.version-name }}",
              "versionCode": ${{steps.apk-info.outputs.version-code }},
              "extra": {
                "target": ${{steps.apk-info.outputs.target-sdk-version }},
                "min": ${{steps.apk-info.outputs.min-sdk-version }},
                "compile": ${{steps.apk-info.outputs.compile-sdk-version }},
                "packageSize": ${{ steps.apk-info.outputs.file-size }}
              },
              "link": "https://jsdelivr.b-cdn.net/gh/LibChecker/assets@ci/app-release.apk"
            }
          }' > ci.json

          git add ci.json
          git commit -m "Update ci.json"
          git push origin main --force

  skipped:
    runs-on: self-hosted
    if: ${{ startsWith(github.event.head_commit.message, '[skip ci]') }}
    steps:
      - uses: actions/checkout@v4
      - name: Send commit to telegram
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO }}
          token: ${{ secrets.TELEGRAM_TOKEN }}
          format: markdown
          message: |+
            New push to github!
            *${{ github.event.head_commit.message }}* by ${{ github.event.head_commit.author.name }}
            See commit detail [here](${{ github.event.head_commit.url }})
            This push skipped building

  cancelled:
    runs-on: self-hosted
    if: ${{ cancelled() && github.repository == 'LibChecker/LibChecker' && github.event_name != 'pull_request' && github.ref == 'refs/heads/master' }}
    needs: [check, build]
    steps:
      - name: Send commit to telegram if cancelled
        uses: appleboy/telegram-action@master
        with:
          to: ${{ secrets.TELEGRAM_TO }}
          token: ${{ secrets.TELEGRAM_TOKEN }}
          format: markdown
          message: |+
            New push to github!
            *${{ github.event.head_commit.message }}* by ${{ github.event.head_commit.author.name }}
            See commit detail [here](${{ github.event.head_commit.url }})
            This push cancelled building
