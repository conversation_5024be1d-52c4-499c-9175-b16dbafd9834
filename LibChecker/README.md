# LibChecker

[![Android CI](https://github.com/LibChecker/LibChecker/actions/workflows/android.yml/badge.svg)](https://github.com/LibChecker/LibChecker/actions/workflows/android.yml)
[![License](https://img.shields.io/github/license/LibChecker/LibChecker?label=License)](https://choosealicense.com/licenses/apache-2.0/)
[![Discussion](https://img.shields.io/badge/Telegram-Group-blue.svg?logo=telegram)](https://t.me/libcheckerr)
[![Crowdin](https://badges.crowdin.net/libchecker/localized.svg)](https://crowdin.com/project/libchecker)

![Header](./source/header.png)

## What's this?
This app is used to view the third-party libraries used by applications in your device. It can view the ABI architecture of the application's native library (in general, whether the application is 64-bit or 32-bit). It can also view well-known libraries marked by [The Rule Repository](https://github.com/LibChecker/LibChecker-Rules), and can even sort and view them according to the number of libraries references.

## Supported Versions
Android 7.0 ~ 16

Android 6 [Marshmallow](https://github.com/LibChecker/LibChecker/tree/marshmallow)

## Document
[LibChecker-Docs](https://github.com/LibChecker/LibChecker-Docs)

## Download
<!-- [<img src="./source/coolapk-badge.png" width="323" height="125" />](https://www.coolapk.com/apk/com.absinthe.libchecker) -->
[<img src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" width="323" height="125" />](https://play.google.com/store/apps/details?id=com.absinthe.libchecker)
[<img src="https://fdroid.gitlab.io/artwork/badge/get-it-on.png" width="323" height="125" />](https://f-droid.org/packages/com.absinthe.libchecker/)
[<img src="https://gitlab.com/IzzyOnDroid/repo/-/raw/master/assets/IzzyOnDroid.png" width="323" height="125" />](https://apt.izzysoft.de/fdroid/index/apk/com.absinthe.libchecker)

## Discussions
[Github Discussions](https://github.com/LibChecker/LibChecker/discussions)

### Telegram Group
<img src="./source/tg_group_dark.png#gh-dark-mode-only" width="240" height="240" />
<img src="./source/tg_group_light.png#gh-light-mode-only" width="240" height="240" />
