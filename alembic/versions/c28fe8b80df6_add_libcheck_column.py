"""add libcheck column

Revision ID: c28fe8b80df6
Revises: 5404facddfa4
Create Date: 2025-08-10 13:17:01.563274

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c28fe8b80df6'
down_revision: Union[str, Sequence[str], None] = '5404facddfa4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_sdk_knowledge_base', sa.Column('detection_type', sa.Integer(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('is_regex_rule', sa.<PERSON>(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('regex_pattern', sa.Text(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('tags', sa.Text(), nullable=True))
    op.alter_column('class_sdk_knowledge_base', 'id',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('class_sdk_knowledge_base', 'id',
               existing_type=sa.INTEGER(),
               server_default=sa.Identity(always=True, start=1, increment=1, minvalue=1, maxvalue=2147483647, cycle=False, cache=1),
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('class_sdk_knowledge_base', 'tags')
    op.drop_column('class_sdk_knowledge_base', 'regex_pattern')
    op.drop_column('class_sdk_knowledge_base', 'is_regex_rule')
    op.drop_column('class_sdk_knowledge_base', 'detection_type')
    # ### end Alembic commands ###
