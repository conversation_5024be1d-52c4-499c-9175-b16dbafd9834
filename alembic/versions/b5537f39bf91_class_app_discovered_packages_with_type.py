"""class_app_discovered_packages with type

Revision ID: b5537f39bf91
Revises: c28fe8b80df6
Create Date: 2025-08-12 13:51:21.310664

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b5537f39bf91'
down_revision: Union[str, Sequence[str], None] = 'c28fe8b80df6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_app_discovered_packages', sa.Column('type', sa.String(length=50), nullable=True))
    op.alter_column('class_app_discovered_packages', 'package_name',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.Text(),
               existing_nullable=False)
    op.execute('ALTER TABLE class_sdk_knowledge_base ALTER COLUMN id DROP IDENTITY IF EXISTS')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE class_sdk_knowledge_base ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY')
    op.alter_column('class_app_discovered_packages', 'package_name',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    op.drop_column('class_app_discovered_packages', 'type')
    # ### end Alembic commands ###