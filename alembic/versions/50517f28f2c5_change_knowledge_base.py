"""change knowledge_base

Revision ID: 50517f28f2c5
Revises: 3814cfba8c5f
Create Date: 2025-08-09 12:31:54.333675

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50517f28f2c5'
down_revision: Union[str, Sequence[str], None] = '3814cfba8c5f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_sdk_knowledge_base', sa.Column('id', sa.Integer(), nullable=False))
    op.add_column('class_sdk_knowledge_base', sa.Column('company_name', sa.String(length=255), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('version', sa.String(length=100), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('subcategory', sa.String(length=100), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('brief_message', sa.Text(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('official_web', sa.Text(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('sdk_document_url', sa.Text(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('privacy_policy_url', sa.Text(), nullable=True))
    op.add_column('class_sdk_knowledge_base', sa.Column('compliance_instructions_url', sa.Text(), nullable=True))
    op.create_unique_constraint(None, 'class_sdk_knowledge_base', ['package_prefix'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'class_sdk_knowledge_base', type_='unique')
    op.drop_column('class_sdk_knowledge_base', 'compliance_instructions_url')
    op.drop_column('class_sdk_knowledge_base', 'privacy_policy_url')
    op.drop_column('class_sdk_knowledge_base', 'sdk_document_url')
    op.drop_column('class_sdk_knowledge_base', 'official_web')
    op.drop_column('class_sdk_knowledge_base', 'brief_message')
    op.drop_column('class_sdk_knowledge_base', 'subcategory')
    op.drop_column('class_sdk_knowledge_base', 'version')
    op.drop_column('class_sdk_knowledge_base', 'company_name')
    op.drop_column('class_sdk_knowledge_base', 'id')
    # ### end Alembic commands ###
