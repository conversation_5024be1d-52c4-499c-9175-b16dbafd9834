"""changed sdk_match_flow needed table

Revision ID: 339eff19f221
Revises: b5537f39bf91
Create Date: 2025-08-13 09:09:55.083432

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '339eff19f221'
down_revision: Union[str, Sequence[str], None] = 'b5537f39bf91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_app_version_sdks', sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False))
    op.add_column('class_app_version_sdks', sa.Column('sdk_knowledge_base_id', sa.Integer(), nullable=True))
    op.add_column('class_app_version_sdks', sa.Column('match_type', sa.String(length=50), nullable=True))
    op.add_column('class_app_version_sdks', sa.Column('is_potential', sa.Boolean(), nullable=False))
    op.add_column('class_app_version_sdks', sa.Column('signal_count', sa.Integer(), nullable=True))
    op.add_column('class_app_version_sdks', sa.Column('child_packages', sa.Text(), nullable=True))
    op.create_index('idx_app_version_sdks_is_potential', 'class_app_version_sdks', ['is_potential'], unique=False)
    op.create_index('idx_app_version_sdks_match_type', 'class_app_version_sdks', ['match_type'], unique=False)
    op.create_index('idx_app_version_sdks_sdk_knowledge_base_id', 'class_app_version_sdks', ['sdk_knowledge_base_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_app_version_sdks_sdk_knowledge_base_id', table_name='class_app_version_sdks')
    op.drop_index('idx_app_version_sdks_match_type', table_name='class_app_version_sdks')
    op.drop_index('idx_app_version_sdks_is_potential', table_name='class_app_version_sdks')
    op.drop_column('class_app_version_sdks', 'child_packages')
    op.drop_column('class_app_version_sdks', 'signal_count')
    op.drop_column('class_app_version_sdks', 'is_potential')
    op.drop_column('class_app_version_sdks', 'match_type')
    op.drop_column('class_app_version_sdks', 'sdk_knowledge_base_id')
    op.drop_column('class_app_version_sdks', 'id')
    # ### end Alembic commands ###
