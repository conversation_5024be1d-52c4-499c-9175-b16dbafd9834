"""add column toclass_app_version_sdks 

Revision ID: e211bbb739be
Revises: 339eff19f221
Create Date: 2025-08-13 17:26:55.484844

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e211bbb739be'
down_revision: Union[str, Sequence[str], None] = '339eff19f221'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_app_version_sdks', sa.Column('type', sa.String(length=50), nullable=True))
    op.drop_column('class_app_version_sdks', 'id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('class_app_version_sdks', sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False))
    op.drop_column('class_app_version_sdks', 'type')
    # ### end Alembic commands ###
