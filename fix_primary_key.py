from tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow import SDKMatcher
from sqlalchemy import text

matcher = SDKMatcher()

with matcher.engine.connect() as conn:
    trans = conn.begin()
    try:
        # 删除现有的主键约束
        print('删除现有的主键约束...')
        conn.execute(text("ALTER TABLE class_app_version_sdks DROP CONSTRAINT IF EXISTS class_app_version_sdks_pkey"))
        
        # 删除id字段的主键属性
        print('删除id字段的主键属性...')
        # 注意：在PostgreSQL中，我们不能直接删除一个自增字段的主键属性
        # 我们需要重新创建表或者使用其他方法
        
        # 为表添加新的主键约束
        print('添加新的主键约束...')
        conn.execute(text("ALTER TABLE class_app_version_sdks ADD PRIMARY KEY (app_version_id, sdk_package_prefix)"))
        
        trans.commit()
        print('主键约束更新成功')
    except Exception as e:
        trans.rollback()
        print(f'更新主键约束时出错: {e}')
        raise