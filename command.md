TENYY_ENV=test alembic current

TENYY_ENV=test alembic stamp head

### 自动产生迁移的代码

TENYY_ENV=test alembic revision --autogenerate -m "add new tables"

### 测试与production数据库升级同步

TENYY_ENV=test alembic upgrade head

TENYY_ENV=production alembic upgrade head



cd /home/<USER>/dev/tenyy-dind
python3 -m tenyy.src.apkinfo_analysis.apk_analysis_flow.apk_analysis_flow

cd /home/<USER>/dev/tenyy-dind
python3 -m tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow

### sql
-- 查看特定应用的所有版本发现的 SDK
SELECT * FROM app_version_sdk_view WHERE app_id = 'com.example.app' ORDER BY app_version_id, is_potential;

-- 查看特定应用版本发现的所有 SDK
SELECT * FROM app_version_sdk_view WHERE app_version_id = 123;

-- 统计每个应用发现的 SDK 数量
SELECT app_id, app_name, COUNT(*) as sdk_count 
FROM app_version_sdk_view 
GROUP BY app_id, app_name 
ORDER BY sdk_count DESC;

-- 查看所有已知 SDK（非潜在 SDK）
SELECT DISTINCT app_name, app_version, sdk_name 
FROM app_version_sdk_view 
WHERE is_potential = false 
ORDER BY app_name, app_version;

-- 查看所有潜在 SDK
SELECT app_name, app_version, sdk_package_prefix, signal_count
FROM app_version_sdk_view 
WHERE is_potential = true 
ORDER BY signal_count DESC;