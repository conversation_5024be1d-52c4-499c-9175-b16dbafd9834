from tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow import SDKMatcher
from sqlalchemy import text

matcher = SDKMatcher()

with matcher.engine.connect() as conn:
    result = conn.execute(text("SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_name='class_app_version_sdks' ORDER BY ordinal_position"))
    print('Database table structure:')
    for row in result.fetchall():
        print(row)