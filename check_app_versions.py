#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的应用版本
"""
import os
import sys
import logging

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, func
from tenyy.src.config.settings import settings
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage


def main():
    """主函数"""
    logger.info("开始检查应用版本")
    
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 获取应用版本ID列表，按包数量排序
        app_versions = session.query(
            class_AppDiscoveredPackage.app_version_id,
            func.count(class_AppDiscoveredPackage.id).label('package_count')
        ).group_by(class_AppDiscoveredPackage.app_version_id).order_by(func.count(class_AppDiscoveredPackage.id).desc()).limit(10).all()
        
        logger.info(f"找到 {len(app_versions)} 个应用版本:")
        for app_version in app_versions:
            logger.info(f"  应用版本 {app_version.app_version_id}: {app_version.package_count} 个包")
        
        session.close()
        
    except Exception as e:
        logger.error(f"检查应用版本时发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        sys.exit(1)
    
    logger.info("检查完成")

if __name__ == "__main__":
    main()