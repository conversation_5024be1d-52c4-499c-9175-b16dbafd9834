#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的process_txt_files函数
"""

import os
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加路径以便导入
sys.path.append('/home/<USER>/dev/tenyy-dind')

def test_real_process_txt_files():
    """测试真实的process_txt_files函数"""
    
    print("=== 测试真实的process_txt_files函数 ===")
    
    try:
        # 导入真实的函数
        from tenyy.src.apkinfo_analysis.apk_analysis_flow.apk_analysis_flow import process_txt_files
        
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        print(f"测试目录: {current_dir}")
        
        # 检查测试文件是否存在
        txt_files = [f for f in os.listdir(current_dir) if f.endswith('.txt') and f.startswith('class_example')]
        print(f"找到测试文件: {txt_files}")
        
        if len(txt_files) < 3:
            print(f"警告：只找到 {len(txt_files)} 个测试文件，期望至少3个")
        
        # 运行真实的函数
        print("\n开始运行process_txt_files函数...")
        result = process_txt_files(current_dir)
        
        if result:
            print("✅ process_txt_files函数执行成功")
        else:
            print("❌ process_txt_files函数执行失败")
        
        return result
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("可能需要设置正确的Python路径或安装依赖")
        return False
    except Exception as e:
        print(f"执行错误: {e}")
        return False

if __name__ == "__main__":
    test_real_process_txt_files()
