#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的修复后流程
"""

import os
import json
import re
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BlackListFilter:
    """黑名单过滤器，用于过滤系统和标准库包名"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.patterns = self._load_blacklist_patterns()
    
    def _load_blacklist_patterns(self) -> List[re.Pattern]:
        """加载黑名单正则表达式模式"""
        patterns = []
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        pattern = re.compile(line)
                        patterns.append(pattern)
        except Exception as e:
            logger.error(f"加载黑名单配置文件失败: {e}")
        logger.info(f"总共加载了 {len(patterns)} 个黑名单模式")
        return patterns
    
    def is_blacklisted(self, package_name: str) -> bool:
        """检查包名是否在黑名单中"""
        for pattern in self.patterns:
            if pattern.match(package_name):
                return True
        return False

def test_complete_flow():
    """测试完整的修复后流程"""
    
    print("=== 测试完整的修复后流程 ===")
    
    # 初始化黑名单过滤器
    script_dir = os.path.dirname(os.path.abspath(__file__))
    blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
    
    # 查找测试文件
    txt_files = [f for f in os.listdir(script_dir) if f.endswith('.txt') and f.startswith('class_example')]
    
    if not txt_files:
        print("没有找到测试文件")
        return False
    
    print(f"找到 {len(txt_files)} 个测试文件: {txt_files}")
    
    results = []
    
    # 处理每个文件
    for txt_file in sorted(txt_files):
        file_path = os.path.join(script_dir, txt_file)
        print(f"\n=== 处理文件: {txt_file} ===")
        
        try:
            # 读取包名列表
            with open(file_path, 'r', encoding='utf-8') as f:
                packages = json.load(f)
            
            print(f"从{txt_file}读取到{len(packages)}个包名")
            
            # 统一包名格式（将斜杠替换为点号）- 这是修复的关键
            formatted_packages = [pkg.replace('/', '.') for pkg in packages]
            
            # 在进行SDK识别前，先对格式化后的包名应用黑名单过滤
            filtered_packages = []
            blacklisted_count = 0
            blacklisted_examples = []
            
            for package in formatted_packages:
                if not blacklist_filter.is_blacklisted(package):
                    filtered_packages.append(package)
                else:
                    blacklisted_count += 1
                    # 收集一些被过滤的例子
                    if len(blacklisted_examples) < 10:
                        blacklisted_examples.append(package)
            
            print(f"从格式化后的包名中过滤掉 {blacklisted_count} 个黑名单包名")
            print(f"过滤前包名数量: {len(formatted_packages)}, 过滤后包名数量: {len(filtered_packages)}")
            
            # 显示被过滤的例子
            if blacklisted_examples:
                print(f"被过滤的包名示例: {blacklisted_examples[:5]}")
            
            # 检查特定的问题包名是否被正确过滤
            problem_packages_check = []
            for pkg in ['androidx.core', 'androidx.core.app', 'android.support.v4.app', 'com.google.gson']:
                if pkg in formatted_packages:
                    was_filtered = pkg not in filtered_packages
                    problem_packages_check.append(f"{pkg}: {'✅ 被过滤' if was_filtered else '❌ 未被过滤'}")
            
            if problem_packages_check:
                print("问题包名检查:")
                for check in problem_packages_check:
                    print(f"  {check}")
            
            # 统计各类包名的过滤情况
            categories = {
                'androidx': 0,
                'android.support': 0,
                'com.google': 0,
                'java': 0,
                'kotlin': 0,
                'other': 0
            }
            
            for pkg in formatted_packages:
                if blacklist_filter.is_blacklisted(pkg):
                    if pkg.startswith('androidx.'):
                        categories['androidx'] += 1
                    elif pkg.startswith('android.support.'):
                        categories['android.support'] += 1
                    elif pkg.startswith('com.google.'):
                        categories['com.google'] += 1
                    elif pkg.startswith('java.'):
                        categories['java'] += 1
                    elif pkg.startswith('kotlin'):
                        categories['kotlin'] += 1
                    else:
                        categories['other'] += 1
            
            print("各类包名过滤统计:")
            for category, count in categories.items():
                if count > 0:
                    print(f"  {category}: {count} 个")
            
            # 记录结果
            file_result = {
                'file': txt_file,
                'total_packages': len(packages),
                'formatted_packages': len(formatted_packages),
                'filtered_packages': len(filtered_packages),
                'blacklisted_count': blacklisted_count,
                'categories': categories
            }
            results.append(file_result)
            
            print(f"{txt_file} 处理完成")
            
        except Exception as e:
            print(f"处理文件{txt_file}时出错: {e}")
            continue
    
    # 总结
    print(f"\n=== 处理总结 ===")
    total_packages = sum(r['total_packages'] for r in results)
    total_filtered = sum(r['filtered_packages'] for r in results)
    total_blacklisted = sum(r['blacklisted_count'] for r in results)
    
    print(f"总共处理了 {len(results)} 个文件")
    print(f"总包名数量: {total_packages}")
    print(f"总过滤数量: {total_blacklisted}")
    print(f"总剩余数量: {total_filtered}")
    print(f"过滤率: {total_blacklisted/total_packages*100:.1f}%")
    
    # 汇总各类包名过滤情况
    total_categories = {}
    for result in results:
        for category, count in result['categories'].items():
            total_categories[category] = total_categories.get(category, 0) + count
    
    print("\n总体各类包名过滤统计:")
    for category, count in total_categories.items():
        if count > 0:
            print(f"  {category}: {count} 个")
    
    # 验证修复是否成功
    androidx_filtered = total_categories.get('androidx', 0)
    android_support_filtered = total_categories.get('android.support', 0)
    
    success = androidx_filtered > 0 and android_support_filtered > 0
    
    print(f"\n=== 修复验证 ===")
    if success:
        print("✅ 修复成功！androidx和android.support包名现在能被正确过滤")
        print(f"  - androidx包名过滤: {androidx_filtered} 个")
        print(f"  - android.support包名过滤: {android_support_filtered} 个")
    else:
        print("❌ 修复验证失败，需要进一步检查")
    
    return success

if __name__ == "__main__":
    test_complete_flow()
