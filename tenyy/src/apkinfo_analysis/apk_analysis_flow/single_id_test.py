#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个 app_version ID 测试程序
测试 apk_analysis_flow 和 sdk_match_flow 的两层过滤流程
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入必要的模块
from sqlalchemy import create_engine, or_
from sqlalchemy.orm import sessionmaker
from tenyy.src.config.settings import Settings
from tenyy.src.models.app_version import AppVersion
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage

# 导入分析流程模块
from apk_analysis_flow import BlackListFilter, SDKIdentifier, AppDataExtractor
from sdk_match_flow import SDKMatcher

class SingleAppTester:
    """单个应用测试器"""
    
    def __init__(self, app_version_id: int):
        self.app_version_id = app_version_id
        self.settings = Settings()
        self.engine = create_engine(self.settings.DATABASE_URL)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 初始化黑名单过滤器
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
        
        # 初始化SDK识别器和数据提取器
        self.sdk_identifier = SDKIdentifier(self.blacklist_filter)
        self.data_extractor = AppDataExtractor(self.blacklist_filter)
        
        # 初始化SDK匹配器
        self.sdk_matcher = SDKMatcher()
        
        # 结果存储
        self.results = {
            'app_version_id': app_version_id,
            'timestamp': datetime.now().isoformat(),
            'step1_apk_analysis': {},
            'step2_sdk_matching': {},
            'summary': {}
        }
        
        logger.info(f"初始化单个应用测试器，app_version_id: {app_version_id}")
    
    def get_app_version_info(self) -> Optional[AppVersion]:
        """获取应用版本信息"""
        with self.SessionLocal() as session:
            app_version = session.query(AppVersion).filter(
                AppVersion.id == self.app_version_id
            ).first()
            
            if not app_version:
                logger.error(f"未找到 app_version_id: {self.app_version_id}")
                return None
            
            logger.info(f"找到应用版本: {app_version.app_name} v{app_version.version_name}")
            return app_version
    
    def extract_packages_from_app_version(self, app_version: AppVersion) -> List[str]:
        """从AppVersion记录中提取包名列表"""
        try:
            packages_class = getattr(app_version, 'packages_class', None)
            if not packages_class:
                logger.warning("packages_class 为空")
                return []
            
            # 解析包名数据
            if isinstance(packages_class, str):
                packages_data = json.loads(packages_class)
            else:
                packages_data = packages_class
            
            # 提取包名列表
            if isinstance(packages_data, dict):
                packages = list(packages_data.keys())
            elif isinstance(packages_data, list):
                packages = packages_data
            else:
                logger.error(f"不支持的packages_class格式: {type(packages_data)}")
                return []
            
            # 去重并排序
            unique_packages = sorted(list(set(packages)))
            logger.info(f"提取到 {len(unique_packages)} 个唯一包名")
            
            return unique_packages
            
        except Exception as e:
            logger.error(f"提取包名失败: {e}")
            return []
    
    def step1_apk_analysis_flow(self, app_version: AppVersion) -> Dict[str, Any]:
        """第一步：APK分析流程（黑名单过滤 + SDK识别）"""
        logger.info("=" * 60)
        logger.info("第一步：APK分析流程")
        logger.info("=" * 60)
        
        step1_results = {
            'original_packages_count': 0,
            'formatted_packages_count': 0,
            'blacklist_filtered_count': 0,
            'remaining_packages_count': 0,
            'identified_sdks_count': 0,
            'identified_sdks': [],
            'discovered_packages_count': 0,
            'blacklist_examples': [],
            'sdk_examples': []
        }
        
        try:
            # 提取包名列表
            packages = self.extract_packages_from_app_version(app_version)
            step1_results['original_packages_count'] = len(packages)
            
            if not packages:
                logger.warning("没有包名数据，跳过分析")
                return step1_results
            
            # 统一包名格式（将斜杠替换为点号）
            formatted_packages = [pkg.replace('/', '.') for pkg in packages]
            step1_results['formatted_packages_count'] = len(formatted_packages)
            
            # 应用黑名单过滤
            filtered_packages = []
            blacklisted_packages = []
            
            for package in formatted_packages:
                if not self.blacklist_filter.is_blacklisted(package):
                    filtered_packages.append(package)
                else:
                    blacklisted_packages.append(package)
            
            step1_results['blacklist_filtered_count'] = len(blacklisted_packages)
            step1_results['remaining_packages_count'] = len(filtered_packages)
            step1_results['blacklist_examples'] = blacklisted_packages[:10]  # 前10个例子
            
            logger.info(f"原始包名数量: {len(packages)}")
            logger.info(f"格式化后包名数量: {len(formatted_packages)}")
            logger.info(f"黑名单过滤数量: {len(blacklisted_packages)}")
            logger.info(f"剩余包名数量: {len(filtered_packages)}")
            
            # SDK识别
            identified_sdks = self.sdk_identifier.identify_sdks(filtered_packages)
            step1_results['identified_sdks_count'] = len(identified_sdks)
            step1_results['identified_sdks'] = [sdk['package_name'] for sdk in identified_sdks]
            step1_results['sdk_examples'] = step1_results['identified_sdks'][:10]
            
            logger.info(f"识别出的SDK数量: {len(identified_sdks)}")
            if identified_sdks:
                logger.info(f"SDK示例: {step1_results['sdk_examples']}")
            
            # 提取其他类型的数据
            discovered_packages = self.data_extractor.extract_all_data(app_version)
            step1_results['discovered_packages_count'] = len(discovered_packages)
            
            logger.info(f"发现包数量: {len(discovered_packages)}")
            
        except Exception as e:
            logger.error(f"第一步分析失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        return step1_results
    
    def step2_sdk_matching_flow(self) -> Dict[str, Any]:
        """第二步：SDK匹配流程（知识库匹配 + 潜在SDK分析）"""
        logger.info("=" * 60)
        logger.info("第二步：SDK匹配流程")
        logger.info("=" * 60)
        
        step2_results = {
            'discovered_packages_count': 0,
            'matched_sdks_count': 0,
            'matched_sdks': [],
            'unmatched_packages_count': 0,
            'filtered_packages_count': 0,
            'potential_sdks_count': 0,
            'potential_sdks': [],
            'final_sdk_records_count': 0
        }
        
        try:
            # 获取第一步产生的discovered packages
            with self.SessionLocal() as session:
                discovered_packages = session.query(class_AppDiscoveredPackage).filter(
                    class_AppDiscoveredPackage.app_version_id == self.app_version_id
                ).all()
                
                step2_results['discovered_packages_count'] = len(discovered_packages)
                logger.info(f"从数据库获取到 {len(discovered_packages)} 个发现包")
            
            if not discovered_packages:
                logger.warning("没有发现包数据，跳过SDK匹配")
                return step2_results
            
            # 获取SDK知识库
            sdk_knowledge_base = self.sdk_matcher.get_sdk_knowledge_base()
            logger.info(f"获取到 {len(sdk_knowledge_base)} 条SDK知识库记录")
            
            # 包与SDK知识库匹配
            matched_results, unmatched_packages = self.sdk_matcher.match_packages_with_sdk_knowledge(
                discovered_packages, sdk_knowledge_base
            )
            
            step2_results['matched_sdks_count'] = len(matched_results)
            step2_results['matched_sdks'] = [r['sdk_package_prefix'] for r in matched_results]
            step2_results['unmatched_packages_count'] = len(unmatched_packages)
            
            logger.info(f"匹配到 {len(matched_results)} 个已知SDK")
            logger.info(f"未匹配包数量: {len(unmatched_packages)}")
            
            # 过滤下级包
            filtered_packages = self.sdk_matcher.filter_child_packages(matched_results, unmatched_packages)
            step2_results['filtered_packages_count'] = len(filtered_packages)
            
            logger.info(f"过滤后剩余 {len(filtered_packages)} 个包用于潜在SDK分析")
            
            # 分析潜在SDK
            potential_sdks = self.sdk_matcher.analyze_potential_sdks(filtered_packages)
            step2_results['potential_sdks_count'] = len(potential_sdks)
            step2_results['potential_sdks'] = [p['sdk_package_prefix'] for p in potential_sdks]
            
            logger.info(f"发现 {len(potential_sdks)} 个潜在SDK")
            
            # 保存结果到数据库
            self.sdk_matcher.save_results(self.app_version_id, matched_results, potential_sdks)
            
            # 检查最终保存的SDK记录数量
            with self.SessionLocal() as session:
                final_count = session.query(class_AppVersionSDK).filter(
                    class_AppVersionSDK.app_version_id == self.app_version_id
                ).count()
                step2_results['final_sdk_records_count'] = final_count
                
                logger.info(f"最终保存到数据库的SDK记录数量: {final_count}")
            
        except Exception as e:
            logger.error(f"第二步分析失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        return step2_results
    
    def generate_summary(self) -> Dict[str, Any]:
        """生成测试总结"""
        step1 = self.results['step1_apk_analysis']
        step2 = self.results['step2_sdk_matching']
        
        summary = {
            'total_original_packages': step1.get('original_packages_count', 0),
            'blacklist_filtered_packages': step1.get('blacklist_filtered_count', 0),
            'blacklist_filter_rate': 0,
            'step1_identified_sdks': step1.get('identified_sdks_count', 0),
            'step1_discovered_packages': step1.get('discovered_packages_count', 0),
            'step2_matched_sdks': step2.get('matched_sdks_count', 0),
            'step2_potential_sdks': step2.get('potential_sdks_count', 0),
            'final_total_sdks': step2.get('final_sdk_records_count', 0),
            'processing_success': True
        }
        
        # 计算过滤率
        if summary['total_original_packages'] > 0:
            summary['blacklist_filter_rate'] = round(
                summary['blacklist_filtered_packages'] / summary['total_original_packages'] * 100, 2
            )
        
        return summary
    
    def run_test(self) -> bool:
        """运行完整测试"""
        logger.info(f"开始测试 app_version_id: {self.app_version_id}")
        
        try:
            # 获取应用版本信息
            app_version = self.get_app_version_info()
            if not app_version:
                return False
            
            # 清理现有的分析结果
            self.clear_existing_results()
            
            # 第一步：APK分析流程
            self.results['step1_apk_analysis'] = self.step1_apk_analysis_flow(app_version)
            
            # 第二步：SDK匹配流程
            self.results['step2_sdk_matching'] = self.step2_sdk_matching_flow()
            
            # 生成总结
            self.results['summary'] = self.generate_summary()
            
            # 保存结果到文件
            self.save_results_to_file()
            
            logger.info("测试完成")
            return True
            
        except Exception as e:
            logger.error(f"测试过程中出现异常: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def clear_existing_results(self):
        """清理现有的分析结果"""
        logger.info("清理现有的分析结果...")
        
        with self.SessionLocal() as session:
            # 删除现有的SDK记录
            sdk_deleted = session.query(class_AppVersionSDK).filter(
                class_AppVersionSDK.app_version_id == self.app_version_id
            ).delete()
            
            # 删除现有的发现包记录
            packages_deleted = session.query(class_AppDiscoveredPackage).filter(
                class_AppDiscoveredPackage.app_version_id == self.app_version_id
            ).delete()
            
            session.commit()
            
            logger.info(f"清理完成：删除了 {sdk_deleted} 条SDK记录，{packages_deleted} 条发现包记录")
    
    def save_results_to_file(self):
        """保存结果到文件"""
        output_file = os.path.join(os.path.dirname(__file__), 'single_result.txt')
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write(f"单个应用版本测试结果\n")
                f.write(f"App Version ID: {self.app_version_id}\n")
                f.write(f"测试时间: {self.results['timestamp']}\n")
                f.write("=" * 80 + "\n\n")
                
                # 第一步结果
                step1 = self.results['step1_apk_analysis']
                f.write("第一步：APK分析流程结果\n")
                f.write("-" * 40 + "\n")
                f.write(f"原始包名数量: {step1.get('original_packages_count', 0)}\n")
                f.write(f"格式化后包名数量: {step1.get('formatted_packages_count', 0)}\n")
                f.write(f"黑名单过滤数量: {step1.get('blacklist_filtered_count', 0)}\n")
                f.write(f"剩余包名数量: {step1.get('remaining_packages_count', 0)}\n")
                f.write(f"识别出的SDK数量: {step1.get('identified_sdks_count', 0)}\n")
                f.write(f"发现包数量: {step1.get('discovered_packages_count', 0)}\n")
                
                if step1.get('blacklist_examples'):
                    f.write(f"\n黑名单过滤示例（前10个）:\n")
                    for i, pkg in enumerate(step1['blacklist_examples'], 1):
                        f.write(f"  {i}. {pkg}\n")
                
                if step1.get('sdk_examples'):
                    f.write(f"\n识别出的SDK示例（前10个）:\n")
                    for i, sdk in enumerate(step1['sdk_examples'], 1):
                        f.write(f"  {i}. {sdk}\n")
                
                # 第二步结果
                step2 = self.results['step2_sdk_matching']
                f.write(f"\n第二步：SDK匹配流程结果\n")
                f.write("-" * 40 + "\n")
                f.write(f"发现包数量: {step2.get('discovered_packages_count', 0)}\n")
                f.write(f"匹配到的已知SDK数量: {step2.get('matched_sdks_count', 0)}\n")
                f.write(f"未匹配包数量: {step2.get('unmatched_packages_count', 0)}\n")
                f.write(f"过滤后包数量: {step2.get('filtered_packages_count', 0)}\n")
                f.write(f"潜在SDK数量: {step2.get('potential_sdks_count', 0)}\n")
                f.write(f"最终SDK记录数量: {step2.get('final_sdk_records_count', 0)}\n")
                
                if step2.get('matched_sdks'):
                    f.write(f"\n匹配到的已知SDK:\n")
                    for i, sdk in enumerate(step2['matched_sdks'], 1):
                        f.write(f"  {i}. {sdk}\n")
                
                if step2.get('potential_sdks'):
                    f.write(f"\n发现的潜在SDK:\n")
                    for i, sdk in enumerate(step2['potential_sdks'], 1):
                        f.write(f"  {i}. {sdk}\n")
                
                # 总结
                summary = self.results['summary']
                f.write(f"\n测试总结\n")
                f.write("=" * 40 + "\n")
                f.write(f"总原始包数量: {summary.get('total_original_packages', 0)}\n")
                f.write(f"黑名单过滤数量: {summary.get('blacklist_filtered_packages', 0)}\n")
                f.write(f"黑名单过滤率: {summary.get('blacklist_filter_rate', 0)}%\n")
                f.write(f"第一步识别SDK数量: {summary.get('step1_identified_sdks', 0)}\n")
                f.write(f"第一步发现包数量: {summary.get('step1_discovered_packages', 0)}\n")
                f.write(f"第二步匹配SDK数量: {summary.get('step2_matched_sdks', 0)}\n")
                f.write(f"第二步潜在SDK数量: {summary.get('step2_potential_sdks', 0)}\n")
                f.write(f"最终总SDK数量: {summary.get('final_total_sdks', 0)}\n")
                f.write(f"处理成功: {'是' if summary.get('processing_success', False) else '否'}\n")
                
                # 完整的JSON结果
                f.write(f"\n完整JSON结果:\n")
                f.write("=" * 40 + "\n")
                f.write(json.dumps(self.results, ensure_ascii=False, indent=2))
            
            logger.info(f"结果已保存到: {output_file}")
            
        except Exception as e:
            logger.error(f"保存结果文件失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python single_id_test.py <app_version_id>")
        print("示例: python single_id_test.py 12345")
        sys.exit(1)
    
    try:
        app_version_id = int(sys.argv[1])
    except ValueError:
        print("错误: app_version_id 必须是一个整数")
        sys.exit(1)
    
    # 创建测试器并运行测试
    tester = SingleAppTester(app_version_id)
    success = tester.run_test()
    
    if success:
        print(f"✅ 测试成功完成，结果已保存到 single_result.txt")
        sys.exit(0)
    else:
        print(f"❌ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
