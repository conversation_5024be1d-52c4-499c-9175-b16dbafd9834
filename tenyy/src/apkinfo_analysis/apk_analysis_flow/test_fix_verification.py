#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证黑名单过滤修复效果
"""

import os
import json
import re
import logging
from typing import List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BlackListFilter:
    """黑名单过滤器，用于过滤系统和标准库包名"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.patterns = self._load_blacklist_patterns()
    
    def _load_blacklist_patterns(self) -> List[re.Pattern]:
        """加载黑名单正则表达式模式"""
        patterns = []
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if line and not line.startswith('#'):
                        pattern = re.compile(line)
                        patterns.append(pattern)
        except Exception as e:
            logger.error(f"加载黑名单配置文件失败: {e}")
        logger.info(f"总共加载了 {len(patterns)} 个黑名单模式")
        return patterns
    
    def is_blacklisted(self, package_name: str) -> bool:
        """检查包名是否在黑名单中"""
        for pattern in self.patterns:
            if pattern.match(package_name):
                return True
        return False

def test_fixed_logic():
    """测试修复后的逻辑"""
    
    print("=== 测试修复后的黑名单过滤逻辑 ===")
    
    # 初始化黑名单过滤器
    script_dir = os.path.dirname(os.path.abspath(__file__))
    blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
    
    # 读取测试数据
    test_file = os.path.join(script_dir, 'class_example2.txt')
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return False
    
    with open(test_file, 'r', encoding='utf-8') as f:
        packages = json.load(f)
    
    print(f"总包名数量: {len(packages)}")
    
    # 模拟修复前的逻辑（直接对斜杠格式应用黑名单）
    print("\n--- 修复前的逻辑 ---")
    old_filtered_packages = []
    old_blacklisted_count = 0
    for package in packages:
        if not blacklist_filter.is_blacklisted(package):
            old_filtered_packages.append(package)
        else:
            old_blacklisted_count += 1
    
    print(f"修复前：过滤掉 {old_blacklisted_count} 个黑名单包名")
    print(f"修复前：剩余 {len(old_filtered_packages)} 个包名")
    
    # 检查androidx包名是否被过滤
    old_androidx_remaining = [pkg for pkg in old_filtered_packages if 'androidx' in pkg]
    print(f"修复前：剩余的androidx包名数量: {len(old_androidx_remaining)}")
    if len(old_androidx_remaining) > 0:
        print(f"修复前：前5个剩余的androidx包名: {old_androidx_remaining[:5]}")
    
    # 模拟修复后的逻辑（先转换格式再应用黑名单）
    print("\n--- 修复后的逻辑 ---")
    # 统一包名格式（将斜杠替换为点号）
    formatted_packages = [pkg.replace('/', '.') for pkg in packages]
    
    # 应用黑名单过滤
    new_filtered_packages = []
    new_blacklisted_count = 0
    for package in formatted_packages:
        if not blacklist_filter.is_blacklisted(package):
            new_filtered_packages.append(package)
        else:
            new_blacklisted_count += 1
    
    print(f"修复后：过滤掉 {new_blacklisted_count} 个黑名单包名")
    print(f"修复后：剩余 {len(new_filtered_packages)} 个包名")
    
    # 检查androidx包名是否被过滤
    new_androidx_remaining = [pkg for pkg in new_filtered_packages if 'androidx' in pkg]
    print(f"修复后：剩余的androidx包名数量: {len(new_androidx_remaining)}")
    if len(new_androidx_remaining) > 0:
        print(f"修复后：前5个剩余的androidx包名: {new_androidx_remaining[:5]}")
    
    # 对比结果
    print(f"\n=== 修复效果对比 ===")
    print(f"过滤效果提升: {new_blacklisted_count - old_blacklisted_count} 个包名")
    print(f"androidx包名过滤效果: {len(old_androidx_remaining) - len(new_androidx_remaining)} 个包名被正确过滤")
    
    # 检查特定的问题包名
    problem_packages = [
        'androidx/core',
        'androidx/core/app', 
        'android/support/v4/app',
        'com/google/gson/Gson'
    ]
    
    print(f"\n=== 特定问题包名检查 ===")
    for pkg in problem_packages:
        if pkg in packages:  # 如果这个包名存在于测试数据中
            old_filtered = pkg in old_filtered_packages
            new_filtered = pkg.replace('/', '.') in new_filtered_packages
            
            print(f"包名: {pkg}")
            print(f"  修复前: {'未被过滤 ❌' if old_filtered else '被过滤 ✅'}")
            print(f"  修复后: {'未被过滤 ❌' if new_filtered else '被过滤 ✅'}")
            print()
    
    # 验证修复是否成功
    success = (new_blacklisted_count > old_blacklisted_count and 
               len(new_androidx_remaining) == 0)
    
    print(f"=== 修复验证结果 ===")
    if success:
        print("✅ 修复成功！androidx.core等包名现在能被正确过滤")
    else:
        print("❌ 修复可能不完整，需要进一步检查")
    
    return success

if __name__ == "__main__":
    test_fixed_logic()
