#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import logging
import math
import os
import re
import xml.etree.ElementTree as ET
from collections import defaultdict, Counter
from math import log2
from typing import List, Dict, Set, Optional, Tuple, Union, Any
from datetime import datetime

# 添加数据库相关导入
from sqlalchemy import create_engine, or_
from sqlalchemy.orm import sessionmaker
# 修复导入路径
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from tenyy.src.models.app_version import AppVersion
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage
from tenyy.src.config.settings import Settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BlackListFilter:
    """黑名单过滤器，用于过滤系统和标准库包名"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.patterns = self._load_blacklist_patterns()
    
    def _load_blacklist_patterns(self) -> List[re.Pattern]:
        """加载黑名单正则表达式模式"""
        patterns = []
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if line and not line.startswith('#'):
                        # 直接使用配置文件中的正则表达式模式
                        # 确保模式匹配整个包名前缀
                        pattern = re.compile(line)
                        patterns.append(pattern)
                        logger.debug(f"加载黑名单模式: {line}")
        except Exception as e:
            logger.error(f"加载黑名单配置文件失败: {e}")
        logger.info(f"总共加载了 {len(patterns)} 个黑名单模式")
        return patterns
    
    def is_blacklisted(self, package_name: str) -> bool:
        """检查包名是否在黑名单中"""
        logger.debug(f"检查包名是否在黑名单中: {package_name}")
        for pattern in self.patterns:
            # 使用match确保从包名开始匹配
            if pattern.match(package_name):
                logger.debug(f"包 {package_name} 被黑名单 {pattern.pattern} 匹配")
                return True
        logger.debug(f"包 {package_name} 未被任何黑名单模式匹配")
        return False


class TrieNode:
    """Trie树节点，用于构建包层级图"""
    def __init__(self, prefix: str):
        self.prefix = prefix
        self.children: Dict[str, 'TrieNode'] = {}
        self.count = 0
        self.breakpoint_score = 0.0
        self.is_breakpoint_score_computed = False
        self.is_virtual_root = False  # 标记是否为虚拟根包
        
    def __repr__(self):
        return f"TrieNode(prefix='{self.prefix}', count={self.count}, children={len(self.children)})"


class PackageHierarchyGraph:
    """包层级图（PHG）构建器"""
    
    def __init__(self, blacklist_filter: Optional[BlackListFilter] = None):
        self.root = TrieNode("")
        self.blacklist_filter = blacklist_filter
        
    def build_from_packages(self, packages: List[str]) -> TrieNode:
        """从包名列表构建PHG，包含虚拟根包补全"""
        logger.info(f"开始构建包层级图，包名数量: {len(packages)}")

        # 第一步：正常构建包层级图
        sorted_packages = sorted(packages)
        for package_name in sorted_packages:
            self._add_package(package_name)

        # 第二步：虚拟根包补全算法
        self._complete_missing_root_packages(packages)

        logger.info(f"包层级图构建完成，根节点子节点数: {len(self.root.children)}")
        return self.root

    def _complete_missing_root_packages(self, packages: List[str]):
        """虚拟根包补全算法：自动推断和补全缺失的根包"""
        # 分析包结构，找出可能缺失的根包
        potential_roots = {}

        for package in packages:
            parts = package.replace('/', '.').split('.')

            # 检查每个可能的根包路径
            for depth in range(2, min(len(parts), 6)):  # 检查深度2-5的根包
                root_path = '.'.join(parts[:depth])

                if root_path not in potential_roots:
                    potential_roots[root_path] = {
                        'subpackages': [],
                        'max_depth': 0,
                        'total_descendants': 0
                    }

                potential_roots[root_path]['subpackages'].append(package)
                potential_roots[root_path]['max_depth'] = max(
                    potential_roots[root_path]['max_depth'],
                    len(parts) - depth
                )

        # 识别需要补全的根包
        for root_path, info in potential_roots.items():
            # 条件：有多个子包，且根包不存在，且结构复杂度足够
            if (len(info['subpackages']) >= 3 and  # 至少3个子包
                root_path.replace('.', '/') not in packages and  # 根包不存在
                info['max_depth'] >= 2):  # 有足够的深度

                # 检查是否在黑名单中
                if self.blacklist_filter and self.blacklist_filter.is_blacklisted(root_path):
                    logger.debug(f"跳过黑名单中的虚拟根包: {root_path}")
                    continue

                # 创建虚拟根包节点
                self._create_virtual_root_package(root_path, info)
                logger.info(f"补全虚拟根包: {root_path} (子包数: {len(info['subpackages'])})")

    def _is_blacklisted_virtual_root(self, package_name: str) -> bool:
        """检查虚拟根包是否在黑名单中"""
        # 使用传入的黑名单过滤器来检查
        if self.blacklist_filter:
            return self.blacklist_filter.is_blacklisted(package_name)
        return False

    def _create_virtual_root_package(self, root_path: str, info: Dict):
        """创建虚拟根包节点"""
        parts = root_path.split('.')
        current_node = self.root

        # 逐级创建或找到节点
        for i, part in enumerate(parts):
            current_prefix = '.'.join(parts[:i+1])

            if part not in current_node.children:
                current_node.children[part] = TrieNode(current_prefix)

            current_node = current_node.children[part]

            # 为虚拟根包设置推算的计数
            if i == len(parts) - 1:  # 最后一级，即根包本身
                # 基于子包数量推算虚拟计数
                virtual_count = len(info['subpackages']) * 2  # 推算的虚拟计数
                current_node.count = max(current_node.count, virtual_count)
                # 标记为虚拟根包，用于后续得分加成
                current_node.is_virtual_root = True
    
    def _add_package(self, package_name: str):
        """添加单个包名到PHG"""
        # 统一包名格式（处理斜杠分隔）
        package_name = package_name.replace('/', '.')
        parts = package_name.split('.')
        
        current_node = self.root
        prefix_parts = []
        
        # 逐级构建Trie树
        for part in parts:
            prefix_parts.append(part)
            current_prefix = '.'.join(prefix_parts)
            
            # 更新当前节点计数
            current_node.count += 1
            
            # 创建或移动到子节点
            if part not in current_node.children:
                current_node.children[part] = TrieNode(current_prefix)
            current_node = current_node.children[part]
        
        # 更新叶子节点计数
        current_node.count += 1


class FeatureExtractor:
    """特征提取器，实现文档中的特征工程算法"""
    
    def __init__(self):
        # 预加载的n-gram频率分布模型（简化版本）
        self.ngram_model = self._build_ngram_model()
        
    def _build_ngram_model(self) -> Dict[str, float]:
        """构建基于统计学的n-gram频率分布模型"""
        # 使用英文字母的自然分布频率（基于大量英文文本统计）
        # 这是客观的统计数据，不是硬编码的词库
        english_char_freq = {
            'a': 8.12, 'b': 1.49, 'c': 2.78, 'd': 4.25, 'e': 12.02, 'f': 2.23,
            'g': 2.02, 'h': 6.09, 'i': 6.97, 'j': 0.15, 'k': 0.77, 'l': 4.03,
            'm': 2.41, 'n': 6.75, 'o': 7.51, 'p': 1.93, 'q': 0.10, 'r': 5.99,
            's': 6.33, 't': 9.06, 'u': 2.76, 'v': 0.98, 'w': 2.36, 'x': 0.15,
            'y': 1.97, 'z': 0.07
        }

        # 基于字符频率生成trigram概率模型
        ngram_freq = defaultdict(float)

        # 生成所有可能的trigram并基于字符频率计算概率
        for c1 in english_char_freq:
            for c2 in english_char_freq:
                for c3 in english_char_freq:
                    trigram = c1 + c2 + c3
                    # 使用几何平均值计算trigram频率
                    freq = (english_char_freq[c1] * english_char_freq[c2] * english_char_freq[c3]) ** (1/3)
                    ngram_freq[trigram] = freq / 100.0  # 归一化

        return dict(ngram_freq)
    
    def extract_features(self, node: TrieNode, phg_root: TrieNode) -> Dict[str, float]:
        """为节点提取所有特征"""
        features = {}
        
        # 结构内聚度量
        features.update(self._extract_cohesion_features(node))
        
        # 结构耦合度量
        features.update(self._extract_coupling_features(node, phg_root))
        
        # 统计性名称分析
        features.update(self._extract_obfuscation_features(node))
        
        return features
    
    def _extract_cohesion_features(self, node: TrieNode) -> Dict[str, float]:
        """提取结构内聚度量特征"""
        features = {}
        
        # 扇出（Fan-Out）
        features['fan_out'] = len(node.children)
        
        # 子树规模（Descendant Count）
        features['descendant_count'] = self._count_descendants(node)
        
        # 子树最大深度
        features['max_depth'] = self._calculate_max_depth(node)
        
        # 平均分支因子
        features['avg_branching_factor'] = self._calculate_avg_branching_factor(node)
        
        return features
    
    def _extract_coupling_features(self, node: TrieNode, root: TrieNode) -> Dict[str, float]:
        """提取结构耦合度量特征"""
        features = {}
        
        # 节点深度
        features['node_depth'] = self._calculate_node_depth(node)
        
        # 单链子路径长度
        features['singleton_path_length'] = self._calculate_singleton_path_length(node)
        
        return features
    
    def _extract_obfuscation_features(self, node: TrieNode) -> Dict[str, float]:
        """提取统计性名称分析特征"""
        features = {}
        
        if not node.prefix:
            return {'name_anomaly_score': 0.0, 'component_entropy': 0.0, 
                   'digit_ratio': 0.0, 'length_penalty': 0.0}
        
        # 获取最后一个组件名称
        component_name = node.prefix.split('.')[-1] if '.' in node.prefix else node.prefix
        
        # 名称异常度（基于n-gram分析）
        features['name_anomaly_score'] = self._calculate_name_anomaly_score(component_name)
        
        # 组件熵
        features['component_entropy'] = self._calculate_entropy(component_name)
        
        # 数字占比
        features['digit_ratio'] = self._calculate_digit_ratio(component_name)
        
        # 长度惩罚
        features['length_penalty'] = 1.0 if len(component_name) <= 2 else 0.0
        
        return features
    
    def _count_descendants(self, node: TrieNode) -> int:
        """计算节点的所有后代数量"""
        count = 0
        for child in node.children.values():
            count += 1 + self._count_descendants(child)
        return count
    
    def _calculate_max_depth(self, node: TrieNode) -> int:
        """计算子树的最大深度"""
        if not node.children:
            return 0
        return 1 + max(self._calculate_max_depth(child) for child in node.children.values())
    
    def _calculate_avg_branching_factor(self, node: TrieNode) -> float:
        """计算平均分支因子"""
        if not node.children:
            return 0.0
            
        total_children = 0
        total_nodes = 0
        
        def traverse(n):
            nonlocal total_children, total_nodes
            total_nodes += 1
            total_children += len(n.children)
            for child in n.children.values():
                traverse(child)
        
        traverse(node)
        return total_children / total_nodes if total_nodes > 0 else 0.0
    
    def _calculate_node_depth(self, node: TrieNode) -> int:
        """计算节点深度"""
        if not node.prefix:
            return 0
        return len(node.prefix.split('.'))
    
    def _calculate_singleton_path_length(self, node: TrieNode) -> int:
        """计算单链子路径长度"""
        length = 0
        current = node
        
        while len(current.children) == 1:
            length += 1
            current = next(iter(current.children.values()))
            
        return length
    
    def _calculate_name_anomaly_score(self, name: str) -> float:
        """基于n-gram模型计算名称异常度"""
        if len(name) < 3:
            return 1.0  # 短名称被认为是异常的
            
        scores = []
        for i in range(len(name) - 2):
            trigram = name[i:i+3].lower()
            # 使用对数概率，未知trigram给予低分
            score = self.ngram_model.get(trigram, 1e-6)
            scores.append(math.log(score))
            
        # 返回几何平均值的负值（越低越异常）
        if scores:
            avg_log_prob = sum(scores) / len(scores)
            return max(0.0, -avg_log_prob / 10.0)  # 归一化到0-1范围
        return 1.0
    
    def _calculate_entropy(self, s: str) -> float:
        """计算字符串的香农熵"""
        if not s:
            return 0.0
            
        # 计算每个字符的频率
        freq = Counter(s)
        entropy = 0.0
        total = len(s)
        
        for count in freq.values():
            p = count / total
            if p > 0:
                entropy -= p * log2(p)
                
        return entropy
    
    def _calculate_digit_ratio(self, s: str) -> float:
        """计算数字字符占比"""
        if not s:
            return 0.0
        digit_count = sum(c.isdigit() for c in s)
        return digit_count / len(s)


class SDKIdentifier:
    """SDK识别器，实现多因子评分与分类算法"""

    def __init__(self, blacklist_filter: Optional[BlackListFilter] = None):
        self.feature_extractor = FeatureExtractor()
        self.blacklist_filter = blacklist_filter

        # 权重配置（基于文档中的设计哲学）
        self.weights = {
            'cohesion': 0.6,      # 内聚度权重最高
            'coupling': 0.2,      # 耦合度权重中等
            'obfuscation': 0.2    # 混淆度权重较低
        }

    def identify_sdks(self, packages: List[str], main_package: Optional[str] = None) -> List[Dict]:
        """识别SDK根包名"""
        logger.info(f"开始SDK识别流程，输入包名数量: {len(packages)}")
        if main_package:
            logger.info(f"应用主包名: {main_package}")
        
        # 添加调试信息，检查输入包中是否包含android.support相关包
        support_packages = [p for p in packages if 'android.support' in p]
        if support_packages:
            logger.info(f"输入包中包含android.support相关包: {support_packages[:10]}")  # 只显示前10个

        # 第一阶段：构建包层级图
        phg_builder = PackageHierarchyGraph(self.blacklist_filter)
        root = phg_builder.build_from_packages(packages)

        # 第二阶段：生成候选集
        candidates = self._generate_candidates(root)
        logger.info(f"生成候选节点数量: {len(candidates)}")
        
        # 检查候选集中是否包含android.support相关节点
        support_candidates = [c for c in candidates if c.prefix and 'android.support' in c.prefix]
        if support_candidates:
            logger.info(f"候选集中包含android.support相关节点: {[c.prefix for c in support_candidates]}")

        # 第三阶段：特征提取和评分
        scored_candidates = []

        for candidate in candidates:
            features = self.feature_extractor.extract_features(candidate, root)
            score = self._calculate_sdk_score(features, candidate)

            scored_candidates.append({
                'node': candidate,
                'features': features,
                'score': score,
                'package_name': candidate.prefix
            })
            
            # 如果是android.support相关包，记录详细信息
            if candidate.prefix and 'android.support' in candidate.prefix:
                logger.info(f"android.support候选包详情: {candidate.prefix}, 得分: {score}")

        # 第四阶段：动态阈值分类
        sdk_candidates = self._apply_dynamic_threshold(scored_candidates)
        logger.info(f"通过动态阈值的SDK候选数量: {len(sdk_candidates)}")
        
        # 检查通过阈值的SDK中是否包含android.support
        support_sdk_candidates = [c for c in sdk_candidates if 'android.support' in c['package_name']]
        if support_sdk_candidates:
            logger.info(f"通过阈值的android.support SDK: {[c['package_name'] for c in support_sdk_candidates]}")

        # 移除调试代码

        # 第五阶段：后处理与精炼
        final_sdks = self._post_process(sdk_candidates)
        logger.info(f"最终识别的SDK数量: {len(final_sdks)}")
        
        # 检查最终结果中是否包含android.support
        support_final_sdks = [s for s in final_sdks if 'android.support' in s['package_name']]
        if support_final_sdks:
            logger.info(f"最终识别的android.support SDK: {[s['package_name'] for s in support_final_sdks]}")

        # 转换为标准格式输出
        result = []
        for sdk in final_sdks:
            result.append({
                'package_name': sdk['package_name'],
                'score': sdk['score'],
                'features': sdk['features']
            })
            
            # 记录所有android.support包的输出
            if 'android.support' in sdk['package_name']:
                logger.info(f"输出android.support SDK: {sdk['package_name']}")

        return result

    def _generate_candidates(self, root: TrieNode) -> List[TrieNode]:
        """基于统计特征的自适应候选集生成"""
        candidates = []
        all_nodes = []

        # 收集所有节点的统计信息
        def collect_stats(node, depth=0):
            if depth > 0:  # 排除根节点
                all_nodes.append({
                    'node': node,
                    'depth': depth,
                    'descendants': self.feature_extractor._count_descendants(node),
                    'fan_out': len(node.children)
                })
            for child in node.children.values():
                collect_stats(child, depth + 1)

        collect_stats(root)

        if not all_nodes:
            return candidates

        # 计算统计阈值
        depths = [n['depth'] for n in all_nodes]
        descendants = [n['descendants'] for n in all_nodes]
        fan_outs = [n['fan_out'] for n in all_nodes]

        # 突破性算法：垂直深度权重分析
        # 计算每个节点的"垂直影响力" = 深度 × 后代数量
        vertical_influences = []
        for node_info in all_nodes:
            vertical_influence = node_info['depth'] * node_info['descendants']
            vertical_influences.append(vertical_influence)

        # 动态深度范围：基于垂直影响力分布
        depth_min = 2
        depth_max = max(8, int(sum(depths) / len(depths) + 2))  # 扩大深度范围

        # 计算分位数阈值
        descendants_sorted = sorted(descendants)
        fan_outs_sorted = sorted(fan_outs)
        vertical_influences_sorted = sorted(vertical_influences)

        # 使用50分位数而不是75分位数，降低门槛
        descendant_50th = descendants_sorted[int(len(descendants_sorted) * 0.5)]
        fan_out_50th = fan_outs_sorted[int(len(fan_outs_sorted) * 0.5)]
        vertical_influence_75th = vertical_influences_sorted[int(len(vertical_influences_sorted) * 0.75)]

        descendant_threshold = max(3, min(descendant_50th, 15))  # 降低阈值
        fan_out_threshold = max(1, min(fan_out_50th, 6))        # 降低阈值
        vertical_threshold = max(20, vertical_influence_75th)    # 垂直影响力阈值

        # 基于垂直深度权重的候选筛选
        def traverse(node, depth=0):
            if depth > 0:
                descendants_count = self.feature_extractor._count_descendants(node)
                fan_out = len(node.children)
                vertical_influence = depth * descendants_count  # 垂直影响力

                # 突破性筛选条件：加入垂直深度权重
                # 条件1: 标准SDK特征 - 有一定规模和分支
                condition1 = (descendants_count >= descendant_threshold and fan_out >= 2)

                # 条件2: 大型SDK - 子树规模很大，即使扇出较小
                condition2 = (descendants_count >= descendant_threshold * 2)

                # 条件3: 分支密集型SDK - 扇出很大，即使子树较小
                condition3 = (fan_out >= fan_out_threshold and descendants_count >= descendant_threshold // 2)

                # 条件4: 垂直深度型SDK - 深度×后代数量大，典型的如GMS、Firebase
                condition4 = (vertical_influence >= vertical_threshold and descendants_count >= 3)

                # 条件5: 深层重要SDK - 深度较大且有一定规模（专门针对GMS/Firebase类型）
                condition5 = (depth >= 4 and descendants_count >= 8 and fan_out >= 1)

                is_candidate = (
                    depth_min <= depth <= depth_max and
                    (condition1 or condition2 or condition3 or condition4 or condition5)
                )

                # 增加混淆检测过滤
                if is_candidate and not self._is_obfuscated_package(node):
                    candidates.append(node)

            for child in node.children.values():
                traverse(child, depth + 1)

        traverse(root)

        # 移除调试代码，专注于解决问题

        # 添加深度优先排序
        # 按深度和后代数量排序，优先保留深层包名
        candidates.sort(key=lambda node: (
            -self.feature_extractor._calculate_node_depth(node),
            -self.feature_extractor._count_descendants(node)
        ))
        
        return candidates

    def _debug_find_node(self, root: TrieNode, target_package: str):
        """调试：查找特定包名节点并分析其特征"""
        def find_node(node, path_parts, current_depth=0):
            if not path_parts:
                # 找到目标节点，分析其特征
                descendants = self.feature_extractor._count_descendants(node)
                fan_out = len(node.children)
                vertical_influence = current_depth * descendants

                logger.info(f"调试：包 {target_package} 特征分析:")
                logger.info(f"  - 深度: {current_depth}")
                logger.info(f"  - 后代数量: {descendants}")
                logger.info(f"  - 扇出: {fan_out}")
                logger.info(f"  - 垂直影响力: {vertical_influence}")
                logger.info(f"  - 是否混淆: {self._is_obfuscated_package(node)}")
                return True

            part = path_parts[0]
            if part in node.children:
                return find_node(node.children[part], path_parts[1:], current_depth + 1)
            return False

        parts = target_package.split('.')
        if not find_node(root, parts):
            logger.info(f"调试：在PHG中未找到包 {target_package}")

    def _is_obfuscated_package(self, node: TrieNode) -> bool:
        """增强的混淆检测算法"""
        if not node.prefix:
            return False

        # 获取包名的各个组件
        components = node.prefix.split('.')

        for component in components:
            if not component:
                continue

            # 检测明显的混淆特征
            # 1. 单字符组件
            if len(component) == 1:
                return True

            # 2. 全数字组件
            if component.isdigit():
                return True

            # 3. 字符重复度过高（如 'aaa', 'bbb'）
            if len(set(component)) == 1 and len(component) > 1:
                return True

            # 4. 数字占比过高
            digit_ratio = sum(c.isdigit() for c in component) / len(component)
            if digit_ratio > 0.5:
                return True

            # 5. O和0混合的混淆模式（新增）
            if self._is_o_zero_obfuscation(component):
                return True

            # 6. 无意义的短组件模式（新增）
            if self._is_meaningless_short_component(component):
                return True

            # 7. 增强的熵值检测（调整阈值）
            if len(component) > 2:
                entropy = self.feature_extractor._calculate_entropy(component)
                # 对于短组件使用更严格的熵值阈值
                entropy_threshold = 1.5 if len(component) <= 4 else 1.0
                if entropy < entropy_threshold:
                    return True

        # 检查整个包名模式是否是混淆
        package_name = node.prefix
        
        # 检查是否是类似"OooO00o.OooO00o"的模式
        if '.' in package_name and len(components) >= 2:
            # 如果所有组件都很短且看起来像混淆，则整个包名是混淆的
            obfuscated_components = 0
            for comp in components:
                if (self._is_meaningless_short_component(comp) or 
                    self._is_o_zero_obfuscation(comp) or
                    len(comp) <= 3):
                    obfuscated_components += 1
            
            # 如果超过一半的组件看起来是混淆的，则整个包名是混淆的
            if obfuscated_components / len(components) >= 0.5:
                return True
                
        return False

    def _is_o_zero_obfuscation(self, component: str) -> bool:
        """检测O和0混合的混淆模式"""
        # 检查是否同时包含O和0
        has_o = 'O' in component or 'o' in component
        has_zero = '0' in component

        if has_o and has_zero:
            # 如果同时包含O和0，且长度较短，很可能是混淆
            if len(component) <= 8:
                return True

        # 检查重复的O/0模式
        if len(component) >= 4:
            o_zero_chars = sum(1 for c in component if c in 'Oo0')
            if o_zero_chars / len(component) > 0.7:  # 70%以上是O/0字符
                return True

        # 检查O/0交替模式，如OooO00o
        if len(component) >= 4:
            o_zero_pattern = ''.join(c for c in component if c in 'Oo0')
            if len(o_zero_pattern) >= 3 and any(c.isupper() for c in o_zero_pattern):
                # 包含大小写混合的O/o/0，且长度足够，很可能是混淆
                return True

        # 检查特定的混淆模式如 OooO00o
        if len(component) >= 5:
            # 模式匹配：大小写O和0交替出现
            o_zero_pattern_count = 0
            for i, c in enumerate(component):
                if c in 'Oo0':
                    o_zero_pattern_count += 1
                elif i > 0 and component[i-1] in 'Oo0' and c.islower() and c.isalpha():
                    # O/o/0 后跟小写字母的模式
                    continue
                else:
                    # 其他字符
                    pass
            
            # 如果O/o/0字符占比超过50%，则认为是混淆
            if o_zero_pattern_count / len(component) >= 0.5:
                return True

        return False

    def _is_meaningless_short_component(self, component: str) -> bool:
        """检测无意义的短组件"""
        if len(component) <= 4:
            # 检查是否是无意义的字母组合
            # 如 qm_m, qm_a, qm_b 等

            # 1. 包含下划线的短组件通常是混淆
            if '_' in component and len(component) <= 6:
                return True

            # 2. 重复字符模式（如 aa, bb, cc），但排除常见缩写
            if len(component) == 2 and component[0] == component[1]:
                # 排除常见的重复字母缩写
                common_abbreviations = {'mm', 'qq', 'tt', 'cc', 'ss', 'pp', 'll'}
                if component.lower() not in common_abbreviations:
                    return True

            # 3. 无意义的字母组合模式
            # 检查是否缺乏元音（真实单词通常包含元音）
            vowels = set('aeiouAEIOU')
            if len(component) >= 3 and not any(c in vowels for c in component):
                # 没有元音的3+字符组件可能是混淆
                consonant_ratio = sum(1 for c in component if c.isalpha() and c not in vowels) / len(component)
                if consonant_ratio > 0.8:  # 80%以上是辅音
                    return True

        # 检查类似qm_m.qm_a.qm_b.qm_b的模式
        if len(component) <= 6 and '_' in component:
            parts = component.split('_')
            if len(parts) >= 2:
                # 检查是否是qm_a, qm_b这类模式
                if len(parts[0]) <= 3 and len(parts[1]) <= 2:
                    # 第一部分是短字母组合，第二部分是单字母或双字母
                    return True
                    
        return False

    def _calculate_sdk_score(self, features: Dict[str, float], node = None) -> float:
        """计算SDK的综合评分，优化深度特征权重"""
        # 提取特征
        fan_out = features['fan_out']
        descendants = features['descendant_count']
        depth = features['max_depth']
        branch_factor = features['avg_branching_factor']
        name_anomaly = features['name_anomaly_score']
        
        # 优化特征权重
        # 增加深度权重，减少对分支因子的依赖
        weights = {
            'fan_out': 0.15,          # 扇出权重
            'descendants': 0.2,         # 后代数量权重
            'depth': 0.3,               # 增加深度权重（原0.1）
            'branch_factor': 0.15,      # 分支因子权重
            'name_anomaly': 0.2,        # 名称异常权重
            'length_penalty': 0.0       # 保留长度惩罚因子
        }
        
        # 计算加权得分
        score = (
            weights['fan_out'] * (fan_out / 10) +  # 限制扇出影响
            weights['descendants'] * min(descendants / 50, 1) +  # 缩小后代数量影响
            weights['depth'] * min(depth / 10, 1) +  # 增加深度权重
            weights['branch_factor'] * min(branch_factor / 1.5, 1) +  # 降低分支因子影响
            weights['name_anomaly'] * (1 - name_anomaly)  # 名称异常性越高得分越低
        )
        
        # 添加深度激励机制
        if depth >= 4:
            score += 0.15  # 深度达到4层额外加分
            logger.debug(f"包 {features.get('package_name', '')} 获得深度激励加分")
        
        # 虚拟根包加分：提高虚拟补全的重要SDK得分
        if node and hasattr(node, 'is_virtual_root') and node.is_virtual_root:
            virtual_bonus = 0.02  # 给虚拟根包额外加分
            score += virtual_bonus

        return max(0.0, score)  # 确保得分非负

    def _normalize_features(self, features: Dict[str, float]) -> Dict[str, float]:
        """特征归一化（简化版本）"""
        normalized = {}

        # 简单的min-max归一化
        for key, value in features.items():
            if key in ['descendant_count']:
                normalized[key] = min(1.0, value / 1000.0)  # 假设最大值为1000
            elif key in ['fan_out']:
                normalized[key] = min(1.0, value / 50.0)    # 假设最大值为50
            elif key in ['max_depth']:
                normalized[key] = min(1.0, value / 10.0)    # 假设最大值为10
            elif key in ['node_depth']:
                normalized[key] = min(1.0, value / 8.0)     # 假设最大值为8
            else:
                normalized[key] = min(1.0, max(0.0, value)) # 其他特征已在0-1范围内

        return normalized

    def _apply_dynamic_threshold(self, scored_candidates: List[Dict]) -> List[Dict]:
        """应用自适应动态阈值分类"""
        if not scored_candidates:
            return []

        scores = [c['score'] for c in scored_candidates]

        # 基于统计学的动态阈值算法
        mean_score = sum(scores) / len(scores)
        variance = sum((s - mean_score) ** 2 for s in scores) / len(scores)
        std_score = math.sqrt(variance)

        # 自适应阈值：基于分布特征自动调整
        # 如果分布较为集中（标准差小），使用更严格的阈值
        # 如果分布较为分散（标准差大），使用更宽松的阈值
        # 优化：大幅降低阈值因子，提高覆盖率
        if std_score < 0.05:
            threshold_factor = 0.1  # 大幅降低
        elif std_score > 0.1:
            threshold_factor = 0.3  # 大幅降低
        else:
            threshold_factor = 0.15  # 大幅降低

        threshold = max(
            mean_score + threshold_factor * std_score,
            0.005  # 降低最低阈值保护
        )

        # 基于结构特征的自适应调整
        adjusted_candidates = []
        for candidate in scored_candidates:
            features = candidate['features']

            # 基于结构复杂性的自适应阈值
            # 子树规模大且结构复杂的包更可能是SDK
            complexity_score = (
                features.get('descendant_count', 0) / 100.0 +  # 子树规模
                features.get('fan_out', 0) / 20.0 +            # 扇出
                features.get('max_depth', 0) / 5.0             # 深度
            )

            # 根据复杂性调整阈值
            if complexity_score > 1.0:
                adjusted_threshold = threshold * 0.8  # 复杂结构降低阈值
            elif complexity_score > 0.5:
                adjusted_threshold = threshold * 0.9  # 中等复杂性略微降低阈值
            else:
                adjusted_threshold = threshold

            if candidate['score'] >= adjusted_threshold:
                adjusted_candidates.append(candidate)

        logger.info(f"自适应动态阈值: {threshold:.3f} (均值: {mean_score:.3f}, 标准差: {std_score:.3f})")
        logger.info(f"通过阈值的候选数量: {len(adjusted_candidates)}")

        return adjusted_candidates

    def _post_process(self, sdk_candidates: List[Dict]) -> List[Dict]:
        """后处理与结果精炼"""
        if not sdk_candidates:
            return []

        # 添加深度优先排序
        # 按深度和后代数量排序，优先保留深层包名
        sdk_candidates.sort(key=lambda candidate: (
            -candidate['features'].get('max_depth', 0),
            -candidate['features'].get('descendant_count', 0)
        ))
        
        # 层级剪枝：移除祖先节点
        pruned_candidates = self._hierarchical_pruning(sdk_candidates)

        # 再次排序确保结果顺序
        pruned_candidates.sort(key=lambda candidate: (
            -candidate['features'].get('max_depth', 0),
            -candidate['features'].get('descendant_count', 0)
        ))

        return pruned_candidates

    def _hierarchical_pruning(self, candidates: List[Dict]) -> List[Dict]:
        """基于结构特征的智能层级剪枝"""
        if not candidates:
            return candidates

        # 基于功能密度分析识别真正的SDK
        def is_functional_sdk(candidate):
            """基于功能密度判断是否为功能性SDK"""
            features = candidate['features']
            package_name = candidate['package_name']

            depth_score = features.get('node_depth', 0)
            descendants = features.get('descendant_count', 0)
            fan_out = features.get('fan_out', 0)
            max_depth = features.get('max_depth', 0)

            # 排除过浅的根包（如com.google, com.tencent）
            if depth_score <= 2:
                return False

            # 功能密度分析：真正的SDK在特定深度有高密度的功能模块
            # 计算功能密度 = 扇出 / 深度（在当前层级的功能集中度）
            functional_density = fan_out / max(depth_score - 2, 1)  # 减去域名层级

            # 垂直扩展性：深度×后代数量（SDK的垂直复杂性）
            vertical_complexity = max_depth * descendants

            # 水平扩展性：扇出×后代数量（SDK的水平复杂性）
            horizontal_complexity = fan_out * descendants

            # 结构平衡性：避免过于线性或过于扁平的结构
            if max_depth > 0 and fan_out > 0:
                structure_balance = min(max_depth, fan_out) / max(max_depth, fan_out)
            else:
                structure_balance = 0

            # 包名语义分析（不使用硬编码词库）
            parts = package_name.split('.')

            # 检查是否有技术性命名模式（长度适中、非数字、非单字符）
            has_tech_naming = (
                len(parts) >= 3 and
                all(2 <= len(part) <= 15 for part in parts[2:]) and  # 技术组件名长度合理
                not any(part.isdigit() for part in parts[2:]) and    # 非纯数字
                sum(len(part) for part in parts[2:]) >= 6            # 总长度表明有意义
            )

            # 优化的功能性SDK识别：降低门槛，提高覆盖率
            return (
                # 条件1: 高功能密度 + 适中深度（降低门槛）
                (functional_density >= 1.5 and 3 <= depth_score <= 6 and descendants >= 6) or

                # 条件2: 高垂直复杂性（降低门槛）
                (vertical_complexity >= 60 and depth_score >= 3 and fan_out >= 3) or

                # 条件3: 平衡的水平垂直复杂性 + 技术命名（降低门槛）
                (horizontal_complexity >= 30 and structure_balance >= 0.2 and
                 has_tech_naming and descendants >= 8) or

                # 条件4: 深层技术SDK（降低门槛）
                (depth_score >= 4 and descendants >= 12 and fan_out >= 4 and has_tech_naming) or

                # 条件5: 中等深度功能密度SDK（大幅降低门槛，专门捕获Firebase类型）
                (depth_score == 3 and functional_density >= 2.0 and descendants >= 6 and
                 fan_out >= 4 and has_tech_naming) or

                # 条件6: 高水平复杂性的中等深度SDK（降低门槛）
                (horizontal_complexity >= 80 and depth_score >= 3 and has_tech_naming) or

                # 条件7: 虚拟根包特殊处理（新增）
                (descendants >= 8 and fan_out >= 5 and depth_score >= 3 and has_tech_naming) or

                # 条件8: 小规模但高质量SDK（新增，专门捕获小型重要SDK）
                (functional_density >= 3.0 and descendants >= 5 and fan_out >= 3 and has_tech_naming)
            )

        # 识别功能性SDK
        functional_sdks = [c for c in candidates if is_functional_sdk(c)]
        other_candidates = [c for c in candidates if not is_functional_sdk(c)]

        # 对功能性SDK进行保护性剪枝
        protected_sdks = []
        for sdk in functional_sdks:
            sdk_package = sdk['package_name']

            # 检查是否有更好的祖先
            has_better_ancestor = False
            for other_sdk in functional_sdks:
                other_package = other_sdk['package_name']
                if (sdk_package.startswith(other_package + '.') and
                    other_sdk['score'] > sdk['score'] * 1.2):  # 祖先得分显著更高
                    has_better_ancestor = True
                    break

            if not has_better_ancestor:
                protected_sdks.append(sdk)

        # 对其他候选进行常规剪枝
        final_others = []
        for candidate in other_candidates:
            package_name = candidate['package_name']

            # 智能覆盖检测：避免功能性SDK被根包错误覆盖
            covered_by_sdk = False
            for sdk in protected_sdks:
                sdk_package = sdk['package_name']

                # 检查是否被覆盖
                if package_name.startswith(sdk_package + '.'):
                    # 计算层级差距
                    candidate_depth = len(package_name.split('.'))
                    sdk_depth = len(sdk_package.split('.'))
                    depth_diff = candidate_depth - sdk_depth

                    # 只有当层级差距足够大时才认为是真正的覆盖
                    # 这样可以避免 com.google.firebase 被 com.google 覆盖
                    if depth_diff >= 2:
                        covered_by_sdk = True
                        break

            if not covered_by_sdk:
                # 检查是否有更好的祖先
                has_better_ancestor = False
                for other in other_candidates:
                    other_package = other['package_name']
                    if (package_name.startswith(other_package + '.') and
                        other['score'] > candidate['score']):
                        has_better_ancestor = True
                        break

                if not has_better_ancestor:
                    final_others.append(candidate)

        return protected_sdks + final_others


class AppDataExtractor:
    """应用数据提取器，从APK分析结果中提取各种类型的数据"""

    # 定义9种数据类型
    TYPE_NATIVE_LIBRARIES = "0"  # Native Libraries (.so文件)
    TYPE_SERVICE = "1"           # Service 组件
    TYPE_ACTIVITY = "2"          # Activity 组件
    TYPE_RECEIVER = "3"          # Receiver 组件
    TYPE_PROVIDER = "4"          # Provider 组件
    TYPE_DEX_PACKAGES = "5"      # DEX Packages (包名) - SDK识别的包就是DEX包名
    TYPE_STATIC_LIBRARIES = "6"  # Static Libraries
    TYPE_PERMISSIONS = "7"       # Permissions
    TYPE_METADATA = "8"          # Metadata
    TYPE_INTENT_ACTIONS = "9"    # Intent Actions

    def __init__(self, blacklist_filter: Optional[BlackListFilter] = None):
        self.blacklist_filter = blacklist_filter

    def extract_all_data(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """从AppVersion中提取所有类型的数据"""
        all_data = []

        # 1. 提取Native Libraries (类型0)
        native_libs = self._extract_native_libraries(app_version)
        all_data.extend(native_libs)

        # 2. 提取Android组件 (类型1-4)
        components = self._extract_components(app_version)
        all_data.extend(components)

        # 3. 提取权限 (类型7)
        permissions = self._extract_permissions(app_version)
        all_data.extend(permissions)

        # 4. 提取Intent Actions (类型9)
        intent_actions = self._extract_intent_actions(app_version)
        all_data.extend(intent_actions)

        # 5. 提取元数据 (类型8)
        metadata = self._extract_metadata(app_version)
        all_data.extend(metadata)

        # 6. 提取DEX包名 (类型5)
        dex_packages = self._extract_dex_packages(app_version)
        all_data.extend(dex_packages)

        return all_data

    def _extract_dex_packages(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取DEX包名"""
        packages_list = []
        try:
            packages_class = getattr(app_version, 'packages_class', None)
            if not packages_class:
                return packages_list

            # 解析JSON格式的packages_class字段
            packages_data = json.loads(packages_class)
            
            # 提取包名列表（只提取键，即包名）
            packages = list(packages_data.keys()) if isinstance(packages_data, dict) else []
            
            # 统一包名格式（将斜杠替换为点号）
            formatted_packages = [pkg.replace('/', '.') for pkg in packages]
            
            # 应用黑名单过滤
            if self.blacklist_filter:
                filtered_packages = []
                blacklisted_count = 0
                for package in formatted_packages:
                    if not self.blacklist_filter.is_blacklisted(package):
                        filtered_packages.append(package)
                    else:
                        blacklisted_count += 1
                        logger.debug(f"DEX包名过滤掉黑名单包: {package}")
                
                logger.info(f"从DEX包名中过滤掉 {blacklisted_count} 个黑名单包名")
                formatted_packages = filtered_packages
            
            # 转换为标准格式
            for package in formatted_packages:
                packages_list.append({
                    'app_version_id': app_version.id,
                    'package_name': package,
                    'type': self.TYPE_DEX_PACKAGES
                })
            
        except json.JSONDecodeError:
            logger.warning(f"记录 {app_version.id} 的 packages_class JSON解析失败")
        except Exception as e:
            logger.error(f"提取DEX包名时出错: {e}")
            
        return packages_list

    def _extract_native_libraries(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取Native Libraries (类型0)"""
        libraries = []
        try:
            lib_files = getattr(app_version, 'lib_files', None)
            if lib_files:
                if isinstance(lib_files, str):
                    lib_files = json.loads(lib_files)
                if isinstance(lib_files, list):
                    for lib_name in lib_files:
                        libraries.append({
                            'app_version_id': app_version.id,
                            'package_name': lib_name,
                            'type': self.TYPE_NATIVE_LIBRARIES
                        })
        except Exception as e:
            logger.warning(f"提取Native Libraries失败 (app_version_id={app_version.id}): {e}")
        return libraries

    def _extract_components(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取Android组件 (类型1-4)"""
        components = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return components

            # 解析AndroidManifest.xml
            root = ET.fromstring(android_manifest)

            # 查找application节点
            app_node = root.find('application')
            if app_node is None:
                return components

            # 提取各种组件
            component_types = [
                ('service', self.TYPE_SERVICE),
                ('activity', self.TYPE_ACTIVITY),
                ('receiver', self.TYPE_RECEIVER),
                ('provider', self.TYPE_PROVIDER)
            ]

            for component_tag, component_type in component_types:
                for component in app_node.findall(component_tag):
                    name = component.get('{http://schemas.android.com/apk/res/android}name')
                    if name:
                        components.append({
                            'app_version_id': app_version.id,
                            'package_name': name,
                            'type': component_type
                        })

        except Exception as e:
            logger.warning(f"提取Android组件失败 (app_version_id={app_version.id}): {e}")
        return components

    def _extract_permissions(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取权限 (类型7)"""
        permissions = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return permissions

            root = ET.fromstring(android_manifest)

            # 提取uses-permission
            for perm in root.findall('uses-permission'):
                name = perm.get('{http://schemas.android.com/apk/res/android}name')
                if name:
                    permissions.append({
                        'app_version_id': app_version.id,
                        'package_name': name,
                        'type': self.TYPE_PERMISSIONS
                    })

        except Exception as e:
            logger.warning(f"提取权限失败 (app_version_id={app_version.id}): {e}")
        return permissions

    def _extract_intent_actions(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取Intent Actions (类型9)"""
        actions = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return actions

            root = ET.fromstring(android_manifest)

            # 查找所有intent-filter中的action
            for intent_filter in root.findall('.//intent-filter'):
                for action in intent_filter.findall('action'):
                    name = action.get('{http://schemas.android.com/apk/res/android}name')
                    if name:
                        actions.append({
                            'app_version_id': app_version.id,
                            'package_name': name,
                            'type': self.TYPE_INTENT_ACTIONS
                        })

        except Exception as e:
            logger.warning(f"提取Intent Actions失败 (app_version_id={app_version.id}): {e}")
        return actions

    def _extract_metadata(self, app_version: AppVersion) -> List[Dict[str, Any]]:
        """提取元数据 (类型8)"""
        metadata = []
        try:
            android_manifest = getattr(app_version, 'android_manifest', None)
            if not android_manifest:
                return metadata

            root = ET.fromstring(android_manifest)

            # 查找所有meta-data
            for meta in root.findall('.//meta-data'):
                name = meta.get('{http://schemas.android.com/apk/res/android}name')
                if name:
                    metadata.append({
                        'app_version_id': app_version.id,
                        'package_name': name,
                        'type': self.TYPE_METADATA
                    })

        except Exception as e:
            logger.warning(f"提取元数据失败 (app_version_id={app_version.id}): {e}")
        return metadata


def process_database_records(limit: int = 100) -> bool:
    """从数据库读取记录（默认100条packages_class不为空的记录），分析后写入class_app_version_sdks和class_app_discovered_packages表"""
    try:
        # 初始化数据库连接
        settings = Settings()
        engine = create_engine(settings.DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        # 初始化黑名单过滤器
        script_dir = os.path.dirname(os.path.abspath(__file__))
        blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
        
        # 初始化SDK识别器和数据提取器
        sdk_identifier = SDKIdentifier(blacklist_filter)
        data_extractor = AppDataExtractor(blacklist_filter)

        with SessionLocal() as session:
            # 从数据库读取记录
            logger.info(f"从数据库读取最多 {limit} 条记录（packages_class 不为空）...")
            app_versions = (
                session.query(AppVersion)
                .filter(
                    AppVersion.analyze_status.in_(['done', 'completed']),
                    AppVersion.packages_class.isnot(None),
                    or_(
                        AppVersion.analysis_result.is_(None),
                        AppVersion.analysis_result['sdk_identified'].astext != 'true'
                    )
                )
                .limit(limit)
                .all()
            )

            logger.info(f"成功读取 {len(app_versions)} 条记录")

            def _extract_packages(av: AppVersion) -> List[str]:
                """从AppVersion记录中提取去重排序后的包名列表"""
                try:
                    packages_class_value = getattr(av, 'packages_class', None)
                    if not packages_class_value:
                        return []
                    # 可能是JSON字符串或列表
                    packages = (
                        json.loads(packages_class_value)
                        if isinstance(packages_class_value, str)
                        else packages_class_value
                    )
                    if not isinstance(packages, list):
                        return []
                    return sorted(list(set(packages)))
                except Exception as e:
                    logger.warning(f"app_version_id={av.id} 提取包名失败: {e}")
                    return []

            # 处理每条记录
            for i, app_version in enumerate(app_versions):
                logger.info(f"处理记录 {i+1}/{len(app_versions)} (ID: {app_version.id})")

                # 提取包名列表
                packages = _extract_packages(app_version)
                if not packages:
                    logger.warning(f"记录 {app_version.id} 的 packages_class 为空或格式不正确，跳过")
                    continue

                try:
                    # 在进行SDK识别前，先对原始包名应用黑名单过滤
                    filtered_packages = []
                    blacklisted_count = 0
                    for package in packages:
                        if not blacklist_filter.is_blacklisted(package):
                            filtered_packages.append(package)
                        else:
                            blacklisted_count += 1
                            logger.debug(f"过滤掉黑名单包名: {package}")

                    logger.info(f"从原始包名中过滤掉 {blacklisted_count} 个黑名单包名")
                    logger.debug(f"过滤前包名数量: {len(packages)}, 过滤后包名数量: {len(filtered_packages)}")

                    # 识别SDK（基于packages_class）
                    identified_sdks = sdk_identifier.identify_sdks(filtered_packages)
                    logger.info(f"识别出 {len(identified_sdks)} 个SDK")
                    
                    # 特别检查识别出的SDK中是否包含android.support
                    support_sdks = [sdk for sdk in identified_sdks if 'android.support' in sdk['package_name']]
                    if support_sdks:
                        logger.info(f"识别出的SDK中包含android.support: {[sdk['package_name'] for sdk in support_sdks]}")

                    # 提取所有类型的数据（Native Libraries、组件、权限等，包括DEX包名）
                    extracted_data = data_extractor.extract_all_data(app_version)
                    
                    # 特别检查提取的数据中是否包含android.support
                    support_data = [data for data in extracted_data if 'android.support' in data['package_name']]
                    if support_data:
                        logger.info(f"提取的数据中包含android.support: {[data['package_name'] for data in support_data]}")

                    # 删除已存在的记录（确保幂等）
                    session.query(class_AppVersionSDK).filter(
                        class_AppVersionSDK.app_version_id == app_version.id
                    ).delete(synchronize_session=False)

                    session.query(class_AppDiscoveredPackage).filter(
                        class_AppDiscoveredPackage.app_version_id == app_version.id
                    ).delete(synchronize_session=False)

                    # 插入新的SDK记录（这些就是识别出的DEX包名）
                    now_ts = datetime.now()
                    for sdk in identified_sdks:
                        logger.debug(f"准备插入SDK记录: {sdk['package_name']}")
                        # 特别记录android.support包的插入
                        if 'android.support' in sdk['package_name']:
                            logger.info(f"准备插入android.support SDK记录: {sdk['package_name']}")
                            
                        sdk_record = class_AppVersionSDK(
                            app_version_id=app_version.id,
                            sdk_package_prefix=sdk['package_name'],
                            created_at=now_ts,
                            updated_at=now_ts,
                        )
                        session.add(sdk_record)

                        # 同时将识别出的SDK包名插入到class_app_discovered_packages表中，类型为DEX_PACKAGES
                        discovered_package = class_AppDiscoveredPackage(
                            app_version_id=app_version.id,
                            package_name=sdk['package_name'],
                            type=AppDataExtractor.TYPE_DEX_PACKAGES,
                            last_checked=now_ts
                        )
                        session.add(discovered_package)
                        
                        # 特别记录android.support包的插入
                        if 'android.support' in sdk['package_name']:
                            logger.info(f"已插入android.support SDK记录: {sdk['package_name']}")

                    # 插入其他类型的数据到class_app_discovered_packages表
                    for data_item in extracted_data:
                        logger.debug(f"准备插入数据记录: {data_item['package_name']} (类型: {data_item['type']})")
                        # 特别记录android.support包的插入
                        if 'android.support' in data_item['package_name']:
                            logger.info(f"准备插入android.support数据记录: {data_item['package_name']} (类型: {data_item['type']})")
                            
                        discovered_package = class_AppDiscoveredPackage(
                            app_version_id=data_item['app_version_id'],
                            package_name=data_item['package_name'],
                            type=data_item['type'],
                            last_checked=now_ts
                        )
                        session.add(discovered_package)
                        
                        # 特别记录android.support包的插入
                        if 'android.support' in data_item['package_name']:
                            logger.info(f"已插入android.support数据记录: {data_item['package_name']} (类型: {data_item['type']})")
                    # 在 app_version.analysis_result 里打标，避免重复处理
                    try:
                        existing_ar = app_version.analysis_result
                        if isinstance(existing_ar, str):
                            existing_ar = json.loads(existing_ar)
                    except Exception:
                        existing_ar = None

                    if not isinstance(existing_ar, dict):
                        existing_ar = {}

                    existing_ar['sdk_identified'] = True
                    existing_ar['sdk_count'] = len(identified_sdks)
                    existing_ar['discovered_packages_count'] = len(extracted_data) + len(identified_sdks)  # 包括SDK包名
                    existing_ar['sdk_updated_at'] = now_ts.isoformat()
                    session.query(AppVersion).filter(AppVersion.id == app_version.id).update(
                        {AppVersion.analysis_result: existing_ar}, synchronize_session=False
                    )

                    session.commit()
                    logger.info(
                        f"记录 {app_version.id} 处理完成，识别出 {len(identified_sdks)} 个 SDK，"
                        f"提取出 {len(extracted_data)} 个其他类型发现包，已写入数据库，并标记 sdk_identified=true"
                    )
                except Exception as e:
                    logger.error(f"处理记录 {app_version.id} 时出错，已回滚: {e}")
                    session.rollback()
                    continue

        return True

    except Exception as e:
        logger.error(f"处理数据库记录过程中出现错误: {e}")
        return False


def process_txt_files(folder_path: str) -> bool:
    """处理文件夹中的txt文件"""
    try:
        # 查找所有txt文件
        txt_files = [f for f in os.listdir(folder_path) if f.endswith('.txt') and f.startswith('class_example')]

        if len(txt_files) != 3:
            logger.error(f"期望找到3个txt文件，实际找到{len(txt_files)}个")
            return False

        logger.info(f"找到{len(txt_files)}个txt文件: {txt_files}")

        # 初始化黑名单过滤器
        blacklist_filter = BlackListFilter(os.path.join(folder_path, 'class_black_list.config'))
        
        # 初始化SDK识别器
        sdk_identifier = SDKIdentifier(blacklist_filter)

        results = []

        # 处理每个文件
        for txt_file in sorted(txt_files):
            file_path = os.path.join(folder_path, txt_file)
            logger.info(f"处理文件: {txt_file}")

            try:
                # 读取包名列表
                with open(file_path, 'r', encoding='utf-8') as f:
                    packages = json.load(f)

                logger.info(f"从{txt_file}读取到{len(packages)}个包名")
                
                # 在进行SDK识别前，先对原始包名应用黑名单过滤
                filtered_packages = []
                blacklisted_count = 0
                for package in packages:
                    if not blacklist_filter.is_blacklisted(package):
                        filtered_packages.append(package)
                    else:
                        blacklisted_count += 1
                        logger.debug(f"过滤掉黑名单包名: {package}")

                logger.info(f"从原始包名中过滤掉 {blacklisted_count} 个黑名单包名")
                logger.debug(f"过滤前包名数量: {len(packages)}, 过滤后包名数量: {len(filtered_packages)}")

                # 识别SDK
                identified_sdks = sdk_identifier.identify_sdks(filtered_packages)
                logger.info(f"识别出 {len(identified_sdks)} 个SDK")
                
                # 特别检查识别出的SDK中是否包含android.support
                support_sdks = [sdk for sdk in identified_sdks if 'android.support' in sdk['package_name']]
                if support_sdks:
                    logger.info(f"识别出的SDK中包含android.support: {[sdk['package_name'] for sdk in support_sdks]}")

                # 简单直接：按得分排序，显示所有识别出的SDK
                identified_sdks.sort(key=lambda x: x['score'], reverse=True)
                
                # 应用黑名单过滤到识别出的SDK包名上（额外保护）
                filtered_sdks = []
                blacklisted_count = 0
                for sdk in identified_sdks:
                    if not blacklist_filter.is_blacklisted(sdk['package_name']):
                        filtered_sdks.append(sdk)
                    else:
                        blacklisted_count += 1
                        logger.debug(f"过滤掉黑名单SDK: {sdk['package_name']}")
                        
                        # 特别记录android.support包被过滤
                        if 'android.support' in sdk['package_name']:
                            logger.info(f"过滤掉黑名单android.support SDK: {sdk['package_name']}")

                logger.info(f"从识别结果中过滤掉 {blacklisted_count} 个黑名单SDK")
                
                # 检查过滤后是否还有android.support
                support_filtered_sdks = [sdk for sdk in filtered_sdks if 'android.support' in sdk['package_name']]
                if support_filtered_sdks:
                    logger.info(f"过滤后仍包含android.support SDK: {[sdk['package_name'] for sdk in support_filtered_sdks]}")

                # 记录结果
                file_result = {
                    'file': txt_file,
                    'total_packages': len(packages),
                    'identified_sdks': len(filtered_sdks),
                    'sdks': [
                        sdk['package_name']  # 仅保留包名，移除其他字段
                        for sdk in filtered_sdks
                    ]
                }
                results.append(file_result)

                logger.info(f"{txt_file} 处理完成，识别出{len(filtered_sdks)}个SDK")

            except Exception as e:
                logger.error(f"处理文件{txt_file}时出错: {e}")
                return False

        # 写入结果文件
        result_file = os.path.join(folder_path, 'result.txt')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'status': 'success',
                'processed_files': len(txt_files),
                'timestamp': datetime.now().isoformat(),
                'results': results
            }, f, ensure_ascii=False, indent=2)

        logger.info(f"处理完成，结果已写入: {result_file}")
        return True

    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        return False


def main():
    """主函数"""
    folder_path = "/home/<USER>/dev/tenyy-dind/tenyy/src/apkinfo_analysis/apk_analysis_flow"

    logger.info("开始APK分析流程")
    
    # 优先处理数据库记录
    success_db = process_database_records()
    
    # 然后处理txt文件
    success_txt = process_txt_files(folder_path)

    if success_db and success_txt:
        logger.info("APK分析流程成功完成")
    else:
        logger.error("APK分析流程失败")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())