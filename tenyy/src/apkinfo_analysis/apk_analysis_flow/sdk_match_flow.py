# coding: utf-8
"""
SDK匹配流程
"""
import os
import sys
import json
import logging
import re
from typing import List, Dict, Tuple
from collections import defaultdict

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    logging.info(f"添加项目根目录到sys.path: {project_root}")

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, and_
from tenyy.src.config.settings import settings
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage
from tenyy.src.models.class_sdk_knowledge_base import class_SDKKnowledgeBase
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK

# 导入黑名单过滤器
from apk_analysis_flow import BlackListFilter

logger = logging.getLogger(__name__)


class SDKMatcher:
    """SDK匹配器"""

    def __init__(self):
        """初始化SDK匹配器"""
        # 创建数据库引擎
        self.engine = create_engine(settings.DATABASE_URL)
        logger.info(f"数据库引擎已创建: {self.engine}")

        # 初始化黑名单过滤器
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
        
    def get_database_session(self):
        """获取数据库会话"""
        Session = sessionmaker(bind=self.engine)
        return Session()
    
    def get_sample_apps(self, count: int = 10) -> List[int]:
        """获取示例应用版本ID列表"""
        session = self.get_database_session()
        try:
            # 获取最新的应用版本ID
            app_version_ids = session.query(class_AppDiscoveredPackage.app_version_id) \
                .distinct() \
                .order_by(class_AppDiscoveredPackage.app_version_id.desc()) \
                .limit(count) \
                .all()
            
            # 提取ID值
            ids = [row[0] for row in app_version_ids]
            logger.info(f"获取到 {len(ids)} 个示例应用版本ID")
            return ids
        finally:
            session.close()
    
    def get_discovered_packages(self, app_version_id: int) -> List[class_AppDiscoveredPackage]:
        """获取指定应用版本的所有包"""
        session = self.get_database_session()
        try:
            packages = session.query(class_AppDiscoveredPackage) \
                .filter(class_AppDiscoveredPackage.app_version_id == app_version_id) \
                .all()
            logger.info(f"应用版本 {app_version_id} 共有 {len(packages)} 个包")
            return packages
        finally:
            session.close()
    
    def get_sdk_knowledge_base(self) -> List[class_SDKKnowledgeBase]:
        """获取SDK知识库"""
        session = self.get_database_session()
        try:
            sdk_knowledge_base = session.query(class_SDKKnowledgeBase).all()
            logger.info(f"共获取到 {len(sdk_knowledge_base)} 条SDK知识库记录")
            return sdk_knowledge_base
        finally:
            session.close()
    
    def match_packages_with_sdk_knowledge(self, 
                                        packages: List[class_AppDiscoveredPackage], 
                                        sdk_knowledge_base: List[class_SDKKnowledgeBase]) -> Tuple[List[Dict], List[class_AppDiscoveredPackage]]:
        """将包与SDK知识库进行匹配"""
        matched_results = []
        unmatched_packages = []
        
        # 将SDK知识库转换为字典以提高查找效率
        sdk_dict = {str(sdk.package_prefix): sdk for sdk in sdk_knowledge_base}
        
        for package in packages:
            # 显式获取对象属性值
            package_name = getattr(package, 'package_name', '')
            package_name = str(package_name) if package_name is not None else ""
            
            # 跳过空包名
            if not package_name:
                continue
            
            matched = False
            # 按照包名长度降序排列，优先匹配更具体的包名
            sorted_prefixes = sorted(list(sdk_dict.keys()), key=lambda x: len(x), reverse=True)
            
            for prefix in sorted_prefixes:
                if package_name.startswith(prefix):
                    matched_results.append({
                        'app_version_id': package.app_version_id,
                        'sdk_package_prefix': prefix,
                        'sdk_knowledge_base_id': sdk_dict[prefix].id,
                        'match_type': 'prefix',
                        'type': getattr(package, 'type', '5')  # 默认为DEX包类型(5)
                    })
                    matched = True
                    break
            
            if not matched:
                unmatched_packages.append(package)
        
        logger.info(f"匹配到 {len(matched_results)} 个已知SDK包，{len(unmatched_packages)} 个未匹配包")
        return matched_results, unmatched_packages
    
    def filter_child_packages(self, 
                            matched_results: List[Dict], 
                            unmatched_packages: List[class_AppDiscoveredPackage]) -> List[class_AppDiscoveredPackage]:
        """过滤掉已匹配SDK的下级包"""
        # 构建已匹配的包名前缀集合
        matched_prefixes = {result['sdk_package_prefix'] for result in matched_results}
        
        filtered_packages = []
        for package in unmatched_packages:
            # 显式获取对象属性值
            package_name = getattr(package, 'package_name', '')
            package_name = str(package_name) if package_name is not None else ""
            
            # 跳过空包名
            if not package_name:
                continue
            
            # 检查是否为已匹配包的下级包
            should_filter = False
            for prefix in matched_prefixes:
                if package_name.startswith(prefix + '.'):
                    should_filter = True
                    break
            
            if not should_filter:
                filtered_packages.append(package)
                
        return filtered_packages
    
    def analyze_potential_sdks(self, unmatched_packages: List[class_AppDiscoveredPackage]) -> List[Dict]:
        """
        分析潜在的SDK特征
        
        Args:
            unmatched_packages: 未匹配的包列表
            
        Returns:
            潜在SDK特征列表
        """
        if not unmatched_packages:
            return []
        
        # 层级分解和频率统计
        level_count = defaultdict(int)  # 各层级出现次数
        level_children = defaultdict(set)  # 各层级的直接子级
        
        for package in unmatched_packages:
            # 显式获取对象属性值
            package_name = getattr(package, 'package_name', '')
            package_name = str(package_name) if package_name is not None else ""
            
            # 跳过空包名
            if not package_name:
                continue
            
            parts = package_name.split('.')
            # 生成所有可能的父级前缀
            for i in range(1, len(parts) + 1):
                prefix = '.'.join(parts[:i])
                level_count[prefix] += 1
                
                # 记录直接子级
                if i < len(parts):
                    child = parts[i]
                    level_children[prefix].add(child)
        
        # 提取候选特征（出现3次或以上）
        candidate_features = {level: count for level, count in level_count.items() if count >= 3}
        
        # 合并父级特征（同一父级下有3个或以上子级）
        potential_sdks = []
        for level, children in level_children.items():
            # 过滤掉1-2级包名（如com, com.huawei等）
            level_parts = level.split('.')
            if len(level_parts) <= 2:
                logger.debug(f"过滤掉1-2级包名: {level}")
                continue
            
            # 应用黑名单过滤，避免将黑名单中的包识别为潜在SDK
            if self.blacklist_filter.is_blacklisted(level):
                logger.debug(f"过滤掉黑名单潜在SDK: {level}")
                continue
                
            if len(children) >= 3 and level in candidate_features:
                # 记录该潜在SDK下的所有包
                sdk_packages = [p for p in unmatched_packages 
                              if (getattr(p, 'package_name', '') or "").startswith(level + '.')]
                
                # 获取类型信息，如果所有包类型相同则使用该类型，否则默认为DEX包类型(5)
                types = [getattr(p, 'type', '5') for p in sdk_packages]
                sdk_type = types[0] if len(set(types)) == 1 else '5'
                
                potential_sdks.append({
                    'sdk_package_prefix': level,
                    'signal_count': len(sdk_packages),
                    'child_packages': json.dumps(list(children), ensure_ascii=False),
                    'packages': sdk_packages,
                    'type': sdk_type
                })
                
        return potential_sdks
    
    def save_results(self, 
                    app_version_id: int,
                    matched_results: List[Dict],
                    potential_sdks: List[Dict]):
        """
        保存匹配结果到class_AppVersionSDK表
        
        Args:
            app_version_id: 应用版本ID
            matched_results: 匹配结果
            potential_sdks: 潜在SDK特征
        """
        session = self.get_database_session()
        try:
            # 删除该应用版本已有的记录
            deleted_count = session.query(class_AppVersionSDK) \
                .filter(class_AppVersionSDK.app_version_id == app_version_id) \
                .delete()
            logger.info(f"删除了应用版本 {app_version_id} 的 {deleted_count} 条旧记录")
            
            # 创建一个集合来跟踪已插入的(app_version_id, sdk_package_prefix)组合
            inserted_combinations = set()
            
            # 保存已匹配的SDK
            matched_count = 0
            for result in matched_results:
                combination = (result['app_version_id'], result['sdk_package_prefix'])
                # 检查是否已插入相同的组合
                if combination in inserted_combinations:
                    continue
                
                sdk_record = class_AppVersionSDK(
                    app_version_id=result['app_version_id'],
                    sdk_package_prefix=result['sdk_package_prefix'] if result['sdk_package_prefix'] is not None else "",
                    sdk_knowledge_base_id=result['sdk_knowledge_base_id'],
                    match_type=result['match_type'] if result['match_type'] is not None else "",
                    is_potential=False,
                    type=result.get('type', '5'),  # 添加type字段，默认为DEX包类型(5)
                    created_at=None,
                    updated_at=None
                )
                session.add(sdk_record)
                session.flush()  # 立即执行插入操作，确保ID被正确生成
                inserted_combinations.add(combination)
                matched_count += 1
            
            # 保存潜在的SDK
            potential_count = 0
            for potential in potential_sdks:
                combination = (app_version_id, potential['sdk_package_prefix'])
                # 检查是否已插入相同的组合
                if combination in inserted_combinations:
                    continue
                
                # 应用黑名单过滤，避免将黑名单中的包识别为潜在SDK
                if self.blacklist_filter.is_blacklisted(potential['sdk_package_prefix']):
                    logger.debug(f"过滤掉黑名单潜在SDK: {potential['sdk_package_prefix']}")
                    continue
                
                sdk_record = class_AppVersionSDK(
                    app_version_id=app_version_id,
                    sdk_package_prefix=potential['sdk_package_prefix'] if potential['sdk_package_prefix'] is not None else "",
                    sdk_knowledge_base_id=None,
                    match_type='potential',
                    is_potential=True,
                    signal_count=potential['signal_count'],
                    child_packages=potential['child_packages'] if potential['child_packages'] is not None else None,
                    type=potential.get('type', '5'),  # 添加type字段，默认为DEX包类型(5)
                    created_at=None,
                    updated_at=None
                )
                session.add(sdk_record)
                session.flush()  # 立即执行插入操作，确保ID被正确生成
                inserted_combinations.add(combination)
                potential_count += 1
            
            logger.info(f"准备保存应用版本 {app_version_id} 的匹配结果: {matched_count} 个已匹配SDK, {potential_count} 个潜在SDK")
            logger.info("开始执行session.commit()")
            session.commit()
            logger.info(f"应用版本 {app_version_id} 的匹配结果已保存")
        except Exception as e:
            logger.error(f"保存应用版本 {app_version_id} 的匹配结果时出错: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            session.rollback()
            raise  # 重新抛出异常以便上层捕获
        finally:
            session.close()
    
    def process_app_version(self, app_version_id: int):
        """
        处理单个应用版本的SDK匹配
        
        Args:
            app_version_id: 应用版本ID
        """
        logger.info(f"开始处理应用版本 {app_version_id}")
        
        # 获取该应用版本的所有包
        packages = self.get_discovered_packages(app_version_id)
        logger.info(f"应用版本 {app_version_id} 共有 {len(packages)} 个包")
        
        if not packages:
            logger.info(f"应用版本 {app_version_id} 没有包数据")
            return
        
        # 获取SDK知识库
        sdk_knowledge_base = self.get_sdk_knowledge_base()
        logger.info(f"共获取到 {len(sdk_knowledge_base)} 条SDK知识库记录")
        
        # 包与SDK知识库匹配
        matched_results, unmatched_packages = self.match_packages_with_sdk_knowledge(packages, sdk_knowledge_base)
        logger.info(f"匹配到 {len(matched_results)} 个已知SDK包，{len(unmatched_packages)} 个未匹配包")
        
        # 过滤下级包
        filtered_packages = self.filter_child_packages(matched_results, unmatched_packages)
        logger.info(f"过滤后剩余 {len(filtered_packages)} 个包用于潜在SDK分析")
        
        # 分析潜在SDK
        potential_sdks = self.analyze_potential_sdks(filtered_packages)
        logger.info(f"发现 {len(potential_sdks)} 个潜在SDK特征")
        
        # 保存结果
        self.save_results(app_version_id, matched_results, potential_sdks)
        
        # 输出统计信息
        logger.info(f"应用版本 {app_version_id} 处理完成:")
        logger.info(f"  - 已识别SDK包: {len(matched_results)} 个")
        logger.info(f"  - 潜在SDK特征: {len(potential_sdks)} 个")
        for potential in potential_sdks:
            logger.info(f"    * {potential['sdk_package_prefix']} (信号数: {potential['signal_count']})")
    
    def run(self, app_count: int = 10):
        """
        运行SDK匹配流程
        
        Args:
            app_count: 处理的应用数量，默认10个
        """
        logger.info("开始SDK匹配流程")
        
        # 获取示例应用
        app_version_ids = self.get_sample_apps(app_count)
        logger.info(f"获取到 {len(app_version_ids)} 个示例应用")
        
        # 处理每个应用版本
        for app_version_id in app_version_ids:
            try:
                self.process_app_version(app_version_id)
            except Exception as e:
                logger.error(f"处理应用版本 {app_version_id} 时出错: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
        
        logger.info("SDK匹配流程完成")

def main():
    """主函数"""
    matcher = SDKMatcher()
    matcher.run(10)

if __name__ == "__main__":
    main()