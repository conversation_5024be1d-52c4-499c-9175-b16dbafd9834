#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的数据库测试脚本 - 验证修复后的黑名单过滤效果
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入配置和模型
from sqlalchemy import create_engine, func, text
from sqlalchemy.orm import sessionmaker
from tenyy.src.config.settings import Settings
from tenyy.src.models.app_version import AppVersion
from tenyy.src.models.class_app_version_sdks import class_AppVersionSDK
from tenyy.src.models.class_app_discovered_packages import class_AppDiscoveredPackage

# 导入修复后的分析流程
from apk_analysis_flow import process_database_records, BlackListFilter

class DatabaseTestManager:
    """数据库测试管理器"""
    
    def __init__(self):
        self.settings = Settings()
        self.engine = create_engine(self.settings.DATABASE_URL)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 初始化黑名单过滤器
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
        
        logger.info(f"数据库连接: {self.settings.DATABASE_URL}")
        logger.info(f"黑名单模式数量: {len(self.blacklist_filter.patterns)}")
    
    def check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            with self.SessionLocal() as session:
                result = session.execute(text("SELECT 1"))
                logger.info("✅ 数据库连接成功")
                return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def get_test_records(self, limit: int = 5) -> List[AppVersion]:
        """获取测试记录"""
        try:
            with self.SessionLocal() as session:
                # 查找有packages_class数据且未处理过SDK的记录
                records = session.query(AppVersion).filter(
                    AppVersion.packages_class.isnot(None),
                    AppVersion.packages_class != '{}',
                    AppVersion.packages_class != '[]'
                ).limit(limit).all()
                
                logger.info(f"找到 {len(records)} 条测试记录")
                return records
        except Exception as e:
            logger.error(f"获取测试记录失败: {e}")
            return []
    
    def analyze_packages_before_fix(self, app_version: AppVersion) -> Dict[str, Any]:
        """分析修复前的包名情况"""
        try:
            packages_class = getattr(app_version, 'packages_class', None)
            if not packages_class:
                return {"error": "无packages_class数据"}
            
            # 解析包名数据
            if isinstance(packages_class, str):
                packages_data = json.loads(packages_class)
            else:
                packages_data = packages_class
            
            # 提取包名列表
            if isinstance(packages_data, dict):
                packages = list(packages_data.keys())
            elif isinstance(packages_data, list):
                packages = packages_data
            else:
                return {"error": "packages_class格式不支持"}
            
            # 统一包名格式（将斜杠替换为点号）
            formatted_packages = [pkg.replace('/', '.') for pkg in packages]
            
            # 分析黑名单过滤情况
            blacklisted_packages = []
            androidx_packages = []
            android_support_packages = []
            
            for package in formatted_packages:
                if self.blacklist_filter.is_blacklisted(package):
                    blacklisted_packages.append(package)
                    
                if package.startswith('androidx.'):
                    androidx_packages.append(package)
                    
                if package.startswith('android.support.'):
                    android_support_packages.append(package)
            
            return {
                "app_version_id": app_version.id,
                "total_packages": len(packages),
                "formatted_packages": len(formatted_packages),
                "blacklisted_packages": len(blacklisted_packages),
                "androidx_packages": len(androidx_packages),
                "android_support_packages": len(android_support_packages),
                "blacklisted_examples": blacklisted_packages[:10],
                "androidx_examples": androidx_packages[:5],
                "android_support_examples": android_support_packages[:5]
            }
            
        except Exception as e:
            logger.error(f"分析包名失败: {e}")
            return {"error": str(e)}
    
    def clear_existing_results(self, app_version_ids: List[int]):
        """清理现有的分析结果"""
        try:
            with self.SessionLocal() as session:
                # 删除现有的SDK记录
                session.query(class_AppVersionSDK).filter(
                    class_AppVersionSDK.app_version_id.in_(app_version_ids)
                ).delete(synchronize_session=False)
                
                # 删除现有的发现包记录
                session.query(class_AppDiscoveredPackage).filter(
                    class_AppDiscoveredPackage.app_version_id.in_(app_version_ids)
                ).delete(synchronize_session=False)
                
                # 重置analysis_result中的SDK相关标记
                for app_version_id in app_version_ids:
                    app_version = session.query(AppVersion).filter(
                        AppVersion.id == app_version_id
                    ).first()
                    
                    if app_version and app_version.analysis_result:
                        existing_ar = app_version.analysis_result
                        if isinstance(existing_ar, dict):
                            existing_ar['sdk_identified'] = False
                            existing_ar['sdk_count'] = 0
                            existing_ar['discovered_packages_count'] = 0
                            existing_ar.pop('sdk_updated_at', None)
                            
                            session.query(AppVersion).filter(AppVersion.id == app_version_id).update(
                                {AppVersion.analysis_result: existing_ar}, synchronize_session=False
                            )
                
                session.commit()
                logger.info(f"✅ 清理了 {len(app_version_ids)} 条记录的现有分析结果")
                
        except Exception as e:
            logger.error(f"清理现有结果失败: {e}")
            raise
    
    def check_results_after_fix(self, app_version_ids: List[int]) -> Dict[str, Any]:
        """检查修复后的结果"""
        try:
            with self.SessionLocal() as session:
                results = {}
                
                for app_version_id in app_version_ids:
                    # 检查SDK记录
                    sdk_count = session.query(func.count(class_AppVersionSDK.app_version_id)).filter(
                        class_AppVersionSDK.app_version_id == app_version_id
                    ).scalar()
                    
                    # 检查发现包记录
                    discovered_count = session.query(func.count(class_AppDiscoveredPackage.app_version_id)).filter(
                        class_AppDiscoveredPackage.app_version_id == app_version_id
                    ).scalar()
                    
                    # 获取SDK包名示例
                    sdk_examples = session.query(class_AppVersionSDK.sdk_package_prefix).filter(
                        class_AppVersionSDK.app_version_id == app_version_id
                    ).limit(10).all()
                    
                    # 检查是否有androidx或android.support的SDK被错误插入
                    blacklisted_sdks = session.query(class_AppVersionSDK.sdk_package_prefix).filter(
                        class_AppVersionSDK.app_version_id == app_version_id,
                        class_AppVersionSDK.sdk_package_prefix.like('androidx.%')
                    ).all()
                    
                    blacklisted_sdks.extend(session.query(class_AppVersionSDK.sdk_package_prefix).filter(
                        class_AppVersionSDK.app_version_id == app_version_id,
                        class_AppVersionSDK.sdk_package_prefix.like('android.support.%')
                    ).all())
                    
                    # 检查analysis_result更新
                    app_version = session.query(AppVersion).filter(AppVersion.id == app_version_id).first()
                    analysis_result = app_version.analysis_result if app_version else {}
                    
                    results[app_version_id] = {
                        "sdk_count": sdk_count,
                        "discovered_count": discovered_count,
                        "sdk_examples": [sdk[0] for sdk in sdk_examples],
                        "blacklisted_sdks_found": [sdk[0] for sdk in blacklisted_sdks],
                        "sdk_identified": analysis_result.get('sdk_identified', False) if isinstance(analysis_result, dict) else False,
                        "analysis_sdk_count": analysis_result.get('sdk_count', 0) if isinstance(analysis_result, dict) else 0
                    }
                
                return results
                
        except Exception as e:
            logger.error(f"检查结果失败: {e}")
            return {}

def run_complete_test():
    """运行完整测试"""
    
    print("=" * 80)
    print("🧪 开始完整的数据库测试 - 验证黑名单过滤修复效果")
    print("=" * 80)
    
    # 初始化测试管理器
    test_manager = DatabaseTestManager()
    
    # 1. 检查数据库连接
    print("\n📡 步骤1: 检查数据库连接")
    if not test_manager.check_database_connection():
        print("❌ 数据库连接失败，测试终止")
        return False
    
    # 2. 获取测试记录
    print("\n📋 步骤2: 获取测试记录")
    test_records = test_manager.get_test_records(limit=3)
    if not test_records:
        print("❌ 没有找到合适的测试记录")
        return False
    
    print(f"✅ 获取到 {len(test_records)} 条测试记录")
    
    # 3. 分析修复前的情况
    print("\n🔍 步骤3: 分析修复前的包名情况")
    before_analysis = {}
    for record in test_records:
        analysis = test_manager.analyze_packages_before_fix(record)
        before_analysis[record.id] = analysis
        
        if "error" not in analysis:
            print(f"  记录 {record.id}:")
            print(f"    总包名: {analysis['total_packages']}")
            print(f"    黑名单过滤: {analysis['blacklisted_packages']} 个")
            print(f"    androidx包: {analysis['androidx_packages']} 个")
            print(f"    android.support包: {analysis['android_support_packages']} 个")
            if analysis['androidx_examples']:
                print(f"    androidx示例: {analysis['androidx_examples'][:3]}")
    
    # 4. 清理现有结果
    print("\n🧹 步骤4: 清理现有分析结果")
    app_version_ids = [record.id for record in test_records]
    test_manager.clear_existing_results(app_version_ids)
    
    # 5. 运行修复后的分析流程
    print("\n🚀 步骤5: 运行修复后的分析流程")
    print("正在执行 process_database_records...")
    
    success = process_database_records(limit=len(test_records))
    
    if not success:
        print("❌ 分析流程执行失败")
        return False
    
    print("✅ 分析流程执行成功")
    
    # 6. 检查修复后的结果
    print("\n📊 步骤6: 检查修复后的结果")
    after_results = test_manager.check_results_after_fix(app_version_ids)
    
    # 7. 对比和验证
    print("\n🎯 步骤7: 结果验证")
    success_count = 0
    
    for app_version_id in app_version_ids:
        before = before_analysis.get(app_version_id, {})
        after = after_results.get(app_version_id, {})
        
        print(f"\n  记录 {app_version_id} 验证结果:")
        print(f"    修复前 - 总包名: {before.get('total_packages', 0)}, 黑名单过滤: {before.get('blacklisted_packages', 0)}")
        print(f"    修复后 - SDK数量: {after.get('sdk_count', 0)}, 发现包数量: {after.get('discovered_count', 0)}")
        
        # 关键验证：检查是否有黑名单包名被错误插入
        blacklisted_found = after.get('blacklisted_sdks_found', [])
        if blacklisted_found:
            print(f"    ❌ 发现黑名单包名被错误插入: {blacklisted_found}")
        else:
            print(f"    ✅ 没有黑名单包名被错误插入")
            success_count += 1
        
        # 显示识别出的SDK示例
        sdk_examples = after.get('sdk_examples', [])[:5]
        if sdk_examples:
            print(f"    识别的SDK示例: {sdk_examples}")
    
    # 8. 总结
    print(f"\n{'='*80}")
    print("📋 测试总结")
    print(f"{'='*80}")
    print(f"测试记录数: {len(test_records)}")
    print(f"成功验证数: {success_count}")
    print(f"成功率: {success_count/len(test_records)*100:.1f}%")
    
    if success_count == len(test_records):
        print("🎉 所有测试通过！黑名单过滤修复成功！")
        print("✅ androidx.core 等黑名单包名不再被错误插入数据库")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = run_complete_test()
        exit(0 if success else 1)
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        exit(1)
