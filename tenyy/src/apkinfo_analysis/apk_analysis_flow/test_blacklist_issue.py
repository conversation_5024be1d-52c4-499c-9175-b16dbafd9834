#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试黑名单过滤问题
"""

import os
import json
import re
import logging
from typing import List

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BlackListFilter:
    """黑名单过滤器，用于过滤系统和标准库包名"""

    def __init__(self, config_file: str):
        self.config_file = config_file
        self.patterns = self._load_blacklist_patterns()

    def _load_blacklist_patterns(self) -> List[re.Pattern]:
        """加载黑名单正则表达式模式"""
        patterns = []
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if line and not line.startswith('#'):
                        # 直接使用配置文件中的正则表达式模式
                        # 确保模式匹配整个包名前缀
                        pattern = re.compile(line)
                        patterns.append(pattern)
                        logger.debug(f"加载黑名单模式: {line}")
        except Exception as e:
            logger.error(f"加载黑名单配置文件失败: {e}")
        logger.info(f"总共加载了 {len(patterns)} 个黑名单模式")
        return patterns

    def is_blacklisted(self, package_name: str) -> bool:
        """检查包名是否在黑名单中"""
        logger.debug(f"检查包名是否在黑名单中: {package_name}")
        for pattern in self.patterns:
            # 使用match确保从包名开始匹配
            if pattern.match(package_name):
                logger.debug(f"包 {package_name} 被黑名单 {pattern.pattern} 匹配")
                return True
        logger.debug(f"包 {package_name} 未被任何黑名单模式匹配")
        return False

def test_blacklist_filter():
    """测试黑名单过滤器"""
    
    # 初始化黑名单过滤器
    script_dir = os.path.dirname(os.path.abspath(__file__))
    blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
    
    # 测试用例
    test_cases = [
        # 斜杠格式（原始格式）
        "androidx/core",
        "androidx/core/app",
        "android/support/v4/app",
        "com/google/gson/Gson",
        
        # 点号格式（转换后格式）
        "androidx.core",
        "androidx.core.app", 
        "android.support.v4.app",
        "com.google.gson.Gson",
        
        # 非黑名单包名
        "com.example.myapp",
        "cn.tencent.mm",
        "com.alibaba.fastjson"
    ]
    
    print("=== 黑名单过滤测试 ===")
    print(f"加载了 {len(blacklist_filter.patterns)} 个黑名单模式")
    
    for pattern in blacklist_filter.patterns:
        print(f"  - {pattern.pattern}")
    
    print("\n=== 测试结果 ===")
    for package in test_cases:
        is_blacklisted = blacklist_filter.is_blacklisted(package)
        status = "❌ 被过滤" if is_blacklisted else "✅ 通过"
        print(f"{status}: {package}")
    
    print("\n=== 问题分析 ===")
    # 测试斜杠格式的androidx包名
    slash_format = "androidx/core"
    dot_format = "androidx.core"
    
    slash_result = blacklist_filter.is_blacklisted(slash_format)
    dot_result = blacklist_filter.is_blacklisted(dot_format)
    
    print(f"斜杠格式 '{slash_format}': {'被过滤' if slash_result else '未被过滤'}")
    print(f"点号格式 '{dot_format}': {'被过滤' if dot_result else '未被过滤'}")
    
    if not slash_result and dot_result:
        print("⚠️  问题确认：斜杠格式的包名没有被正确过滤！")
        return False
    else:
        print("✅ 黑名单过滤工作正常")
        return True

def test_with_real_data():
    """使用真实测试数据进行测试"""
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    blacklist_filter = BlackListFilter(os.path.join(script_dir, 'class_black_list.config'))
    
    # 读取测试数据
    test_file = os.path.join(script_dir, 'class_example2.txt')
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return False
    
    with open(test_file, 'r', encoding='utf-8') as f:
        packages = json.load(f)
    
    print(f"\n=== 真实数据测试 ===")
    print(f"总包名数量: {len(packages)}")
    
    # 统计androidx相关包名
    androidx_packages = [pkg for pkg in packages if 'androidx' in pkg]
    print(f"包含androidx的包名数量: {len(androidx_packages)}")
    
    # 测试原始格式（斜杠）
    print("\n--- 原始格式测试（斜杠） ---")
    blacklisted_count = 0
    for package in androidx_packages[:10]:  # 只测试前10个
        is_blacklisted = blacklist_filter.is_blacklisted(package)
        status = "❌ 被过滤" if is_blacklisted else "✅ 通过"
        print(f"{status}: {package}")
        if is_blacklisted:
            blacklisted_count += 1
    
    print(f"前10个androidx包名中被过滤的数量: {blacklisted_count}")
    
    # 测试转换格式（点号）
    print("\n--- 转换格式测试（点号） ---")
    converted_packages = [pkg.replace('/', '.') for pkg in androidx_packages[:10]]
    blacklisted_count = 0
    for package in converted_packages:
        is_blacklisted = blacklist_filter.is_blacklisted(package)
        status = "❌ 被过滤" if is_blacklisted else "✅ 通过"
        print(f"{status}: {package}")
        if is_blacklisted:
            blacklisted_count += 1
    
    print(f"前10个转换后的androidx包名中被过滤的数量: {blacklisted_count}")
    
    return True

if __name__ == "__main__":
    print("开始测试黑名单过滤问题...")
    
    # 基础测试
    basic_test_passed = test_blacklist_filter()
    
    # 真实数据测试
    real_data_test_passed = test_with_real_data()
    
    print(f"\n=== 测试总结 ===")
    print(f"基础测试: {'通过' if basic_test_passed else '失败'}")
    print(f"真实数据测试: {'通过' if real_data_test_passed else '失败'}")
