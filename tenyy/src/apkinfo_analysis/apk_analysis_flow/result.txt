{"status": "success", "processed_files": 3, "timestamp": "2025-08-13T16:11:20.233343", "results": [{"file": "class_example.txt", "total_packages": 9146, "identified_sdks": 44, "sdks": ["com.tencent.thumbplayer", "com.google.android", "com.tencent.mapsdk", "com.tencent.liteav", "com.tencent.matrix", "com.tencent.tencentmap", "com.tencent.midas", "com.tencent.luggage", "com.tencent.maas", "com.tencent.tencentmap.mapsdk", "com.tencent.qqmusic", "com.tencent.qqmusic.mediaplayer", "com.tencent.mapsdk.core", "com.tencent.tmassistantsdk", "com.tencent.mars", "com.tencent.wechat", "com.tencent.youtu", "com.tencent.plugin", "com.tencent.xweb", "io.flutter.embedding.engine", "com.tencent.kinda", "com.tencent.pigeon", "com.google.firebase", "com.tencent.kinda.framework", "io.flutter.plugins", "com.tencent.tinker", "io.flutter.embedding", "com.tencent.tavkit", "org.chromium.base", "com.tencent.magicbrush", "com.tencent.thumbplayer.tplayer", "io.flutter.plugin", "com.tencent.youtu.sdkkitframework", "com.tencent.live", "com.tencent.tinker.loader", "com.tencent.luggage.game", "com.tencent.matrix.resource", "com.tencent.thumbplayer.adapter", "com.tencent.midas.comm", "com.hihonor.easygo", "com.huawei.easygo", "com.tencent.wevision2", "com.tencent.matrix.batterycanary", "com.davemorrissey.labs"]}, {"file": "class_example2.txt", "total_packages": 8496, "identified_sdks": 126, "sdks": ["com.tencent.qimei", "com.tencent.ams.fusion.widget", "com.tencent.qqlive.module.videoreport", "com.tencent.common", "com.xueersi.common", "com.tencent.qqlive.tvkplayer", "com.tencent.kuikly.core", "com.tencent.trpcprotocol", "com.tencent.thumbplayer", "com.tencent.rmonitor", "com.tencent.qqlive", "com.xueersi.meta.modules.plugin", "com.tencent.beacon", "com.tencent.thumbplayer.core", "cn.hutool.core", "com.google.android.material", "com.tencent.ams.fusion.service", "com.tencent.qqlive.modules", "com.tencent.qqlive.module", "com.tencent.ams.mosaic.jsengine.component", "com.xueersi.meta.modules", "com.xueersi.meta", "com.tencent.ams.mosaic.jsengine", "com.tencent.mobileqq", "com.tdsrightly.qmethod", "com.tdsrightly.qmethod.monitor", "com.tencent.kuikly", "com.tencent.tinker", "com.tencent.qqmini", "com.xueersi.parentsmeeting", "com.facebook.imagepipeline", "com.tencent.opentelemetry", "com.tencent.mobileqq.triton", "com.tencent.midas", "com.meizu.cloud", "com.tencent.bugly", "com.facebook.drawee", "com.xueersi.meta.base", "com.xueersi.meta.base.live.framework", "com.tencent.mttreader", "com.meizu.cloud.pushsdk", "com.tencent.gathererga", "com.huawei.secure", "com.tencent.bugly.common", "com.tencent.rdelivery.reshub", "com.bumptech.glide", "tencent.doc.opensdk", "com.tencent.rdelivery", "com.tencent.superplayer", "com.tencent.beacon.base", "com.tencent.cloud", "com.tencent.raft", "tencent.doc.opensdk.openapi", "com.xueersi.common.business", "okhttp3.internal", "com.tencent.rfix.loader", "com.tencent.rfix", "com.facebook.common", "com.tencent.startrail.report", "com.google.android", "com.tencent.paysdk", "com.tencent.galileo", "com.tencent.startrail", "com.tencent.portraitsdk", "com.tencent.upgrade", "com.iflytek.cloud", "com.vivo.push", "com.tencent.tuxmetersdk", "com.tencent.raft.raftframework", "com.tencent.matrix", "com.tencent.ad.tangram.views.canvas.components", "com.facebook.fresco", "com.tencent.apkchannel", "com.tencent.open", "org.tensorflow.lite", "com.tencent.qbmnnbusiness", "org.xutils.xutils", "com.huawei.secure.android.common", "com.airbnb.lottie", "io.flutter.embedding.engine", "com.tencent.startrail.report.vendor", "com.tencent.qcloud.core", "com.tencent.odk.player.client", "com.hihonor.push", "com.xes.meta.modules.metaunity", "com.tencent.ad.tangram.views", "com.huawei.agconnect", "com.tencent.connect", "io.flutter.embedding", "org.tensorflow.lite.support", "com.tencent.tddiag", "com.heytap.mcssdk", "com.jayway.jsonpath", "com.facebook.fresco.animation", "com.tencent.qqvideo.edgeengine", "com.tencent.ams.fusion.tbox", "com.google.archivepatcher", "com.tencent.shadow", "com.tencent.qcloud", "com.tencent.ams.music.widget", "tmsdk.common.gourd", "com.tencent.trouter", "com.tencent.gathererga.core", "tmsdk.common", "com.jayway.jsonpath.internal", "com.tencent.thumbplayer.tplayer", "com.tencent.tmediacodec", "com.tencent.ams.fusion.widget.apng", "com.tencent.memorycanary", "com.xueersi.component.cloud", "com.tencent.qbmnn", "com.tencent.tinker.commons", "com.tencent.mttreader.epub", "com.tencent.lu.extension.phone", "com.huya.huyasdk", "com.tencent.rmpbusiness", "com.tencent.ad.tangram.views.canvas", "com.tencent.tinker.loader", "com.tencent.ams.xsad.rewarded", "com.tencent.qqvideo", "com.tencent.mobileqq.openpay", "com.tencent.apkchannel.channel", "tencent.doc.opensdk.bean", "com.tencent.trpcprotocol.tsearchRecommend", "com.huawei.agconnect.core", "com.tencent.shadow.core"]}, {"file": "class_example3.txt", "total_packages": 1882, "identified_sdks": 60, "sdks": ["com.beizi.fusion", "com.ibplus.client", "com.google.firebase", "com.google.android.material", "com.google.android", "kt.pieceui.activity", "com.huawei.updatesdk", "com.google.android.exoplayer2", "com.google.zxing", "com.taobao.aranger", "com.meizu.cloud", "anet.channel", "cn.jpush.android", "com.huawei.hianalytics", "com.facebook.drawee", "kt.pieceui.fragment", "com.huawei.appmarket", "com.umeng.socialize", "com.huawei.appmarket.component.buoycircle", "com.meizu.cloud.pushsdk", "com.bumptech.glide", "com.taobao.accs", "com.huawei.android", "com.alipay.android", "com.huawei.appmarket.component.buoycircle.impl", "okhttp3.internal", "com.umeng.message", "com.umeng.commonsdk", "com.alibaba.fastjson", "com.example.libimagefilter", "com.tencent.open", "com.youzan.androidsdk", "com.youzan.spiderman", "com.liulishuo.filedownloader", "com.iflytek.cloud", "com.luck.picture", "com.facebook.imagepipeline", "com.vivo.push", "com.alibaba.fastjson.support", "com.google.firebase.crashlytics", "org.jetbrains.anko", "org.kymjs.kjframe", "cn.sharesdk.framework", "anetwork.channel", "com.scwang.smartrefresh.layout", "com.youzan.jsbridge", "org.parceler.guava", "com.chad.library.adapter.base", "com.lljjcoder.citypickerview", "com.chad.library", "org.parceler.apache.commons", "org.greenrobot.greendao", "com.tencent.connect", "org.parceler.apache", "kt.pieceui.adapter", "com.xiaomi.push", "org.android.agoo", "com.scwang.smartrefresh", "com.bigkoo.pickerview", "cn.sharesdk.onekeyshare"]}]}