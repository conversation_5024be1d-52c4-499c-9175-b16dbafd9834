{"status": "success", "processed_files": 3, "timestamp": "2025-08-13T15:35:39.727980", "results": [{"file": "class_example.txt", "total_packages": 9146, "identified_sdks": 47, "sdks": ["com.tencent.thumbplayer", "com.google.android", "com.tencent.mapsdk", "com.tencent.liteav", "com.tencent.matrix", "com.tencent.tencentmap", "com.tencent.midas", "com.tencent.luggage", "com.tencent.tencentmap.mapsdk", "com.tencent.qqmusic", "com.tencent.maas", "com.tencent.qqmusic.mediaplayer", "com.tencent.mapsdk.core", "com.tencent.tmassistantsdk", "com.tencent.mars", "com.tencent.wechat", "com.tencent.youtu", "com.tencent.plugin", "com.tencent.xweb", "com.tencent.pigeon", "com.google.firebase", "io.flutter.embedding.engine", "com.tencent.kinda", "com.tencent.kinda.framework", "io.flutter.plugins", "com.tencent.tinker", "io.flutter.embedding", "org.chromium.base", "com.tencent.tavkit", "io.flutter.embedding.engine.plugins", "io.flutter.plugin", "com.tencent.magicbrush", "com.tencent.thumbplayer.tplayer", "com.tencent.luggage.game", "com.tencent.youtu.sdkkitframework", "com.tencent.live", "com.tencent.tinker.loader", "com.tencent.wevision2", "com.tencent.matrix.resource", "com.tencent.thumbplayer.adapter", "com.tencent.midas.comm", "com.davemorrissey.labs", "com.tencent.recovery", "com.hihonor.easygo", "com.huawei.easygo", "com.tencent.matrix.batterycanary", "com.tencent.soter"]}, {"file": "class_example2.txt", "total_packages": 8496, "identified_sdks": 133, "sdks": ["com.tencent.qimei", "com.tencent.ams.fusion.widget", "com.tencent.qqlive.module.videoreport", "com.tencent.common", "com.xueersi.common", "com.tencent.qqlive.tvkplayer", "com.tencent.kuikly.core", "com.tencent.trpcprotocol", "com.tencent.thumbplayer", "com.tencent.rmonitor", "com.tencent.qqlive", "com.xueersi.meta.modules.plugin", "com.tencent.thumbplayer.core", "cn.hutool.core", "com.tencent.beacon", "com.tencent.qqlive.modules", "com.tencent.qqlive.module", "com.google.android.material", "com.tencent.ams.fusion.service", "com.tencent.ams.mosaic.jsengine.component", "com.xueersi.meta.modules", "com.xueersi.meta", "com.tencent.mobileqq", "com.tdsrightly.qmethod", "com.tencent.ams.mosaic.jsengine", "com.tencent.kuikly", "com.tencent.tinker", "com.tdsrightly.qmethod.monitor", "com.xueersi.parentsmeeting", "com.tencent.qqmini", "com.tencent.opentelemetry", "com.facebook.imagepipeline", "com.meizu.cloud", "com.tencent.mobileqq.triton", "com.tencent.midas", "com.xueersi.meta.base", "com.tencent.bugly", "com.xueersi.meta.base.live.framework", "com.facebook.drawee", "com.tencent.ams.fusion.service.splash", "com.tencent.mttreader", "com.tencent.gathererga", "com.meizu.cloud.pushsdk", "com.huawei.secure", "com.tencent.bugly.common", "com.tencent.rdelivery.reshub", "com.bumptech.glide", "tencent.doc.opensdk", "com.tencent.rdelivery", "com.tencent.superplayer", "com.tencent.cloud", "com.tencent.beacon.base", "com.tencent.raft", "okhttp3.internal", "com.tencent.rfix.loader", "com.tencent.rfix", "com.facebook.common", "tencent.doc.opensdk.openapi", "com.xueersi.common.business", "com.tencent.startrail.report", "com.google.android", "com.tencent.galileo", "com.tencent.paysdk", "com.tencent.upgrade", "com.tencent.ad.tangram.views.canvas.components", "com.facebook.fresco", "com.tencent.startrail", "com.tencent.portraitsdk", "com.iflytek.cloud", "com.vivo.push", "com.huawei.secure.android.common", "com.tencent.tuxmetersdk", "com.tencent.raft.raftframework", "com.tencent.matrix", "com.tencent.apkchannel", "com.tencent.open", "org.tensorflow.lite", "com.tencent.qbmnnbusiness", "org.xutils.xutils", "com.airbnb.lottie", "io.flutter.embedding.engine", "com.tencent.startrail.report.vendor", "com.tencent.odk.player.client", "com.hihonor.push", "com.tencent.connect", "io.flutter.embedding", "com.tencent.qcloud.core", "com.heytap.mcssdk", "com.facebook.fresco.animation", "com.xes.meta.modules.metaunity", "com.tencent.ad.tangram.views", "com.huawei.agconnect", "org.tensorflow.lite.support", "com.google.gson", "com.tencent.tddiag", "com.tencent.shadow", "com.tencent.qcloud", "com.jayway.jsonpath", "com.tencent.qqvideo.edgeengine", "com.tencent.ams.fusion.tbox", "com.google.archivepatcher", "com.tencent.tmediacodec", "com.tencent.ams.fusion.widget.apng", "com.xueersi.component.cloud", "com.tencent.tinker.commons", "io.flutter.embedding.engine.plugins", "com.tencent.ams.music.widget", "tmsdk.common.gourd", "com.tencent.trouter", "com.tencent.gathererga.core", "com.tencent.mttreader.epub", "tmsdk.common", "com.tencent.ad.tangram.views.canvas", "com.jayway.jsonpath.internal", "com.tencent.thumbplayer.tplayer", "com.tencent.ams.xsad.rewarded", "com.tencent.qqvideo", "com.tencent.memorycanary", "com.tencent.qbmnn", "com.tencent.mobileqq.openpay", "com.tencent.apkchannel.channel", "com.tencent.trpcprotocol.tsearchRecommend", "com.tencent.shadow.core", "com.tencent.ams.fusion.widget.animatorview", "com.tencent.rflutter", "com.tencent.lu.extension.phone", "com.tencent.nativevue.hippy", "com.tencent.feedback", "com.huya.huyasdk", "com.tencent.rmpbusiness", "com.xueersi.component", "com.facebook.drawee.backends", "com.tencent.tinker.loader"]}, {"file": "class_example3.txt", "total_packages": 1882, "identified_sdks": 63, "sdks": ["com.beizi.fusion", "com.ibplus.client", "com.google.firebase", "com.google.android.material", "com.google.android", "kt.pieceui.activity", "com.huawei.updatesdk", "com.google.android.exoplayer2", "com.google.zxing", "com.meizu.cloud", "com.taobao.aranger", "com.huawei.hianalytics", "anet.channel", "cn.jpush.android", "com.facebook.drawee", "com.huawei.appmarket", "kt.pieceui.fragment", "com.umeng.socialize", "com.huawei.appmarket.component.buoycircle", "com.meizu.cloud.pushsdk", "com.bumptech.glide", "com.huawei.android", "com.alipay.android", "com.taobao.accs", "com.huawei.appmarket.component.buoycircle.impl", "okhttp3.internal", "com.umeng.message", "com.umeng.commonsdk", "com.alibaba.fastjson", "com.example.libimagefilter", "com.liulishuo.filedownloader", "com.tencent.open", "com.luck.picture", "com.youzan.androidsdk", "com.facebook.imagepipeline", "com.youzan.spiderman", "com.iflytek.cloud", "com.alibaba.fastjson.support", "com.vivo.push", "com.google.firebase.crashlytics", "org.jetbrains.anko", "anetwork.channel", "com.scwang.smartrefresh.layout", "com.google.gson", "com.youzan.jsbridge", "org.kymjs.kjframe", "org.parceler.guava", "cn.sharesdk.framework", "com.chad.library.adapter.base", "org.parceler.apache.commons", "org.greenrobot.greendao", "com.tencent.connect", "org.parceler.apache", "kt.pieceui.adapter", "com.lljjcoder.citypickerview", "org.android.agoo", "com.chad.library", "com.scwang.smartrefresh", "com.bigkoo.pickerview", "in.srain.cube", "com.facebook.drawee.backends", "com.xiaomi.push", "com.daimajia.slider"]}]}