================================================================================
单个应用版本测试结果
App Version ID: 3792
测试时间: 2025-08-14T12:48:13.450697
================================================================================

第一步：APK分析流程结果
----------------------------------------
原始包名数量: 2481
格式化后包名数量: 2481
黑名单过滤数量: 336
剩余包名数量: 2145
识别出的SDK数量: 59
发现包数量: 432

黑名单过滤示例（前10个）:
  1. OooO00o.OooO00o.OooO00o
  2. android.app.servertransaction
  3. android.support.v4.app
  4. android.support.v4.graphics.drawable
  5. android.support.v4.media
  6. android.support.v4.media.session
  7. android.support.v4.os
  8. androidx.activity
  9. androidx.annotation
  10. androidx.annotation.experimental

识别出的SDK示例（前10个）:
  1. com.wangmai.adIdUtils
  2. com.alimm.tanx
  3. com.wangmai.adIdUtils.oaid
  4. com.alimm.tanx.core
  5. com.wangmai.adIdUtils.oaid.lib
  6. com.kwad.components
  7. com.bytedance.android
  8. com.anythink.core
  9. com.kwad.components.core
  10. com.kwad.components.offline

第二步：SDK匹配流程结果
----------------------------------------
发现包数量: 0
匹配到的已知SDK数量: 0
未匹配包数量: 0
过滤后包数量: 0
潜在SDK数量: 0
最终SDK记录数量: 0

测试总结
========================================
总原始包数量: 2481
黑名单过滤数量: 336
黑名单过滤率: 13.54%
第一步识别SDK数量: 59
第一步发现包数量: 432
第二步匹配SDK数量: 0
第二步潜在SDK数量: 0
最终总SDK数量: 0
处理成功: 是

完整JSON结果:
========================================
{
  "app_version_id": 3792,
  "timestamp": "2025-08-14T12:48:13.450697",
  "step1_apk_analysis": {
    "original_packages_count": 2481,
    "formatted_packages_count": 2481,
    "blacklist_filtered_count": 336,
    "remaining_packages_count": 2145,
    "identified_sdks_count": 59,
    "identified_sdks": [
      "com.wangmai.adIdUtils",
      "com.alimm.tanx",
      "com.wangmai.adIdUtils.oaid",
      "com.alimm.tanx.core",
      "com.wangmai.adIdUtils.oaid.lib",
      "com.kwad.components",
      "com.bytedance.android",
      "com.anythink.core",
      "com.kwad.components.core",
      "com.kwad.components.offline",
      "com.google.zxing",
      "com.tanx.onlyid",
      "com.chad.library",
      "com.bytedance.android.live",
      "com.anythink.basead",
      "com.anythink.core.common",
      "com.google.android",
      "com.kwad.components.ad.reward",
      "io.reactivex.rxjava3",
      "com.bumptech.glide",
      "com.bytedance.pangle",
      "org.jetbrains.anko",
      "com.itextpdf.text",
      "com.tanx.onlyid.core",
      "com.kwad.library",
      "com.baidu.mobads",
      "com.anythink.dlopt",
      "com.baidubce.services",
      "com.yxcorp.kuaishou",
      "com.anythink.odopt",
      "org.koin.androidx",
      "com.google.zxing.oned",
      "com.google.android.material",
      "com.bytedance.applog",
      "io.reactivex.rxjava3.internal",
      "com.umeng.commonsdk",
      "io.reactivex.internal",
      "com.kwad.framework",
      "com.wangmai.okhttp",
      "okhttp3.internal",
      "com.oueyehxa.nvyetegxj",
      "com.chad.library.adapter.base",
      "com.airbnb.lottie",
      "com.anythink.network",
      "com.scwang.smartrefresh",
      "com.kwad.library.solder",
      "com.anythink.banner",
      "org.koin.android",
      "com.anythink.dlopt.common",
      "com.anythink.interstitial",
      "com.anythink.nativead",
      "com.anythink.rewardvideo",
      "com.anythink.splashad",
      "com.kwad.framework.filedownloader",
      "org.koin.core",
      "com.wangmai.common",
      "com.scwang.smartrefresh.layout",
      "com.youth.banner",
      "com.bigkoo.pickerview"
    ],
    "discovered_packages_count": 432,
    "blacklist_examples": [
      "OooO00o.OooO00o.OooO00o",
      "android.app.servertransaction",
      "android.support.v4.app",
      "android.support.v4.graphics.drawable",
      "android.support.v4.media",
      "android.support.v4.media.session",
      "android.support.v4.os",
      "androidx.activity",
      "androidx.annotation",
      "androidx.annotation.experimental"
    ],
    "sdk_examples": [
      "com.wangmai.adIdUtils",
      "com.alimm.tanx",
      "com.wangmai.adIdUtils.oaid",
      "com.alimm.tanx.core",
      "com.wangmai.adIdUtils.oaid.lib",
      "com.kwad.components",
      "com.bytedance.android",
      "com.anythink.core",
      "com.kwad.components.core",
      "com.kwad.components.offline"
    ]
  },
  "step2_sdk_matching": {
    "discovered_packages_count": 0,
    "matched_sdks_count": 0,
    "matched_sdks": [],
    "unmatched_packages_count": 0,
    "filtered_packages_count": 0,
    "potential_sdks_count": 0,
    "potential_sdks": [],
    "final_sdk_records_count": 0
  },
  "summary": {
    "total_original_packages": 2481,
    "blacklist_filtered_packages": 336,
    "blacklist_filter_rate": 13.54,
    "step1_identified_sdks": 59,
    "step1_discovered_packages": 432,
    "step2_matched_sdks": 0,
    "step2_potential_sdks": 0,
    "final_total_sdks": 0,
    "processing_success": true
  }
}