#!/usr/bin/env python3
# coding: utf-8

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 直接硬编码数据库配置
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "tenyy_app",
    "username": "admin",
    "password": "zhangdi168",
}

print(f"数据库配置: {DATABASE_CONFIG}")

# 添加模型路径
models_path = project_root / "tenyy" / "src" / "models"
sys.path.insert(0, str(models_path))

# 直接定义模型类（与修改后的模型一致）
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, String, Text, DateTime
from sqlalchemy.dialects.postgresql import INTEGER

Base = declarative_base()

class class_SDKKnowledgeBase(Base):
    """SDK知识库表，存储所有已识别的SDK信息"""
    __tablename__ = 'class_sdk_knowledge_base'

    id = Column(INTEGER, primary_key=True, autoincrement=True)  # 主键ID
    package_prefix = Column(String(255), unique=True, nullable=False)  # SDK的关键包名前缀
    sdk_name = Column(String(255), nullable=True)  # SDK的正式名称
    company_name = Column(String(255), nullable=True)  # 公司名称
    version = Column(String(100), nullable=True)  # SDK版本
    category = Column(String(100), nullable=True)  # SDK主分类
    subcategory = Column(String(100), nullable=True)  # SDK子分类
    description = Column(Text, nullable=True)  # SDK的功能描述
    brief_message = Column(Text, nullable=True)  # 简要信息
    official_web = Column(Text, nullable=True)  # 官方网站URL
    sdk_document_url = Column(Text, nullable=True)  # SDK文档URL
    privacy_policy_url = Column(Text, nullable=True)  # 隐私政策URL
    compliance_instructions_url = Column(Text, nullable=True)  # 合规说明URL
    status = Column(String(50), nullable=True)  # 记录状态: identified, pending_analysis, error
    last_checked_at = Column(DateTime, nullable=True)  # 上次检查或更新的时间

    def __repr__(self):
        return f"<class_SDKKnowledgeBase(package_prefix='{self.package_prefix}', sdk_name='{self.sdk_name}', status='{self.status}')>"


def load_json_data(file_path):
    """加载JSON数据文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def create_db_session():
    """创建数据库会话"""
    # 使用local.py中的数据库配置
    db_url = f"postgresql+psycopg2://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
    engine = create_engine(db_url)
    Session = sessionmaker(bind=engine)
    return Session()


def import_sdk_data(json_file_path):
    """将SDK数据从JSON文件导入到数据库"""
    # 加载JSON数据
    print(f"加载数据文件: {json_file_path}")
    data = load_json_data(json_file_path)
    
    # 创建数据库会话
    session = create_db_session()
    
    try:
        sdk_count = 0
        duplicate_count = 0
        
        # 遍历所有分类和子分类中的SDK
        for category, subcategories in data.get("technologies", {}).items():
            for subcategory, sdk_list in subcategories.items():
                for sdk_item in sdk_list:
                    package_name = sdk_item.get("packageName") or sdk_item.get("package_prefix")
                    
                    # 如果没有包名，跳过该记录
                    if not package_name or package_name == "dontknow":
                        continue
                    
                    # 检查是否已存在相同的package_prefix
                    existing_sdk = session.query(class_SDKKnowledgeBase).filter_by(
                        package_prefix=package_name
                    ).first()
                    
                    if existing_sdk:
                        # 如果已存在，可以选择更新或跳过
                        print(f"跳过已存在的SDK: {package_name}")
                        duplicate_count += 1
                        continue
                    
                    # 创建新的SDK记录
                    sdk_record = class_SDKKnowledgeBase(
                        package_prefix=package_name,
                        sdk_name=sdk_item.get("sdkName"),
                        company_name=sdk_item.get("companyName"),
                        version=sdk_item.get("version"),
                        category=category,
                        subcategory=subcategory,
                        description=sdk_item.get("enhancedDescription"),
                        brief_message=sdk_item.get("briefMsg"),
                        official_web=sdk_item.get("officialWeb"),
                        sdk_document_url=sdk_item.get("sdkDocumentUrl"),
                        privacy_policy_url=sdk_item.get("privacyPolicyUrl"),
                        compliance_instructions_url=sdk_item.get("sdkComplianceInstructionsUrl"),
                        status="identified",
                        last_checked_at=datetime.now()
                    )
                    
                    session.add(sdk_record)
                    sdk_count += 1
                    
                    # 每100条记录提交一次
                    if sdk_count % 100 == 0:
                        session.commit()
                        print(f"已导入 {sdk_count} 条SDK记录...")
        
        # 提交剩余的记录
        session.commit()
        print(f"导入完成! 成功导入 {sdk_count} 条SDK记录，跳过 {duplicate_count} 条重复记录。")
        
    except Exception as e:
        session.rollback()
        print(f"导入过程中出错: {e}")
        raise
    finally:
        session.close()


def main():
    # JSON文件路径
    json_file_path = "assets/new_tech_together.json"
    full_path = Path(__file__).resolve().parent / json_file_path
    
    if not full_path.exists():
        print(f"找不到文件: {full_path}")
        return
    
    print("开始导入SDK数据到数据库...")
    import_sdk_data(str(full_path))


if __name__ == "__main__":
    main()