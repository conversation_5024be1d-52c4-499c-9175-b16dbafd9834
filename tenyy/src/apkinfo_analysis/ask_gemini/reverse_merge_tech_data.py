import json
from typing import Dict, List, Any, Set
from collections import defaultdict

def load_json_file(file_path: str) -> Dict[str, Any]:
    """加载JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json_file(data: Dict[str, Any], file_path: str):
    """保存JSON文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def analyze_corrected_sdk_categories(corrected_data: Dict[str, Any]) -> Dict[str, int]:
    """分析corrected_sdk_data.json的分类体系"""
    categories = defaultdict(int)
    
    for item in corrected_data.get("data", []):
        type_name = item.get("typeName", "Unknown")
        categories[type_name] += 1
    
    print("corrected_sdk_data.json 分类体系:")
    # 处理可能的None值
    sorted_categories = sorted([(k if k is not None else "None", v) for k, v in categories.items()])
    for type_name, count in sorted_categories:
        print(f"  - {type_name} ({count} SDKs)")
    
    return categories

def analyze_new_tech_categories(new_tech_data: Dict[str, Any]) -> Dict[str, Dict[str, int]]:
    """分析new_tech.json的分类体系"""
    categories = {}
    
    for category, subcategories in new_tech_data.get("technologies", {}).items():
        categories[category] = {}
        for subcategory, sdk_list in subcategories.items():
            categories[category][subcategory] = len(sdk_list)
    
    print("\nnew_tech.json 分类体系:")
    for category, subcategories in categories.items():
        print(f"  - {category}")
        for subcategory, count in subcategories.items():
            print(f"    - {subcategory} ({count} SDKs)")
    
    return categories

def create_reverse_merge_mapping() -> Dict[str, tuple]:
    """创建反向分类映射关系，从corrected_sdk分类映射到new_tech分类"""
    # 定义如何将corrected_sdk的分类映射到new_tech的分类体系
    # 格式: {"corrected_sdk分类": ("new_tech主分类", "new_tech子分类")}
    mapping = {
        "广告类": ("Advertising and Marketing", "Advertising and Monetization"),
        "统计类": ("Analytics", "User Behavior Analysis"),
        "特定工具类": ("Development Tools", "Function Component"),
        "第三方登录类": ("Development Tools", "Login and Share"),
        "游戏联运类": ("Game", "Game Platform"),
        "实时音视频类": ("Audio and Video", "Real-time Audio and Video"),
        "认证类": ("Development Tools", "Identity Verification"),
        "框架类": ("Development Tools", "Development Frameworks"),
        "推送类": ("Development Tools", "Message Pushing"),
        "社交类": ("Integration Services", "Social Networking"),
        "地图类": ("Integration Services", "Map Services"),
        "客服类": ("Operations and Maintenance Service", "Customer Experience"),
        "安全风控类": ("Security", "Risk Control"),
        "人工智能类": ("Development Tools", "Machine Learning"),
        "平台服务类": ("Integration Services", "Application Full Chain Service"),
        "性能监控类": ("Operations and Maintenance Service", "Performance Management"),
        "支付类": ("Integration Services", "Payment")
        # 未映射的分类将被忽略或需要特殊处理
    }
    
    return mapping

def convert_corrected_sdk_to_new_tech_format(corrected_data: Dict[str, Any]) -> Dict[str, Any]:
    """将corrected_sdk_data格式转换为new_tech格式"""
    # 创建新的数据结构，以new_tech.json为模板
    converted_data = {
        "technologies": {}
    }
    
    # 获取分类映射
    category_mapping = create_reverse_merge_mapping()
    
    # 按分类组织数据
    categorized_data = defaultdict(lambda: defaultdict(list))
    
    for item in corrected_data.get("data", []):
        type_name = item.get("typeName", "Unknown")
        
        # 只处理有映射关系的分类
        if type_name in category_mapping:
            main_category, sub_category = category_mapping[type_name]
            categorized_data[main_category][sub_category].append(item)
    
    # 转换数据格式
    for main_category, subcategories in categorized_data.items():
        converted_data["technologies"][main_category] = {}
        
        for sub_category, items in subcategories.items():
            converted_data["technologies"][main_category][sub_category] = []
            
            for item in items:
                # 转换为new_tech格式
                new_item = {
                    "sdkName": item.get("sdkName", ""),
                    "typeName": item.get("typeName", ""),
                    "companyName": item.get("companyName", "") or "dontknow",
                    "version": item.get("version", "") or "dontknow",
                    "officialWeb": item.get("officialWeb", "") or "dontknow",
                    "sdkDocumentUrl": item.get("sdkDocumentUrl", "") or "dontknow",
                    "privacyPolicyUrl": item.get("privacyPolicyUrl", "") or "dontknow",
                    "sdkComplianceInstructionsUrl": item.get("sdkComplianceInstructionsUrl", "") or "dontknow",
                    "packageName": item.get("packageName", "") or "dontknow",
                    "enhancedDescription": item.get("enhancedDescription", ""),
                    "briefMsg": item.get("briefMsg", "")
                }
                
                converted_data["technologies"][main_category][sub_category].append(new_item)
    
    return converted_data

def merge_data_onto_new_tech(corrected_data: Dict[str, Any], new_tech_data: Dict[str, Any]) -> Dict[str, Any]:
    """将corrected_sdk_data合并到new_tech_data上，以new_tech的分类体系为主"""
    # 首先复制new_tech_data作为基础
    merged_data = {
        "technologies": {}
    }
    
    # 复制new_tech_data的所有数据
    for category, subcategories in new_tech_data.get("technologies", {}).items():
        merged_data["technologies"][category] = {}
        for subcategory, sdk_list in subcategories.items():
            # 复制现有的SDK列表
            merged_data["technologies"][category][subcategory] = []
            for sdk in sdk_list:
                # 创建深拷贝以避免修改原始数据
                copied_sdk = {key: value for key, value in sdk.items()}
                merged_data["technologies"][category][subcategory].append(copied_sdk)
    
    # 转换corrected_sdk_data格式
    converted_corrected = convert_corrected_sdk_to_new_tech_format(corrected_data)
    
    # 合并转换后的数据
    merged_count = 0
    for category, subcategories in converted_corrected.get("technologies", {}).items():
        if category in merged_data["technologies"]:
            for subcategory, sdk_list in subcategories.items():
                if subcategory in merged_data["technologies"][category]:
                    # 如果子分类已存在，则追加数据
                    merged_data["technologies"][category][subcategory].extend(sdk_list)
                    merged_count += len(sdk_list)
                else:
                    # 如果子分类不存在，则创建新的子分类
                    merged_data["technologies"][category][subcategory] = sdk_list
                    merged_count += len(sdk_list)
        else:
            # 如果主分类不存在，则创建新的主分类
            merged_data["technologies"][category] = subcategories
            for subcategory, sdk_list in subcategories.items():
                merged_count += len(sdk_list)
    
    print(f"\n从corrected_sdk_data.json中合并了 {merged_count} 个SDK条目")
    
    return merged_data

def count_total_sdks(tech_data: Dict[str, Any]) -> int:
    """统计技术数据中的SDK总数"""
    total = 0
    for category, subcategories in tech_data.get("technologies", {}).items():
        for subcategory, sdk_list in subcategories.items():
            total += len(sdk_list)
    return total

def main():
    # 加载数据文件
    print("加载 corrected_sdk_data.json...")
    corrected_data = load_json_file("assets/corrected_sdk_data.json")
    
    print("加载 new_tech.json...")
    new_tech_data = load_json_file("assets/new_tech.json")
    
    # 分析分类体系
    print("\n=== 分类体系分析 ===")
    corrected_categories = analyze_corrected_sdk_categories(corrected_data)
    new_tech_categories = analyze_new_tech_categories(new_tech_data)
    
    # 创建合并策略
    print("\n=== 合并策略 ===")
    mapping = create_reverse_merge_mapping()
    print("分类映射关系:")
    for corrected_cat, (new_cat, new_subcat) in mapping.items():
        print(f"  {corrected_cat} 映射到 {new_cat} -> {new_subcat}")
    
    # 显示合并前的统计信息
    new_tech_count = count_total_sdks(new_tech_data)
    corrected_count = len(corrected_data.get("data", []))
    print(f"\n合并前统计:")
    print(f"  new_tech.json 包含 {new_tech_count} 个SDK")
    print(f"  corrected_sdk_data.json 包含 {corrected_count} 个SDK")
    
    # 合并数据
    print("\n=== 数据合并 ===")
    merged_data = merge_data_onto_new_tech(corrected_data, new_tech_data)
    
    # 显示合并后的统计信息
    merged_count = count_total_sdks(merged_data)
    print(f"\n合并后统计:")
    print(f"  new_tech_together.json 包含 {merged_count} 个SDK")
    
    # 保存合并后的数据
    output_file = "assets/new_tech_together.json"
    print(f"\n保存合并后的数据到 {output_file}...")
    save_json_file(merged_data, output_file)
    
    print("合并完成！")

if __name__ == "__main__":
    main()