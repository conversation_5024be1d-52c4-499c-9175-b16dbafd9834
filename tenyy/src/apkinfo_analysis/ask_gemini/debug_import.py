#!/usr/bin/env python3
# coding: utf-8

import json
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent.parent.parent
sys.path.insert(0, str(project_root))

print(f"项目根目录: {project_root}")

# 设置环境变量以使用local配置
os.environ['TENYY_ENV'] = 'local'

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 硬编码数据库配置（从之前查看的local.py中获取）
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "tenyy_app",
    "username": "admin",
    "password": "zhangdi168",
}

print(f"数据库配置: {DATABASE_CONFIG}")

# 添加src路径以便正确导入模型
src_path = tenyy_path / "src"
sys.path.insert(0, str(src_path))

# 添加tenyy模块路径
tenyy_path = project_root / "tenyy"
sys.path.insert(0, str(tenyy_path))

# 检查模型文件是否存在
model_path = tenyy_path / "src" / "models" / "class_sdk_knowledge_base.py"
print(f"模型文件路径: {model_path}")
if not model_path.exists():
    print(f"❌ 模型文件不存在: {model_path}")
    sys.exit(1)

from class_sdk_knowledge_base import class_SDKKnowledgeBase


def create_db_session():
    """创建数据库会话"""
    # 使用local.py中的数据库配置
    db_url = f"postgresql+psycopg2://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
    print(f"数据库连接URL: {db_url}")
    engine = create_engine(db_url, echo=True)  # 启用echo可以看到SQL语句
    Session = sessionmaker(bind=engine)
    return Session()


def test_insert():
    """测试插入一条记录"""
    # 创建数据库会话
    session = create_db_session()
    
    try:
        # 创建测试记录
        test_sdk = class_SDKKnowledgeBase(
            package_prefix="com.test.sdk",
            sdk_name="Test SDK",
            company_name="Test Company",
            version="1.0.0",
            category="Test Category",
            subcategory="Test Subcategory",
            description="This is a test SDK for debugging purposes",
            brief_message="Test SDK brief message",
            official_web="https://test-sdk.example.com",
            sdk_document_url="https://docs.test-sdk.example.com",
            privacy_policy_url="https://privacy.test-sdk.example.com",
            compliance_instructions_url="https://compliance.test-sdk.example.com",
            status="identified",
            last_checked_at=datetime.now()
        )
        
        print("准备插入测试记录...")
        print(f"记录内容: {test_sdk}")
        
        # 添加并提交记录
        session.add(test_sdk)
        session.commit()
        print("✅ 测试记录插入成功!")
        
    except Exception as e:
        session.rollback()
        print(f"❌ 插入过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()


def main():
    print("开始测试数据库插入功能...")
    test_insert()


if __name__ == "__main__":
    main()