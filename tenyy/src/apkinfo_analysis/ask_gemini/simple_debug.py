#!/usr/bin/env python3
# coding: utf-8

import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.dialects.postgresql import INTEGER

# 直接硬编码数据库配置
DATABASE_URL = "postgresql+psycopg2://admin:zhangdi168@localhost:5432/tenyy_app"
print(f"数据库连接URL: {DATABASE_URL}")

# 添加模型路径
models_path = project_root / "tenyy" / "src" / "models"
sys.path.insert(0, str(models_path))

# 直接定义模型类（与修改后的模型一致）
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, String, Text, DateTime

Base = declarative_base()

class class_SDKKnowledgeBase(Base):
    """SDK知识库表，存储所有已识别的SDK信息"""
    __tablename__ = 'class_sdk_knowledge_base'

    id = Column(INTEGER, primary_key=True, autoincrement=True)  # 主键ID
    package_prefix = Column(String(255), unique=True, nullable=False)  # SDK的关键包名前缀
    sdk_name = Column(String(255), nullable=True)  # SDK的正式名称
    company_name = Column(String(255), nullable=True)  # 公司名称
    version = Column(String(100), nullable=True)  # SDK版本
    category = Column(String(100), nullable=True)  # SDK主分类
    subcategory = Column(String(100), nullable=True)  # SDK子分类
    description = Column(Text, nullable=True)  # SDK的功能描述
    brief_message = Column(Text, nullable=True)  # 简要信息
    official_web = Column(Text, nullable=True)  # 官方网站URL
    sdk_document_url = Column(Text, nullable=True)  # SDK文档URL
    privacy_policy_url = Column(Text, nullable=True)  # 隐私政策URL
    compliance_instructions_url = Column(Text, nullable=True)  # 合规说明URL
    status = Column(String(50), nullable=True)  # 记录状态: identified, pending_analysis, error
    last_checked_at = Column(DateTime, nullable=True)  # 上次检查或更新的时间

    def __repr__(self):
        return f"<class_SDKKnowledgeBase(package_prefix='{self.package_prefix}', sdk_name='{self.sdk_name}', status='{self.status}')>"


def create_db_session():
    """创建数据库会话"""
    engine = create_engine(DATABASE_URL, echo=True)  # 启用echo可以看到SQL语句
    Session = sessionmaker(bind=engine)
    return Session()


def test_insert():
    """测试插入一条记录"""
    # 创建数据库会话
    session = create_db_session()
    
    try:
        # 创建测试记录
        test_sdk = class_SDKKnowledgeBase(
            package_prefix="com.test.sdk",
            sdk_name="Test SDK",
            company_name="Test Company",
            version="1.0.0",
            category="Test Category",
            subcategory="Test Subcategory",
            description="This is a test SDK for debugging purposes",
            brief_message="Test SDK brief message",
            official_web="https://test-sdk.example.com",
            sdk_document_url="https://docs.test-sdk.example.com",
            privacy_policy_url="https://privacy.test-sdk.example.com",
            compliance_instructions_url="https://compliance.test-sdk.example.com",
            status="identified",
            last_checked_at=datetime.now()
        )
        
        print("准备插入测试记录...")
        print(f"记录内容: {test_sdk}")
        
        # 添加并提交记录
        session.add(test_sdk)
        session.commit()
        print("✅ 测试记录插入成功!")
        
    except Exception as e:
        session.rollback()
        print(f"❌ 插入过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()


def main():
    print("开始测试数据库插入功能...")
    test_insert()


if __name__ == "__main__":
    main()