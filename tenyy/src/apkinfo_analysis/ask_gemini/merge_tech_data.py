import json
from typing import Dict, List, Any, Set

def load_json_file(file_path: str) -> Dict[str, Any]:
    """加载JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json_file(data: Dict[str, Any], file_path: str):
    """保存JSON文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def analyze_corrected_sdk_categories(corrected_data: Dict[str, Any]) -> Dict[str, Set[str]]:
    """分析corrected_sdk_data.json的分类体系"""
    categories = {}
    
    for item in corrected_data.get("data", []):
        type_name = item.get("typeName", "Unknown")
        sdk_name = item.get("sdkName", "Unknown SDK")
        
        if type_name not in categories:
            categories[type_name] = set()
        
        # 尝试从sdkName或packageName中提取可能的子分类线索
        # 这里我们简单地将所有同类型的SDK归为一类
        # 在实际应用中，可能需要更复杂的逻辑来确定子分类
    
    print("corrected_sdk_data.json 分类体系:")
    for type_name in categories:
        print(f"  - {type_name}")
    
    return categories

def analyze_new_tech_categories(new_tech_data: Dict[str, Any]) -> Dict[str, Dict[str, int]]:
    """分析new_tech.json的分类体系"""
    categories = {}
    
    for category, subcategories in new_tech_data.get("technologies", {}).items():
        categories[category] = {}
        for subcategory, sdk_list in subcategories.items():
            categories[category][subcategory] = len(sdk_list)
    
    print("\nnew_tech.json 分类体系:")
    for category, subcategories in categories.items():
        print(f"  - {category}")
        for subcategory, count in subcategories.items():
            print(f"    - {subcategory} ({count} SDKs)")
    
    return categories

def create_merge_mapping() -> Dict[str, Dict[str, str]]:
    """创建完整的分类映射关系"""
    # 定义如何将new_tech的分类映射到corrected_sdk的分类体系
    mapping = {
        "Advertising and Marketing": {
            "Advertising Aggregation": "广告类",
            "Advertising Attribution": "广告类",
            "Advertising and Monetization": "广告类",
            "Content Marketing": "广告类",
            "Marketing Platform": "广告类",
            "Marketing Tools": "广告类"
        },
        "Analytics": {
            "A/B Testing": "统计类",
            "Intelligent Video Analysis": "统计类",
            "User Behavior Analysis": "统计类",
            "User Profile": "统计类",
            "Analytics": "统计类"
        },
        "Audio and Video": {
            "Audio Processing": "实时音视频类",
            "Beauty Filter": "实时音视频类",
            "Interactive Whiteboard": "实时音视频类",
            "Media Player": "实时音视频类",
            "Real-time Audio and Video": "实时音视频类",
            "Real-time Voice": "实时音视频类",
            "Short Video": "实时音视频类",
            "Video Engine": "实时音视频类",
            "Video Live Streaming": "实时音视频类",
            "VoIP": "实时音视频类"
        },
        "Development Tools": {
            "AR/VR": "特定工具类",
            "Deep Linking": "特定工具类",
            "Development Frameworks": "框架类",
            "Full Stack Integration": "特定工具类",
            "Function Component": "特定工具类",
            "Identity Verification": "认证类",
            "Language Processing": "人工智能类",
            "Location Information": "地图类",
            "Login and Share": "第三方登录类",
            "Machine Learning": "人工智能类",
            "Message Pushing": "推送类",
            "Near Field Communication": "特定工具类",
            "SMS and Email": "特定工具类",
            "Scan Recognition": "特定工具类"
        },
        "Game": {
            "Game Data Analysis": "游戏联运类",
            "Game Engine": "游戏联运类",
            "Game Interface": "游戏联运类",
            "Game Platform": "游戏联运类",
            "Game Services": "游戏联运类"
        },
        "Integration Services": {
            "Application Full Chain Service": "平台服务类",
            "Business Communication": "平台服务类",
            "E-commerce Platform": "平台服务类",
            "Education Service": "平台服务类",
            "Instant Messaging": "社交类",
            "Intelligent Chat": "社交类",
            "Map Services": "地图类",
            "Payment": "支付类",
            "Real-time Messaging": "社交类",
            "Route Navigation": "地图类",
            "Smart Space": "平台服务类",
            "Social Networking": "社交类"
        },
        "Operations and Maintenance Service": {
            "Cloud Infrastructure Service": "平台服务类",
            "Cloud Storage": "平台服务类",
            "Consent Management Platform": "平台服务类",
            "Customer Experience": "客服类",
            "Data Management Platform": "平台服务类",
            "Database": "平台服务类",
            "Intelligent Platform": "平台服务类",
            "Network Connection": "平台服务类",
            "Performance Management": "性能监控类",
            "State Synchronization": "平台服务类",
            "User Interface": "特定工具类"
        },
        "Security": {
            "Data Encryption": "安全风控类",
            "Mobile Security": "安全风控类",
            "Risk Control": "安全风控类",
            "Security Verification": "安全风控类"
        }
    }
    
    return mapping

def merge_data(corrected_data: Dict[str, Any], new_tech_data: Dict[str, Any]) -> Dict[str, Any]:
    """合并两个数据源"""
    # 创建新的数据结构，以corrected_sdk_data为模板
    merged_data = {
        "total_count": corrected_data.get("total_count", 0),
        "crawl_time": corrected_data.get("crawl_time", ""),
        "enhance_time": corrected_data.get("enhance_time", ""),
        "platform": corrected_data.get("platform", ""),
        "enhanced": corrected_data.get("enhanced", False),
        "data": corrected_data.get("data", []).copy()
    }
    
    # 获取分类映射
    category_mapping = create_merge_mapping()
    
    # 从new_tech_data中提取数据并转换格式
    new_sdk_count = 0
    
    for category, subcategories in new_tech_data.get("technologies", {}).items():
        if category in category_mapping:
            for subcategory, sdk_list in subcategories.items():
                if subcategory in category_mapping[category]:
                    target_type = category_mapping[category][subcategory]
                    
                    # 转换每个SDK条目格式
                    for sdk in sdk_list:
                        # 创建符合corrected_sdk_data格式的条目
                        new_sdk_entry = {
                            "recordId": "",  # 留空，因为没有recordId
                            "sdkId": "",     # 留空
                            "sdkIcon": "",   # 留空
                            "sdkName": sdk.get("sdkName", ""),
                            "typeId": "",    # 留空
                            "companyId": "", # 留空
                            "typeName": target_type,
                            "companyName": sdk.get("companyName") or "",
                            "version": sdk.get("version") or "",
                            "installationAmount": 0,
                            "platform": 1,   # Android平台
                            "officialWeb": sdk.get("officialWeb") or "",
                            "sdkDocumentUrl": sdk.get("sdkDocumentUrl") or "",
                            "privacyPolicyUrl": sdk.get("privacyPolicyUrl") or "",
                            "briefMsg": sdk.get("briefMsg", ""),
                            "isTop": 0,
                            "createTime": "", # 留空
                            "updateTime": "", # 留空
                            "isCompliance": 0,
                            "sdkComplianceInstructionsUrl": sdk.get("sdkComplianceInstructionsUrl") or "",
                            "msgType": 1,
                            "cornerMark": None,
                            "riskSdkData": None,
                            "packageName": sdk.get("packageName") or "",
                            "enhancedDescription": sdk.get("enhancedDescription", ""),
                            "sdkSize": "Unknown",
                            "minSdkVersion": "Unknown",
                            "permissions": [],
                            "packageSource": "Known"
                        }
                        
                        merged_data["data"].append(new_sdk_entry)
                        new_sdk_count += 1
    
    # 更新总计数
    merged_data["total_count"] = len(merged_data["data"])
    
    print(f"\n从new_tech.json中合并了 {new_sdk_count} 个SDK条目")
    print(f"合并后总共有 {merged_data['total_count']} 个SDK条目")
    
    return merged_data

def main():
    # 加载数据文件
    print("加载 corrected_sdk_data.json...")
    corrected_data = load_json_file("assets/corrected_sdk_data.json")
    
    print("加载 new_tech.json...")
    new_tech_data = load_json_file("assets/new_tech.json")
    
    # 分析分类体系
    print("\n=== 分类体系分析 ===")
    corrected_categories = analyze_corrected_sdk_categories(corrected_data)
    new_tech_categories = analyze_new_tech_categories(new_tech_data)
    
    # 创建合并策略
    print("\n=== 合并策略 ===")
    mapping = create_merge_mapping()
    print("分类映射关系:")
    for new_cat, subcats in mapping.items():
        for new_subcat, target_cat in subcats.items():
            print(f"  {new_cat} -> {new_subcat} 映射到 {target_cat}")
    
    # 合并数据
    print("\n=== 数据合并 ===")
    merged_data = merge_data(corrected_data, new_tech_data)
    
    # 保存合并后的数据
    output_file = "assets/new_tech_together.json"
    print(f"\n保存合并后的数据到 {output_file}...")
    save_json_file(merged_data, output_file)
    
    print("合并完成！")

if __name__ == "__main__":
    main()