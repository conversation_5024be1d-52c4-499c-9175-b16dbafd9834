import json
import time
import os
import re
from typing import Dict, List, Any
import google.generativeai as genai

# 配置API密钥
genai.configure(api_key="AIzaSyBZCalOmsEWl7TmHLg9x2cOoLBj4vxEOkc")

def load_tech_catalog(file_path: str) -> Dict[str, Any]:
    """加载技术目录JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_tech_catalog(data: Dict[str, Any], file_path: str):
    """保存技术目录到JSON文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def append_to_tech_catalog(category: str, subcategory: str, data: List[Dict[str, Any]], file_path: str):
    """追加数据到技术目录JSON文件"""
    # 如果文件不存在，创建新文件
    if not os.path.exists(file_path):
        initial_data = {"technologies": {}}
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, ensure_ascii=False, indent=2)
    
    # 读取现有数据
    with open(file_path, 'r', encoding='utf-8') as f:
        try:
            existing_data = json.load(f)
        except json.JSONDecodeError:
            existing_data = {"technologies": {}}
    
    # 初始化结构
    if "technologies" not in existing_data:
        existing_data["technologies"] = {}
    
    if category not in existing_data["technologies"]:
        existing_data["technologies"][category] = {}
        
    if subcategory not in existing_data["technologies"][category]:
        existing_data["technologies"][category][subcategory] = []
    
    # 追加新数据
    existing_data["technologies"][category][subcategory].extend(data)
    
    # 保存更新后的数据
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(existing_data, f, ensure_ascii=False, indent=2)

def create_prompt(sdk_entries: List[Dict[str, Any]]) -> str:
    """为Gemini API创建提示词"""
    prompt = """
你是一个移动应用SDK分析专家。请分析以下SDK信息，并完善缺失的信息。

对于每个SDK条目，请根据已知信息判断并填写以下字段尽可能的完善，（如果为"dontknow"就重新思考），如果不知道就填写 null，不要遐想或者猜，尽可能用中文：
- companyName: 公司名称
- version: SDK版本
- officialWeb: 官方网站URL
- sdkDocumentUrl: SDK文档URL
- privacyPolicyUrl: 隐私政策URL
- sdkComplianceInstructionsUrl: 合规说明URL
- packageName: SDK包名
- enhancedDescription: 增强描述（更详细的SDK功能描述）
- briefMsg: 简要信息（简洁的SDK功能说明）

请严格按照JSON格式返回结果，只返回JSON，不要添加额外的文本或解释。

输入数据：
"""
    prompt += json.dumps(sdk_entries, ensure_ascii=False, indent=2)
    prompt += "\n\n请返回相同结构的JSON数组，但包含完善的信息，只返回JSON，不要添加任何其他文本："
    return prompt

def extract_json_from_response(response_text: str) -> Any:
    """从响应中提取JSON"""
    # 尝试直接解析
    try:
        return json.loads(response_text)
    except json.JSONDecodeError:
        pass
    
    # 尝试提取代码块中的JSON
    json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response_text)
    if json_match:
        try:
            return json.loads(json_match.group(1))
        except json.JSONDecodeError:
            pass
    
    # 尝试清理文本后解析
    cleaned_text = response_text.strip()
    if cleaned_text.startswith('```'):
        cleaned_text = cleaned_text[3:]
    if cleaned_text.endswith('```'):
        cleaned_text = cleaned_text[:-3]
    if cleaned_text.startswith('json'):
        cleaned_text = cleaned_text[4:]
    cleaned_text = cleaned_text.strip()
    
    try:
        return json.loads(cleaned_text)
    except json.JSONDecodeError:
        # 如果所有方法都失败，返回None
        return None

def process_with_gemini(sdk_entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """使用Gemini API处理SDK条目"""
    try:
        # 设置模型
        model = genai.GenerativeModel('gemini-2.5-flash')
        
        prompt = create_prompt(sdk_entries)
        
        # 生成内容
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=88192,
            )
        )
        
        # 打印响应内容用于调试（仅在出错时）
        # print("Gemini API 响应内容:")
        # print(response.text)
        # print("-" * 50)
        
        # 尝试解析JSON
        result = extract_json_from_response(response.text)
        if result is not None:
            return result
        else:
            print("无法从响应中提取有效的JSON:")
            print(response.text[:200] + "..." if len(response.text) > 200 else response.text)
            return sdk_entries
                
    except Exception as e:
        print(f"处理条目时出错: {e}")
        # 出错时返回原始条目
        return sdk_entries

def process_tech_catalog_in_batches(catalog: Dict[str, Any], output_file: str, batch_size: int = 10) -> None:
    """批量处理技术目录，每批处理后立即保存到文件"""
    
    # 用于跟踪API调用时间，确保不超过每分钟10次
    api_call_times = []
    
    def wait_if_needed():
        """检查是否需要等待以满足API速率限制"""
        current_time = time.time()
        # 移除超过60秒的调用记录
        api_call_times[:] = [t for t in api_call_times if current_time - t < 60]
        
        # 如果在过去1分钟内已有10次调用，则等待
        if len(api_call_times) >= 10:
            oldest_call = api_call_times[0]
            sleep_time = 60 - (current_time - oldest_call) + 1  # 多等1秒确保安全
            print(f"为满足API速率限制，等待 {sleep_time:.1f} 秒...")
            time.sleep(sleep_time)
            # 更新当前时间
            current_time = time.time()
            # 再次清理过期的调用记录
            api_call_times[:] = [t for t in api_call_times if current_time - t < 60]
        
        # 记录本次调用时间
        api_call_times.append(current_time)
    
    for category, subcategories in catalog["technologies"].items():
        
        for subcategory, sdk_list in subcategories.items():
            
            # 分批处理
            for i in range(0, len(sdk_list), batch_size):
                batch = sdk_list[i:i + batch_size]
                print(f"处理分类 '{category}' - '{subcategory}' 的第 {i//batch_size + 1} 批...")
                
                # 检查是否需要等待以满足API速率限制
                wait_if_needed()
                
                # 使用Gemini处理这一批
                processed_batch = process_with_gemini(batch)
                
                # 立即追加保存到文件
                append_to_tech_catalog(category, subcategory, processed_batch, output_file)
                print(f"已将 '{category}' - '{subcategory}' 的第 {i//batch_size + 1} 批结果保存到 {output_file}")
                
                # 添加小延迟以避免过于频繁的调用
                time.sleep(1)

def main():
    # 加载原始数据
    input_file = "assets/reorganized_class_tech_catalog.json"
    output_file = "assets/new_tech.json"
    
    print("加载技术目录数据...")
    catalog = load_tech_catalog(input_file)
    
    print("开始处理数据...")
    process_tech_catalog_in_batches(catalog, output_file)
    
    print("所有数据处理完成！结果已保存到", output_file)

if __name__ == "__main__":
    main()