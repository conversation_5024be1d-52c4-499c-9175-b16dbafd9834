# -*- coding: utf-8 -*-
"""
APK Processor Flow

APK处理流程 - 系统的"生产线"
接收一批任务并以最大并发度并行处理。
包含批量处理和单个APK处理子流程。
"""

import os
import asyncio
import tempfile
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json
from concurrent.futures import ProcessPoolExecutor
import atexit

from prefect import flow, task, get_run_logger

# 全局进程池执行器，用于CPU密集型任务
# 在模块加载时创建，在程序退出时关闭，以供所有并发任务共享
cpu_executor = ProcessPoolExecutor(max_workers=os.cpu_count())
atexit.register(lambda: cpu_executor.shutdown(wait=True))
from prefect.task_runners import ConcurrentTaskRunner
import aria2p
try:
    from androguard.core.bytecodes.apk import APK
except ImportError:
    try:
        from androguard.core.apk import APK
    except ImportError:
        # 如果androguard不可用，创建一个模拟类用于测试
        class APK:
            def __init__(self, apk_path):
                self.apk_path = apk_path

            def get_android_manifest_xml(self):
                return "<manifest>test manifest</manifest>"

            def get_all_dex(self):
                return []

            def get_files(self):
                return ["lib/arm64-v8a/libtest.so", "lib/armeabi-v7a/libtest2.so"]

# 添加日志配置，降低androguard日志级别
import logging
# 将androguard相关库的日志级别设置为WARNING，减少INFO级别日志输出
logging.getLogger('androguard').setLevel(logging.WARNING)
logging.getLogger('dex').setLevel(logging.WARNING)

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, update

# 导入配置和模型
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tenyy.src.download_extract.config import (
    ARIA2_CONFIG,
    PROCESSING_CONFIG,
    RETRY_CONFIG,
    DATABASE_URL,
    STATUS_VALUES,
    get_temp_file_path
)

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../..'))
from tenyy.src.models.app_version import AppVersion


class APKDownloader:
    """APK下载器"""
    
    def __init__(self):
        try:
            self.logger = get_run_logger()
        except:
            # 如果不在Prefect上下文中，使用标准日志
            import logging
            self.logger = logging.getLogger(__name__)
            if not self.logger.handlers:
                handler = logging.StreamHandler()
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)

        try:
            # 解析RPC URL
            rpc_url = ARIA2_CONFIG["rpc_url"]  # http://localhost:6800/jsonrpc
            if "://" in rpc_url:
                host_port = rpc_url.split("://")[1].split("/")[0]  # localhost:6800
                host = host_port.split(":")[0]  # localhost
                port = int(host_port.split(":")[1])  # 6800
            else:
                host = "localhost"
                port = 6800

            client = aria2p.Client(
                host=f"http://{host}",
                port=port,
                secret=ARIA2_CONFIG["rpc_token"]
            )
            self.aria2 = aria2p.API(client)
        except Exception as e:
            self.logger.error(f"Aria2客户端初始化失败: {e}")
            self.aria2 = None
    
    async def download_apk(self, task: Dict[str, Any]) -> Tuple[bool, str, Optional[str], Dict[str, Any]]:
        """
        下载APK文件

        Args:
            task: 任务信息，包含id、download_url等

        Returns:
            (是否成功, 错误信息或成功信息, 本地文件路径, 下载信息)
        """
        app_version_id = task["id"]
        download_url = task["download_url"]
        
        # 生成临时文件路径
        temp_file_path = get_temp_file_path(app_version_id)
        
        # 确保下载目录存在
        os.makedirs(os.path.dirname(temp_file_path), exist_ok=True)
        
        try:
            if self.aria2 is None:
                error_msg = "Aria2客户端未初始化"
                self.logger.error(error_msg)
                return False, error_msg, None, {}

            self.logger.info(f"开始下载APK: {download_url}")

            # 预清理：删除可能存在的同名文件，避免Aria2自动重命名
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                    self.logger.info(f"预清理已存在的文件: {temp_file_path}")
                except Exception as e:
                    self.logger.warning(f"预清理文件失败: {e}")

            # 使用aria2下载，明确指定25个切片
            # 本地环境使用本地路径，容器环境使用容器路径
            container_download_dir = ARIA2_CONFIG["download_dir"]  # 使用配置中的下载目录

            download_options = {
                "dir": container_download_dir,  # 使用容器内的下载目录
                "out": os.path.basename(temp_file_path),
                "max-concurrent-downloads": "1",  # 单个任务专注下载
                "continue": "true",  # 支持断点续传
                "timeout": str(ARIA2_CONFIG["timeout"]),
                "retry-wait": str(ARIA2_CONFIG["retry_wait"]),
                "max-tries": str(ARIA2_CONFIG["max_retries"]),
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            }

            self.logger.info(f"使用aria2服务默认配置进行下载，目标: {os.path.basename(temp_file_path)}")

            download = self.aria2.add_uris([download_url], options=download_options)
            
            # 等待下载完成
            timeout = PROCESSING_CONFIG["download_timeout"]
            start_time = datetime.now()

            while True:
                download.update()

                if download.is_complete:
                    self.logger.info(f"Aria2下载完成，检查文件...")

                    # 动态获取下载目录路径（完全基于配置，无硬编码）
                    actual_files = []

                    # 使用本地下载目录配置（会根据环境自动选择正确路径）
                    search_download_dir = ARIA2_CONFIG["local_download_dir"]

                    self.logger.info(f"在下载目录中查找APK文件: {search_download_dir}")
                    self.logger.info(f"环境信息: DOCKER_ENV={os.getenv('DOCKER_ENV', 'false')}, TENYY_ENV={os.getenv('TENYY_ENV', 'unknown')}")

                    # 获取预期的文件名
                    expected_filename = os.path.basename(temp_file_path)
                    expected_file_path = os.path.join(search_download_dir, expected_filename)

                    if os.path.exists(expected_file_path):
                        actual_files.append(expected_file_path)
                        self.logger.info(f"找到预期的APK文件: {expected_file_path}")
                    else:
                        self.logger.warning(f"预期文件不存在: {expected_file_path}")

                    # 如果预期文件不存在，搜索所有APK文件
                    if not actual_files:

                        if os.path.exists(search_download_dir):
                            for filename in os.listdir(search_download_dir):
                                if filename.endswith('.apk'):
                                    full_path = os.path.join(search_download_dir, filename)
                                    if os.path.exists(full_path):
                                        actual_files.append(full_path)
                                        self.logger.info(f"找到APK文件: {full_path}")
                        else:
                            self.logger.error(f"下载目录不存在: {search_download_dir}")

                    if actual_files:
                        actual_file_path = actual_files[0]
                        self.logger.info(f"APK下载成功: {actual_file_path}")

                        # 如果文件名不匹配，重命名文件
                        final_file_path = actual_file_path
                        if actual_file_path != temp_file_path:
                            try:
                                os.rename(actual_file_path, temp_file_path)
                                self.logger.info(f"文件重命名: {os.path.basename(actual_file_path)} -> {os.path.basename(temp_file_path)}")
                                final_file_path = temp_file_path
                            except Exception as e:
                                self.logger.warning(f"文件重命名失败: {e}")
                                final_file_path = actual_file_path

                        # 收集下载信息（使用最终的文件路径）
                        file_size = os.path.getsize(final_file_path) if os.path.exists(final_file_path) else 0
                        download_time = (datetime.now() - start_time).total_seconds()
                        download_info = {
                            "file_size": file_size,
                            "download_time": download_time,
                            "download_speed": file_size / download_time if download_time > 0 else 0,
                            "file_path": final_file_path
                        }



                        return True, "下载成功", final_file_path, download_info
                    else:
                        error_msg = "下载完成但找不到文件"
                        self.logger.error(error_msg)
                        return False, error_msg, None, {}

                if download.has_failed:
                    error_msg = f"下载失败: {download.error_message}"
                    self.logger.error(error_msg)
                    return False, error_msg, None, {}

                # 检查超时
                if (datetime.now() - start_time).seconds > timeout:
                    self.aria2.remove([download], force=True)
                    error_msg = f"下载超时 ({timeout}秒)"
                    self.logger.error(error_msg)
                    return False, error_msg, None, {}
                
                await asyncio.sleep(1)  # 优化：增加轮询间隔到5秒，减少RPC调用频率，降低通信压力
                
        except Exception as e:
            error_msg = f"下载异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None, {}


class APKAnalyzer:
    """APK分析器"""

    def __init__(self):
        try:
            self.logger = get_run_logger()
        except:
            # 如果不在Prefect上下文中，使用标准日志
            import logging
            self.logger = logging.getLogger(__name__)
            if not self.logger.handlers:
                handler = logging.StreamHandler()
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.WARNING)  # 将日志级别从INFO改为WARNING，减少日志输出


    async def analyze_apk(self, apk_file_path: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        异步分析APK文件，使用线程池实现真正的并行处理
        
        Args:
            apk_file_path: APK文件路径
            
        Returns:
            (是否成功, 分析结果字典, 错误信息)
        """
        
        try:
            # 将详细日志改为debug级别，减少INFO级别的日志输出
            self.logger.debug(f"开始分析APK: {apk_file_path}")
            
            # 获取事件循环
            loop = asyncio.get_event_loop()
            
            # 使用全局共享的进程池执行CPU密集型的APK分析
            success, analysis_result, error_msg = await loop.run_in_executor(
                cpu_executor, 
                analyze_apk_sync, 
                apk_file_path
            )
            
            if success:
                # 将详细日志改为debug级别
                self.logger.debug(
                    f"APK分析成功: 包名数量={len(analysis_result['packages_class'])}, "
                    f"库文件数量={len(analysis_result['lib_files'])}"
                )
                return True, analysis_result, None
            else:
                self.logger.error(f"APK分析失败: {error_msg}")
                return False, {}, error_msg
                
        except Exception as e:
            error_msg = f"APK分析异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {}, error_msg


def analyze_apk_sync(apk_file_path: str) -> Tuple[bool, Dict[str, Any], Optional[str]]:
    """
    同步APK分析函数，用于在线程池中执行
    
    Args:
        apk_file_path: APK文件路径
        
    Returns:
        (是否成功, 分析结果字典, 错误信息)
    """
    try:
        # 创建APK对象
        apk = APK(apk_file_path)

        # 1. 提取AndroidManifest.xml内容
        try:
            android_manifest_element = apk.get_android_manifest_xml()
            if android_manifest_element is not None:
                import xml.etree.ElementTree as ET
                android_manifest = ET.tostring(android_manifest_element, encoding='unicode')
            else:
                android_manifest = ""
        except Exception as e:
            # 将print改为logger.debug或直接移除，减少输出
            # print(f"提取AndroidManifest.xml失败: {e}")
            android_manifest = ""

        # 2. 提取所有包名，去重并排序
        packages_class = []
        try:
            from androguard.core.dex import DEX

            # 获取所有DEX文件
            dex_files = list(apk.get_all_dex())

            for dex_data in dex_files:
                # 创建DEX对象
                if isinstance(dex_data, bytes):
                    dex = DEX(dex_data)
                else:
                    dex = dex_data

                # 获取所有类名
                for class_def in dex.get_classes():
                    class_name = class_def.get_name()
                    if class_name:
                        # 提取包名（去掉类名部分）
                        if class_name.startswith('L') and class_name.endswith(';'):
                            class_name = class_name[1:-1]  # 去掉L和;

                        package_parts = class_name.split('/')
                        if len(package_parts) > 1:
                            package_name = '/'.join(package_parts[:-1])
                            if package_name not in packages_class:
                                packages_class.append(package_name)

            packages_class.sort()

        except Exception as e:
            # 将print改为logger.debug或直接移除，减少输出
            # print(f"提取包名失败: {e}")
            packages_class = []

        # 3. 提取所有.so库文件名，去重并排序
        lib_files = []
        try:
            # 获取APK中的所有文件
            for file_name in apk.get_files():
                if file_name.endswith('.so'):
                    # 只保留文件名，不包含路径
                    lib_name = os.path.basename(file_name)
                    if lib_name not in lib_files:
                        lib_files.append(lib_name)

            lib_files.sort()

        except Exception as e:
            # 将print改为logger.debug或直接移除，减少输出
            # print(f"提取库文件失败: {e}")
            lib_files = []
        
        analysis_result = {
            "android_manifest": android_manifest,
            "packages_class": packages_class,
            "lib_files": lib_files
        }
        
        return True, analysis_result, None
        
    except Exception as e:
        error_msg = f"APK分析失败: {str(e)}"
        # 将print改为logger.debug或直接移除，减少输出
        # print(error_msg)
        return False, {}, error_msg


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        try:
            self.logger = get_run_logger()
        except:
            # 如果不在Prefect上下文中，使用标准日志
            import logging
            self.logger = logging.getLogger(__name__)
            if not self.logger.handlers:
                handler = logging.StreamHandler()
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)
        self.engine = create_async_engine(DATABASE_URL, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
    
    async def update_download_status(self, app_version_id: int, status: str, error_message: Optional[str] = None):
        """更新下载状态"""
        async with self.async_session() as session:
            try:
                stmt = update(AppVersion).where(
                    AppVersion.id == app_version_id
                ).values(
                    download_status=status,
                    last_attempt=datetime.utcnow(),
                    last_updated=datetime.utcnow(),
                    error_message=error_message
                )
                
                await session.execute(stmt)
                await session.commit()
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"更新下载状态失败: {e}")
    
    async def update_analysis_status(self, app_version_id: int, status: str, error_message: Optional[str] = None):
        """更新分析状态"""
        async with self.async_session() as session:
            try:
                stmt = update(AppVersion).where(
                    AppVersion.id == app_version_id
                ).values(
                    analyze_status=status,
                    analyze_error_message=error_message
                )
                
                await session.execute(stmt)
                await session.commit()
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"更新分析状态失败: {e}")
    
    async def update_analysis_results(self, app_version_id: int, analysis_result: Dict[str, Any]):
        """更新分析结果"""
        async with self.async_session() as session:
            try:
                stmt = update(AppVersion).where(
                    AppVersion.id == app_version_id
                ).values(
                    android_manifest=analysis_result["android_manifest"],
                    packages_class=analysis_result["packages_class"],
                    lib_files=analysis_result["lib_files"],
                    analyze_status=STATUS_VALUES["analyze_status"]["completed"],
                    analyzed_at=datetime.utcnow()
                )
                
                await session.execute(stmt)
                await session.commit()
                
                self.logger.info(f"成功更新分析结果: app_version_id={app_version_id}")
                
            except Exception as e:
                await session.rollback()
                self.logger.error(f"更新分析结果失败: {e}")
                raise
    
    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()


@task(
    name="Process Single APK",
    description="处理单个APK的完整生命周期",
    retries=RETRY_CONFIG["max_retries"],
    retry_delay_seconds=RETRY_CONFIG["retry_delay"]
)
async def process_single_apk(task: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理单个APK的完整生命周期

    Args:
        task: 任务信息，包含id、download_url等

    Returns:
        处理结果
    """
    logger = get_run_logger()
    app_version_id = task["id"]
    download_url = task["download_url"]

    logger.info(f"开始处理APK任务: {app_version_id}")

    # 初始化组件
    downloader = APKDownloader()
    analyzer = APKAnalyzer()
    db_manager = DatabaseManager()

    temp_file_path = None

    # 初始化变量
    download_info = {}
    temp_file_path = None

    try:
        # 1. 任务已在get_pending_tasks中标记为processing，现在更新为downloading
        await db_manager.update_download_status(
            app_version_id,
            STATUS_VALUES["download_status"]["downloading"]
        )

        # 2. 下载APK文件
        success, message, temp_file_path, download_info = await downloader.download_apk(task)

        if not success:
            await db_manager.update_download_status(
                app_version_id,
                STATUS_VALUES["download_status"]["failed"],
                message
            )
            raise Exception(f"下载失败: {message}")

        # 3. 更新下载状态为downloaded，分析状态为processing
        await db_manager.update_download_status(
            app_version_id,
            STATUS_VALUES["download_status"]["downloaded"]
        )
        await db_manager.update_analysis_status(
            app_version_id,
            STATUS_VALUES["analyze_status"]["processing"]
        )

        # 4. 分析APK文件
        success, analysis_result, error_message = await analyzer.analyze_apk(temp_file_path)

        if not success:
            await db_manager.update_analysis_status(
                app_version_id,
                STATUS_VALUES["analyze_status"]["failed"],
                error_message
            )
            raise Exception(f"分析失败: {error_message}")

        # 5. 更新分析结果到数据库
        await db_manager.update_analysis_results(app_version_id, analysis_result)

        logger.info(f"✅ APK任务处理成功: {app_version_id}")

        return {
            "status": "success",
            "app_version_id": app_version_id,
            "download_url": download_url,
            "download_info": download_info,  # 添加下载信息
            "analysis_summary": {
                "packages_count": len(analysis_result["packages_class"]),
                "lib_files_count": len(analysis_result["lib_files"]),
                "manifest_size": len(analysis_result["android_manifest"])
            }
        }

    except Exception as e:
        logger.error(f"❌ APK任务处理失败: {app_version_id}, 错误: {e}")

        # 更新错误状态，但保留重试机会
        # 如果是临时错误（网络、服务器等），重置为NULL以便重试
        # 如果是永久错误（URL无效等），标记为failed
        error_str = str(e).lower()
        if any(keyword in error_str for keyword in ['timeout', 'connection', 'network', 'temporary']):
            # 检查重试次数，如果超过3次则标记为失败
            async with db_manager.async_session() as session:
                try:
                    from tenyy.src.models.app_version import AppVersion
                    stmt = select(AppVersion).where(AppVersion.id == app_version_id)
                    result = await session.execute(stmt)
                    app_version = result.scalar_one_or_none()
                    
                    current_retry_count = app_version.retry_count or 0
                    if current_retry_count >= 3:
                        # 重试次数已达上限，标记为失败
                        await db_manager.update_download_status(
                            app_version_id,
                            STATUS_VALUES["download_status"]["failed"],
                            f"已重试{current_retry_count}次仍失败: {str(e)}"
                        )
                        await db_manager.update_analysis_status(
                            app_version_id,
                            STATUS_VALUES["analyze_status"]["failed"],
                            f"已重试{current_retry_count}次仍失败: {str(e)}"
                        )
                        logger.info(f"任务 {app_version_id} 已重试 {current_retry_count} 次，达到上限，标记为失败")
                    else:
                        # 增加重试次数并重置状态以便重试
                        await db_manager.update_download_status(
                            app_version_id, 
                            None, 
                            f"临时错误，将重试({current_retry_count + 1}/3): {str(e)}"
                        )
                        # 更新重试次数
                        update_stmt = update(AppVersion).where(
                            AppVersion.id == app_version_id
                        ).values(
                            retry_count=current_retry_count + 1
                        )
                        await session.execute(update_stmt)
                        await session.commit()
                        logger.info(f"任务 {app_version_id} 遇到临时错误，已重试 {current_retry_count + 1} 次，重置状态以便重试")
                except Exception as db_error:
                    logger.error(f"更新重试次数失败: {db_error}")
                    await session.rollback()
        else:
            # 永久错误，标记为失败
            await db_manager.update_download_status(
                app_version_id,
                STATUS_VALUES["download_status"]["failed"],
                str(e)
            )
            await db_manager.update_analysis_status(
                app_version_id,
                STATUS_VALUES["analyze_status"]["failed"],
                str(e)
            )

        return {
            "status": "failed",
            "app_version_id": app_version_id,
            "download_url": download_url,
            "download_info": download_info,  # 使用实际的下载信息（可能为空）
            "error": str(e)
        }

    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

        # 关闭数据库连接
        await db_manager.close()


@flow(
    name="APK Processing Pipeline",
    description="APK处理生产线 - 批量并行处理APK下载和分析",
    task_runner=ConcurrentTaskRunner(),
    flow_run_name="apk-batch-{flow_run.scheduled_start_time}"
)
async def apk_processing_pipeline(tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    APK处理生产线主流程

    接收一批任务并以最大并发度并行处理。

    Args:
        tasks: 任务列表，每个任务包含id、download_url等信息

    Returns:
        批量处理结果统计
    """
    logger = get_run_logger()

    if not tasks:
        logger.warning("没有收到任何任务")
        return {
            "status": "no_tasks",
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0
        }

    logger.info(f"🚀 开始批量处理APK任务，数量: {len(tasks)}")

    try:
        # 使用asyncio.gather实现高并发处理
        results = await asyncio.gather(
            *[process_single_apk(task) for task in tasks],
            return_exceptions=True
        )

        # 统计结果
        successful_tasks = 0
        failed_tasks = 0
        errors = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_tasks += 1
                errors.append(f"Task {tasks[i]['id']}: {str(result)}")
                logger.error(f"任务异常: {tasks[i]['id']} - {result}")
            elif result.get("status") == "success":
                successful_tasks += 1
            else:
                failed_tasks += 1
                errors.append(f"Task {result['app_version_id']}: {result.get('error', 'Unknown error')}")

        logger.info(
            f"✅ 批量处理完成: 总数={len(tasks)}, "
            f"成功={successful_tasks}, 失败={failed_tasks}"
        )

        return {
            "status": "completed",
            "total_tasks": len(tasks),
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": successful_tasks / len(tasks) * 100,
            "errors": errors[:10]  # 只返回前10个错误
        }

    except Exception as e:
        logger.error(f"❌ 批量处理异常: {e}")
        return {
            "status": "error",
            "total_tasks": len(tasks),
            "successful_tasks": 0,
            "failed_tasks": len(tasks),
            "error": str(e)
        }


if __name__ == "__main__":
    # 用于本地测试
    import asyncio

    # 测试单个任务
    test_task = {
        "id": 1,
        "download_url": "https://example.com/test.apk",
        "store_app_id": 1,
        "version": "1.0.0",
        "apk_hash": "test_hash"
    }

    asyncio.run(process_single_apk(test_task))
