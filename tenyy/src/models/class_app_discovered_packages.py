# coding: utf-8
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Index, Text
from tenyy.src.models.base import Base


class class_AppDiscoveredPackage(Base):
    """应用发现包表，存储从APK中提取并提纯后的候选包名"""
    __tablename__ = 'class_app_discovered_packages'
    __table_args__ = (
        Index('idx_app_discovered_packages_app_version_id', 'app_version_id'),
        Index('idx_app_discovered_packages_package_name', 'package_name'),
        Index('idx_app_discovered_packages_last_checked', 'last_checked')
    )

    id = Column(BigInteger, primary_key=True)  # 自增ID
    app_version_id = Column(Integer, nullable=False)  # 关联的应用版本ID
    package_name = Column(Text, nullable=False)  # 提纯后的候选包名
    type = Column(String(50), nullable=True)  # 包类型（如SDK、组件等）
    # 类型定义参考LibChecker规则系统:
    # 1. DEX Packages (类型5) - DEX包名，用于识别应用中使用的Java/Kotlin库和SDK
    # 2. Native Libraries (类型0) - 原生库(.so文件)，用于识别应用中包含的原生库文件
    # 3. Components - 组件类型:
    #    - Service (类型1) - 服务组件
    #    - Activity (类型2) - 活动组件
    #    - Receiver (类型3) - 广播接收器组件
    #    - Provider (类型4) - 内容提供者组件
    # 4. Static Libraries (类型6) - 静态库
    # 5. Permissions (类型7) - 权限
    # 6. Metadata (类型8) - 元数据
    # 7. Intent Actions (类型9) - Intent动作
    last_checked = Column(DateTime, default=None)  # 最后检查时间

    def __repr__(self):
        return f"<class_AppDiscoveredPackage(id={self.id}, app_version_id={self.app_version_id}, package_name='{self.package_name}')>"
