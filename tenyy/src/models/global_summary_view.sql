-- 创建一个视图，用于显示每个应用版本发现的 SDK 信息
-- 这个视图将连接多个表，提供应用名称、版本号和发现的 SDK 名称等信息

CREATE OR REPLACE VIEW app_version_sdk_view AS
SELECT 
    a.id AS app_id,
    a.name AS app_name,
    sa.store_type AS store_type,
    av.version AS app_version,
    av.id AS app_version_id,
    -- 已知 SDK 的名称来自 SDK 知识库
    CASE 
        WHEN cas.is_potential = false THEN csk.sdk_name
        ELSE cas.sdk_package_prefix
    END AS sdk_name,
    -- SDK 包前缀
    cas.sdk_package_prefix AS sdk_package_prefix,
    -- 是否为潜在 SDK
    cas.is_potential AS is_potential,
    -- SDK 类型描述
    CASE cas.type
        WHEN '0' THEN 'Native库'
        WHEN '1' THEN 'Service'
        WHEN '2' THEN 'Activity'
        WHEN '3' THEN 'Receiver'
        WHEN '4' THEN 'Provider'
        WHEN '5' THEN 'DEX包名'
        WHEN '6' THEN '静态库'
        WHEN '7' THEN '权限'
        WHEN '8' THEN '元数据'
        WHEN '9' THEN 'Intent动作'
        ELSE '未知类型'
    END AS sdk_type,
    -- 匹配类型
    cas.match_type AS match_type,
    -- 信号数量（仅对潜在 SDK 有意义）
    cas.signal_count AS signal_count
FROM class_app_version_sdks cas
-- 连接应用版本表
JOIN app_version av ON cas.app_version_id = av.id
-- 连接商店应用表
JOIN store_app sa ON av.store_app_id = sa.id
-- 连接应用主表
JOIN app a ON sa.app_id = a.id
-- 左连接 SDK 知识库表（潜在 SDK 可能没有关联的知识库记录）
LEFT JOIN class_sdk_knowledge_base csk ON cas.sdk_knowledge_base_id = csk.id;

-- 添加注释
COMMENT ON VIEW app_version_sdk_view IS '应用版本SDK信息视图，显示每个应用版本发现的SDK名称';

-- 示例查询：
-- SELECT * FROM app_version_sdk_view WHERE app_id = 'com.example.app' ORDER BY app_version_id, is_potential;