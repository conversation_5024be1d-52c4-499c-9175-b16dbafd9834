# coding: utf-8
from sqlalchemy import Column, String, Text, DateTime, Index, Integer, Boolean
from sqlalchemy.dialects.postgresql import INTEGER
from tenyy.src.models.base import Base


class class_SDKKnowledgeBase(Base):
    """SDK知识库表，存储所有已识别的SDK信息"""
    __tablename__ = 'class_sdk_knowledge_base'
    __table_args__ = (
        Index('idx_sdk_knowledge_base_package_prefix', 'package_prefix'),
    )

    id = Column(INTEGER, primary_key=True, autoincrement=True)  # 主键ID
    package_prefix = Column(String(255), unique=True, nullable=False)  # SDK的关键包名前缀
    sdk_name = Column(String(255), nullable=True)  # SDK的正式名称
    company_name = Column(String(255), nullable=True)  # 公司名称
    version = Column(String(100), nullable=True)  # SDK版本
    category = Column(String(100), nullable=True)  # SDK主分类
    subcategory = Column(String(100), nullable=True)  # SDK子分类
    description = Column(Text, nullable=True)  # SDK的功能描述
    brief_message = Column(Text, nullable=True)  # 简要信息
    official_web = Column(Text, nullable=True)  # 官方网站URL
    sdk_document_url = Column(Text, nullable=True)  # SDK文档URL
    privacy_policy_url = Column(Text, nullable=True)  # 隐私政策URL
    compliance_instructions_url = Column(Text, nullable=True)  # 合规说明URL
    status = Column(String(50), nullable=True)  # 记录状态: identified, pending_analysis, error
    last_checked_at = Column(DateTime, nullable=True)  # 上次检查或更新的时间
    
    # LibChecker相关字段
    detection_type = Column(Integer, nullable=True)  # 检测类型 (0=NATIVE, 1=SERVICE, 2=ACTIVITY等)
    is_regex_rule = Column(Boolean, nullable=True)   # 是否为正则表达式规则
    regex_pattern = Column(Text, nullable=True)      # 正则表达式模式
    tags = Column(Text, nullable=True)               # 标签字段，存储JSON格式的标签数组

    def __repr__(self):
        return f"<class_SDKKnowledgeBase(package_prefix='{self.package_prefix}', sdk_name='{self.sdk_name}', status='{self.status}')>"