# coding: utf-8
from sqlalchemy import Column, String, Text, DateTime, Index, Integer, Boolean, BigInteger
from tenyy.src.models.base import Base


class class_AppVersionSDK(Base):
    """应用版本与SDK关联表，记录哪个应用版本包含了哪个SDK"""
    __tablename__ = 'class_app_version_sdks'
    __table_args__ = (
        Index('idx_app_version_sdks_app_version_id', 'app_version_id'),
        Index('idx_app_version_sdks_sdk_package_prefix', 'sdk_package_prefix'),
        Index('idx_app_version_sdks_sdk_knowledge_base_id', 'sdk_knowledge_base_id'),
        Index('idx_app_version_sdks_is_potential', 'is_potential'),
        Index('idx_app_version_sdks_match_type', 'match_type')
    )

    # 数据库表的主键是(app_version_id, sdk_package_prefix)的组合
    app_version_id = Column(Integer, primary_key=True, nullable=False)  # 关联到具体的APK版本
    sdk_package_prefix = Column(String(255), primary_key=True, nullable=False)  # SDK包名前缀（对于已知SDK来自class_sdk_knowledge_base，对于潜在SDK为识别出的包名前缀）
    sdk_knowledge_base_id = Column(Integer, nullable=True)  # 关联到class_sdk_knowledge_base的ID（对于潜在SDK为空）
    match_type = Column(String(50), nullable=True)  # 匹配类型: prefix(前缀匹配), regex(正则表达式匹配), potential(潜在SDK)
    is_potential = Column(Boolean, nullable=False, default=False)  # 是否为潜在SDK
    signal_count = Column(Integer, nullable=True)  # 信号数量（用于潜在SDK）
    child_packages = Column(Text, nullable=True)  # 子包列表（用于潜在SDK，JSON格式）
    type = Column(String(50), nullable=True)  # 包类型（参考LibChecker规则系统: 0-Native库, 1-Service, 2-Activity, 3-Receiver, 4-Provider, 5-DEX包名, 6-静态库, 7-权限, 8-元数据, 9-Intent动作）
    created_at = Column(DateTime, nullable=True)  # 创建时间
    updated_at = Column(DateTime, nullable=True)  # 更新时间

    def __repr__(self):
        return f"<class_AppVersionSDK(app_version_id={self.app_version_id}, sdk_package_prefix='{self.sdk_package_prefix}', is_potential={self.is_potential}')>"