# -*- coding: utf-8 -*-
"""
Tenyy 统一配置系统 - 本地容器环境配置

本地容器化环境特定的配置，使用Docker服务名和容器网络。
适用于本地使用docker-compose运行的完全容器化测试环境。
"""

import os
from .base import *  # 导入所有基础配置

# ============================================================================
# 🌍 环境标识
# ============================================================================

ENVIRONMENT = "prefect_local_container"
ENVIRONMENT_NAME = "本地容器环境"

# ============================================================================
# 🗄️ 数据库配置 - 本地容器
# ============================================================================

DATABASE_CONFIG = {
    "host": os.getenv("POSTGRES_HOST", "app-db"),  # Docker服务名
    "port": int(os.getenv("POSTGRES_PORT", "5432")),  # 容器内端口
    "database": os.getenv("POSTGRES_DB", "tenyy_app"),
    "username": os.getenv("POSTGRES_USER", "admin"),
    "password": os.getenv("POSTGRES_PASSWORD", "zhangdi168"),
}

# 统一数据库环境变量配置 (仅使用POSTGRES_*格式)
DB_CONFIG = {
    "POSTGRES_HOST": DATABASE_CONFIG["host"],
    "POSTGRES_PORT": str(DATABASE_CONFIG["port"]),
    "POSTGRES_DB": DATABASE_CONFIG["database"],
    "POSTGRES_USER": DATABASE_CONFIG["username"],
    "POSTGRES_PASSWORD": DATABASE_CONFIG["password"],
}

# ============================================================================
# 🔄 Prefect配置 - 本地容器
# ============================================================================

PREFECT_CONFIG = {
    "api_url": os.getenv("PREFECT_API_URL", "http://prefect-server:4200/api"),  # 容器内服务名
    "ui_url": "http://localhost:4200",  # 宿主机访问地址
    "work_pool_name": WORK_POOL_NAME,
    "logging_level": "INFO",
}

# ============================================================================
# 📥 Aria2配置 - 本地容器
# ============================================================================

ARIA2_CONFIG = {
    "rpc_url": os.getenv("ARIA2_RPC_URL", "http://**************:6800/jsonrpc"),  # 容器访问宿主机Aria2
    "rpc_token": os.getenv("ARIA2_RPC_TOKEN", "zhangdi168"),  # 从环境变量获取token
    "download_dir": os.getenv("DOWNLOAD_DIR", "/downloads"),
    **ARIA2_COMMON_CONFIG  # 继承通用配置
}

# ============================================================================
# 🐳 Docker配置 - 本地容器
# ============================================================================

DOCKER_REGISTRY = {
    "host": "localhost",
    "port": "5000",
    "url": "localhost:5000",  # 容器通过host网络访问本地registry
}

DOCKER_IMAGE_CONFIG = {
    "registry": DOCKER_REGISTRY["url"],
    "image_name": DOCKER_CONFIG["image_name"],
    "tag": DOCKER_CONFIG["image_tag"],
    "full_image": f"{DOCKER_REGISTRY['url']}/{DOCKER_CONFIG['image_name']}:{DOCKER_CONFIG['image_tag']}",
}

# ============================================================================
# 🌐 网络配置 - 本地容器
# ============================================================================

NETWORK_CONFIG = {
    "docker_network": "test_stack_tenyy-net",
    "network_mode": "test_stack_tenyy-net",  # 使用自定义Docker网络
}

# ============================================================================
# 📁 存储路径配置 - 本地容器
# ============================================================================

LOCAL_STORAGE_CONFIG = {
    **STORAGE_CONFIG,  # 继承基础存储配置
    "log_file_path": "/tmp/tenyy_container.log",
    "host_download_dir": "/Users/<USER>/dev/local_download/aria2-downloads",  # 宿主机下载目录
}

# ============================================================================
# 🔧 Prefect部署配置 - 本地容器
# ============================================================================

PREFECT_DEPLOYMENT_CONFIG = {
    "work_pool": {
        "name": WORK_POOL_NAME,
        "type": "docker",
    },
    "job_variables": {
        "image": DOCKER_IMAGE_CONFIG["full_image"],
        "networks": [NETWORK_CONFIG["docker_network"]],  # 使用Docker网络
        "env": {
            "PREFECT_LOGGING_LEVEL": "INFO",
            "PYTHONPATH": DOCKER_CONFIG["pythonpath"],
            "DOCKER_ENV": "true",  # 标识容器环境
            **DB_CONFIG,  # 数据库环境变量
            **{
                "ARIA2_RPC_URL": ARIA2_CONFIG["rpc_url"],
                "ARIA2_RPC_TOKEN": ARIA2_CONFIG["rpc_token"],
                "DOWNLOAD_DIR": ARIA2_CONFIG["download_dir"],
            }
        },
        "volumes": [
            # 直接挂载宿主机目录，与Aria2容器共享文件
            "/mnt/ssd/tenyy/downloads:/downloads",
            # 临时目录挂载
            f"/tmp/apk_downloads:{LOCAL_STORAGE_CONFIG['temp_apk_dir']}",
        ],
        **RESOURCE_LIMITS["crawler"],  # 使用爬虫资源限制作为默认
    }
}

# ============================================================================
# 🕷️ 爬虫特定配置 - 本地容器
# ============================================================================

CRAWLER_CONFIG = {
    "config_dir": "tenyy/src/crawler/configs",
    "yinyongbao_config": "tenyy/src/crawler/configs/yinyongbao.yaml",
    "huawei_config": "tenyy/src/crawler/configs/huawei.yaml",
    "categories_dir": "tenyy/src/crawler",
}

# ============================================================================
# 📦 Download-Extract特定配置 - 本地容器
# ============================================================================

DOWNLOAD_EXTRACT_CONFIG = {
    "scheduler_deployment": {
        "name": "adaptive-scheduler-container",
        "description": "自适应调度器 - 本地容器版",
        "work_queue": "scheduler-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["scheduler"],
        }
    },
    "processor_deployment": {
        "name": "apk-processor-container",
        "description": "APK处理器 - 本地容器版",
        "work_queue": "processor-queue",
        "job_variables": {
            **PREFECT_DEPLOYMENT_CONFIG["job_variables"],
            **RESOURCE_LIMITS["download_extract"]["processor"],
        }
    }
}

# ============================================================================
# 🔧 环境验证函数
# ============================================================================

def validate_container_config() -> bool:
    """验证本地容器环境配置"""
    required_configs = [
        DATABASE_CONFIG,
        PREFECT_CONFIG,
        ARIA2_CONFIG,
        DOCKER_REGISTRY,
        NETWORK_CONFIG,
    ]
    
    # 检查必要的配置项
    for config in required_configs:
        if not config:
            return False
    
    # 检查容器特定配置
    if DATABASE_CONFIG["host"] == "localhost":
        print("警告: 容器环境使用localhost，应该使用Docker服务名")
        # 不返回False，只是警告
    
    if DATABASE_CONFIG["port"] != 5432:
        print("警告: 容器环境数据库端口不是5432")
    
    return True

# 在模块加载时验证配置
if not validate_container_config():
    raise ValueError("本地容器环境配置验证失败，请检查配置")

# ============================================================================
# 🚀 Prefect部署配置 - 本地容器环境
# ============================================================================

# 本地容器环境Prefect部署配置
HOST_DOWNLOAD_DIR = "/mnt/ssd/tenyy/downloads"
DOCKER_IMAGE = "localhost:5000/tenyy-unified:latest"
DOCKER_NETWORK = "test_stack_tenyy-net"
PREFECT_API_URL = "http://localhost:4200/api"

# ============================================================================
# 🔧 配置导出
# ============================================================================

# 导出给其他模块使用的配置字典
CONFIG = {
    "environment": ENVIRONMENT,
    "database": DATABASE_CONFIG,
    "prefect": PREFECT_CONFIG,
    "aria2": ARIA2_CONFIG,
    "docker": DOCKER_IMAGE_CONFIG,
    "network": NETWORK_CONFIG,
    "storage": LOCAL_STORAGE_CONFIG,
    "crawler": CRAWLER_CONFIG,
    "download_extract": DOWNLOAD_EXTRACT_CONFIG,
}
