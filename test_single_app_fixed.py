#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个应用版本的SDK匹配流程，验证修复后的功能
"""
import os
import sys
import logging

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

from tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow import SDKMatcher


def main():
    """主函数"""
    logger.info("开始测试单个应用版本的SDK匹配流程")
    
    try:
        # 创建SDK匹配器实例
        matcher = SDKMatcher()
        
        # 测试处理单个应用版本（ID为19678，有7106个包）
        app_version_id = 19678
        logger.info(f"开始处理应用版本 {app_version_id}")
        matcher.process_app_version(app_version_id)
        logger.info(f"应用版本 {app_version_id} 处理完成")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        sys.exit(1)
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()