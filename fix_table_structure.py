from tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow import SDKMatcher
from sqlalchemy import text

matcher = SDKMatcher()

with matcher.engine.connect() as conn:
    trans = conn.begin()
    try:
        conn.execute(text('CREATE SEQUENCE IF NOT EXISTS class_app_version_sdks_id_seq'))
        conn.execute(text("ALTER TABLE class_app_version_sdks ALTER COLUMN id SET DEFAULT nextval('class_app_version_sdks_id_seq')"))
        conn.execute(text('ALTER SEQUENCE class_app_version_sdks_id_seq OWNED BY class_app_version_sdks.id'))
        trans.commit()
        print('Successfully updated table structure')
    except Exception as e:
        trans.rollback()
        print(f'Error: {e}')