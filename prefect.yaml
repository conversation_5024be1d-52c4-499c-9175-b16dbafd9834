build: null
deployments:
- description: 腾讯应用宝应用爬虫 - 爬取应用信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: yinyongbao-app-crawler
  parameters:
    app_type: app
    max_categories: -1
    max_pages: -1
    store_type: yinyongbao
  schedule: null
  tags:
  - crawler
  - yinyongbao
  - app
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: 2Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 腾讯应用宝游戏爬虫 - 爬取游戏信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: yinyongbao-game-crawler
  parameters:
    app_type: game
    max_categories: -1
    max_pages: -1
    store_type: yinyongbao
  schedule: null
  tags:
  - crawler
  - yinyongbao
  - game
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: 2Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 华为应用市场爬虫 - 爬取应用信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: huawei-app-crawler
  parameters:
    max_categories: -1
    max_pages: -1
    store_type: huawei
  schedule: null
  tags:
  - crawler
  - huawei
  - app
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: 2Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 腾讯应用宝推荐爬虫 - 爬取推荐应用信息
  entrypoint: tenyy/src/crawler/prefect_flow/prefect_flow.py:universal_crawler
  name: yinyongbao-similar-crawler
  parameters:
    max_categories: 0
    max_pages: -1
    store_type: yinyongbao_similar
  schedule: null
  tags:
  - crawler
  - yinyongbao
  - similar
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      env:
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: 2Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
    name: tenyy-unified-pool
    work_queue_name: crawler-queue
- description: 批量APK下载和分析处理器 - 处理待下载的APK文件
  entrypoint: tenyy/src/download_extract/flows/batch_processor.py:batch_apk_processing_flow
  name: batch-apk-processing
  parameters:
    batch_size: 10
    max_concurrent: 3
  schedule: null
  tags:
  - download
  - extract
  - apk
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '1.0'
      env:
        ARIA2_RPC_TOKEN: zhangdi168
        ARIA2_RPC_URL: http://**************:6800/jsonrpc
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        DOCKER_ENV: 'true'
        DOWNLOAD_DIR: /downloads
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: '0'
      memory_request: 2Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
      volumes:
      - /mnt/ssd/tenyy/downloads:/downloads
    name: tenyy-unified-pool
    work_queue_name: download-queue
- description: 循环批量APK下载和分析处理器 - 持续处理直到没有待处理任务
  entrypoint: tenyy/src/download_extract/flows/batch_processor.py:continuous_batch_apk_processing_flow
  name: continuous-batch-apk-processing
  parameters:
    batch_size: 50
    cycle_delay: 30
    max_concurrent: 10
    max_cycles: 100
  schedule: null
  tags:
  - download
  - extract
  - apk
  - continuous
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '2.0'
      env:
        ARIA2_RPC_TOKEN: zhangdi168
        ARIA2_RPC_URL: http://host.docker.internal:6800/jsonrpc
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        DOCKER_ENV: 'true'
        DOWNLOAD_DIR: /downloads
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: '0'
      memory_request: 4Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
      volumes:
      - /mnt/ssd/tenyy/downloads:/downloads
    name: tenyy-unified-pool
    work_queue_name: download-queue
- description: 🚀 真正的流水线APK处理器 - 任务队列+工作池，无批次等待
  entrypoint: tenyy/src/download_extract/flows/batch_processor.py:continuous_pipeline_apk_processing_flow
  name: pipeline-apk-processing
  parameters:
    check_interval: 5
    max_concurrent: 10
    max_idle_time: 60
    task_fetch_size: 20
  schedule: null
  tags:
  - download
  - extract
  - apk
  - pipeline
  - continuous
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '0'
      cpu_request: '2.0'
      env:
        ARIA2_RPC_TOKEN: zhangdi168
        ARIA2_RPC_URL: http://**************:6800/jsonrpc
        DB_HOST: app-db
        DB_NAME: tenyy_app
        DB_PASSWORD: zhangdi168
        DB_PORT: '5432'
        DB_USER: admin
        DOCKER_ENV: 'true'
        DOWNLOAD_DIR: /downloads
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: '0'
      memory_request: 4Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Always
      volumes:
      - /mnt/ssd/tenyy/downloads:/downloads
    name: tenyy-unified-pool
    work_queue_name: pipeline-queue
- description: 数据库备份任务 - 执行数据库备份操作
  entrypoint: tenyy/src/backup_plan/database_backup_flow.py:database_backup
  name: database-backup
  parameters:
    backup_dir: /mnt/ssd/tenyy/db_backups
    backup_type: base
  schedule: null
  tags:
  - database
  - backup
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      cpu_request: '0.5'
      env:
        BACKUP_DIR: /mnt/ssd/tenyy/db_backups
        DOCKER_ENV: 'true'
        POSTGRES_DB: tenyy_app
        POSTGRES_HOST: app-db
        POSTGRES_PASSWORD: zhangdi168
        POSTGRES_PORT: '5432'
        POSTGRES_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: 2Gi
      memory_request: 1Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Never
      volumes:
      - /mnt/ssd/tenyy/db_backups:/mnt/ssd/tenyy/db_backups
    name: tenyy-unified-pool
    work_queue_name: backup-queue
- description: 数据库恢复任务 - 从备份文件恢复数据库
  entrypoint: tenyy/src/backup_plan/database_backup_flow.py:database_restore
  name: database-restore
  parameters:
    backup_file: /mnt/ssd/tenyy/db_backups/full/base_20250731_120000.tar.gz
  schedule: null
  tags:
  - database
  - restore
  - prefect_local_container
  version: 1.0.0
  work_pool:
    job_variables:
      cpu_limit: '1.0'
      cpu_request: '0.5'
      env:
        DOCKER_ENV: 'true'
        POSTGRES_DB: tenyy_app
        POSTGRES_HOST: app-db
        POSTGRES_PASSWORD: zhangdi168
        POSTGRES_PORT: '5432'
        POSTGRES_USER: admin
        PREFECT_API_URL: http://prefect-server:4200/api
        PREFECT_LOGGING_LEVEL: INFO
        PYTHONPATH: /app
      image: localhost:5000/tenyy-unified:latest
      image_pull_policy: Always
      memory_limit: 2Gi
      memory_request: 1Gi
      networks:
      - test_stack_tenyy-net
      restart_policy: Never
      volumes:
      - /mnt/ssd/tenyy/db_backups:/mnt/ssd/tenyy/db_backups
    name: tenyy-unified-pool
    work_queue_name: backup-queue
name: tenyy-unified
prefect-version: 3.0.0
pull:
- prefect.deployments.steps.set_working_directory:
    directory: /app
push: null
