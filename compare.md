# SDK特征精确匹配与潜在新特征发现流程

## 概述

本文档详细描述了如何使用[class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)特征表精确匹配[class_app_discovered_packages](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_discovered_packages.py#L0-L0)中的DEX包名信号，并通过层级统计分析发现潜在的新SDK特征。

[class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)表不仅包含手动维护的SDK特征，还整合了来自LibChecker的规则数据，这些数据覆盖了Android应用中多种类型的组件和库。

处理后的结果将存储在[class_app_version_sdks](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_version_sdks.py#L0-L0)表中，该表记录了应用版本与SDK的关联关系，包括已识别的SDK和潜在的新SDK特征。

## 数据表结构

### class_sdk_knowledge_base (特征表)
存储已知SDK的特征信息：
- `id`: 主键ID
- `package_prefix`: SDK的关键包名前缀（用于匹配）
- `sdk_name`: SDK的正式名称
- `company_name`: 公司名称
- `detection_type`: 检测类型，对应LibChecker规则类型:
  - 0: Native Libraries (.so文件)
  - 1: Service组件
  - 2: Activity组件
  - 3: Receiver组件
  - 4: Provider组件
  - 5: DEX Packages (DEX包名)
  - 6: Static Libraries (静态库)
  - 7: Permissions (权限)
  - 8: Metadata (元数据)
  - 9: Intent Actions (Intent动作)
- `is_regex_rule`: 是否为正则表达式规则
- `regex_pattern`: 正则表达式模式
- `tags`: 标签字段，存储JSON格式的标签数组

### class_app_discovered_packages (信号表)
存储从应用中提取的信号：
- `id`: 主键ID
- `app_version_id`: 关联的应用版本ID
- `package_name`: 提纯后的候选包名（DEX包名使用点号分隔格式）
- `type`: 包类型（5表示DEX Packages）
- `last_checked`: 最后检查时间

### class_app_version_sdks (结果表)
存储应用版本与SDK的关联关系：
- `id`: 主键ID
- `app_version_id`: 关联的应用版本ID
- `sdk_package_prefix`: SDK包名前缀（对于已知SDK来自[class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)的package_prefix，对于潜在SDK为识别出的包名前缀）
- `sdk_knowledge_base_id`: 关联到[class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)的ID（对于潜在SDK为空）
- `match_type`: 匹配类型（prefix表示前缀匹配，regex表示正则表达式匹配，potential表示潜在SDK）
- `is_potential`: 是否为潜在SDK（true表示潜在SDK，false表示已知SDK）
- `signal_count`: 信号数量（用于潜在SDK，表示该潜在SDK下发现的信号数量）
- `child_packages`: 子包列表（用于潜在SDK，JSON格式存储发现的子包列表）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 9种信号类型详解

LibChecker定义了9种主要的信号类型，其中只有部分以包名形式存在：

### 1. DEX Packages (类型5)
- **格式**: Java/Kotlin包名，使用点号(.)分隔
- **示例**: `com.google.gson`, `com.alipay.sdk.util`
- **用途**: 识别应用中使用的Java/Kotlin库和SDK

### 2. Native Libraries (类型0)
- **格式**: .so文件名
- **示例**: `libtensorflow_inference.so`, `libmmkv.so`
- **用途**: 识别应用中包含的原生库文件

### 3. 组件类型 (类型1-4)
- **格式**: Android组件完整类名，使用点号(.)分隔
- **示例**: 
  - Service: `com.google.firebase.components.ComponentDiscoveryService`
  - Activity: `com.tencent.smtt.utils.FileProvider`
  - Receiver: `com.alibaba.sdk.android.push.MiPushReceiver`
  - Provider: `com.facebook.internal.content.Provider`
- **用途**: 识别应用中声明的各种Android组件

### 4. Static Libraries (类型6)
- **格式**: 静态库标识符
- **示例**: `libwebviewchromium`, `libandroidfw`
- **用途**: 识别应用链接的静态库

### 5. Permissions (类型7)
- **格式**: 权限名称
- **示例**: `android.permission.INTERNET`, `com.google.android.c2dm.permission.RECEIVE`
- **用途**: 识别应用请求的权限

### 6. Metadata (类型8)
- **格式**: 元数据键名
- **示例**: `com.google.android.gms.version`, `android.max_aspect`
- **用途**: 识别应用定义的元数据

### 7. Intent Actions (类型9)
- **格式**: Intent动作名称
- **示例**: `com.google.android.c2dm.intent.RECEIVE`, `android.intent.action.BOOT_COMPLETED`
- **用途**: 识别应用使用的Intent动作

**总结**: 9种信号类型中，有6种以包名或类名形式存在（类型1-6），2种以标识符形式存在（类型0和7），1种以键值形式存在（类型8），1种以动作名称形式存在（类型9）。除了Native Libraries（类型0）是.so文件名格式外，其他类型都可以视为某种形式的"包名"或命名标识符。

## 精确匹配流程

### 1. 数据准备阶段

获取所有待处理的DEX包名信号和已知SDK特征（包括LibChecker规则）。

### 2. 精确前缀匹配与正则表达式匹配

对每个发现的包名，检查是否匹配已知SDK特征。匹配过程遵循以下优先级和规则：

1. 优先尝试前缀匹配（非正则表达式规则）
2. 如果前缀匹配失败，再尝试正则表达式匹配
3. 记录所有匹配的特征信息

例如，假设有以下SDK特征：
- `com.alipay.sdk` (前缀匹配规则)
- `^com\.alipay\..+$` (正则表达式规则)
- `^com\.example\.[a-zA-Z0-9]+$` (正则表达式规则)

以及以下发现的包名信号：
- `com.alipay.sdk.util`
- `com.alipay.other.component`
- `com.example.feature1`
- `com.example.feature2`
- `com.unknown.sdk.subfeature`

匹配过程：
1. `com.alipay.sdk.util` 
   - 前缀匹配：匹配到 `com.alipay.sdk` → 标记为支付宝SDK（前缀匹配）
   - 正则表达式匹配：也匹配到 `^com\.alipay\..+$`，但以前缀匹配为准

2. `com.alipay.other.component`
   - 前缀匹配：不匹配任何前缀规则
   - 正则表达式匹配：匹配到 `^com\.alipay\..+$` → 标记为支付宝相关（正则表达式匹配）

3. `com.example.feature1`
   - 前缀匹配：不匹配任何前缀规则
   - 正则表达式匹配：匹配到 `^com\.example\.[a-zA-Z0-9]+$` → 标记为示例SDK（正则表达式匹配）

4. `com.example.feature2`
   - 前缀匹配：不匹配任何前缀规则
   - 正则表达式匹配：匹配到 `^com\.example\.[a-zA-Z0-9]+$` → 标记为示例SDK（正则表达式匹配）

5. `com.unknown.sdk.subfeature`
   - 前缀匹配：不匹配任何前缀规则
   - 正则表达式匹配：不匹配任何正则表达式规则
   - 结果：加入未匹配列表

### 3. 下一级信号过滤

过滤掉已匹配SDK前缀的下级信号，但对正则表达式匹配的信号采用不同的策略：

1. 对于前缀匹配成功的信号，过滤掉所有以该前缀开头的更长包名
2. 对于正则表达式匹配的信号，不进行下级过滤，因为正则表达式的匹配逻辑已经定义了精确的匹配规则

继续上面的例子：

已匹配的前缀：[`com.alipay.sdk`]
需要过滤的下级信号：
- 如果存在 `com.alipay.sdk.util.helper`，应该被过滤掉
- 如果存在 `com.alipay.sdk.network.api`，应该被过滤掉

正则表达式匹配的信号不需要过滤其下级，因为：
- 正则表达式 `^com\.alipay\..+$` 可能特意设计为匹配特定层级
- 正则表达式 `^com\.example\.[a-zA-Z0-9]+$` 明确只匹配两级包名，不会匹配三级或更深层级

### 正则表达式匹配的特殊情况处理

考虑复杂的正则表达式匹配情况：

假设有以下特征和信号：
- 正则表达式特征：`^com\.complex\..+$`
- 信号：
  1. `com.complex.feature`
  2. `com.complex.feature.subfeature`
  3. `com.complex.other`

在这种情况下：
- 所有三个信号都会被正则表达式匹配
- 不进行下级过滤，因为正则表达式本身就可能设计为匹配多层次结构
- 在统计潜在特征时，这些信号将按正常层级分析处理

## 潜在新SDK特征发现

对未匹配且未被过滤的信号进行层级统计分析。

### 示例场景

假设有以下未匹配包名：
```
com.unknown.sdk.feature1
com.unknown.sdk.feature2
com.unknown.sdk.feature3
com.another.new.featureA
com.another.new.featureB
com.another.new.featureC
com.single.package.standalone
com.complex.matched.signal
com.complex.matched.other
```

其中最后两个信号虽然被正则表达式匹配，但不在前缀匹配的过滤范围内。

### 1. 层级分解

对每个包名生成所有可能的父级前缀：

```
com.unknown.sdk.feature1 → [com, com.unknown, com.unknown.sdk, com.unknown.sdk.feature1]
com.unknown.sdk.feature2 → [com, com.unknown, com.unknown.sdk, com.unknown.sdk.feature2]
com.unknown.sdk.feature3 → [com, com.unknown, com.unknown.sdk, com.unknown.sdk.feature3]
com.another.new.featureA → [com, com.another, com.another.new, com.another.new.featureA]
com.another.new.featureB → [com, com.another, com.another.new, com.another.new.featureB]
com.another.new.featureC → [com, com.another, com.another.new, com.another.new.featureC]
com.single.package.standalone → [com, com.single, com.single.package, com.single.package.standalone]
com.complex.matched.signal → [com, com.complex, com.complex.matched, com.complex.matched.signal]
com.complex.matched.other → [com, com.complex, com.complex.matched, com.complex.matched.other]
```

### 2. 层级频率统计

统计每个层级的出现次数：

| 层级 | 出现次数 |
|------|---------|
| com | 9 |
| com.unknown | 3 |
| com.unknown.sdk | 3 |
| com.another | 3 |
| com.another.new | 3 |
| com.single | 1 |
| com.single.package | 1 |
| com.complex | 2 |
| com.complex.matched | 2 |
| ... | ... |

### 3. 候选特征提取

提取出现3次或以上的层级作为候选特征：

候选特征列表：
- com (出现9次)
- com.unknown (出现3次)
- com.unknown.sdk (出现3次)
- com.another (出现3次)
- com.another.new (出现3次)

### 4. 父级特征合并

统计每个父级下的直接子级数量：

| 父级 | 直接子级数量 | 子级列表 |
|------|------------|---------|
| com.unknown | 1 | [sdk] |
| com.unknown.sdk | 3 | [feature1, feature2, feature3] |
| com.another | 1 | [new] |
| com.another.new | 3 | [featureA, featureB, featureC] |
| com.single | 1 | [package] |
| com.complex | 1 | [matched] |

根据规则（同一父级下有3个或以上子级），提取满足条件的父级作为潜在SDK特征：

潜在SDK特征：
- `com.unknown.sdk` (3个子级: feature1, feature2, feature3)
- `com.another.new` (3个子级: featureA, featureB, featureC)

## 其他信号类型的处理

除了DEX包名(type=5)之外，还有其他8种信号类型也可以通过类似的方式进行处理：

### Native Libraries (类型0)
- 匹配.so文件名前缀
- 示例：
  - 特征：`libtensorflow_`
  - 信号：`libtensorflow_inference.so` → 匹配成功，标记为TensorFlow库

### 组件类型信号 (类型1-4)
- Service (类型1)
- Activity (类型2)
- Receiver (类型3)
- Provider (类型4)
- 匹配完整类名前缀
- 示例：
  - 特征：`com.google.firebase.components`
  - 信号：`com.google.firebase.components.ComponentDiscoveryService` → 匹配成功，标记为Firebase组件

### 其他类型信号 (类型6-9)
- Static Libraries (类型6)
- Permissions (类型7)
- Metadata (类型8)
- Intent Actions (类型9)

## 完整处理流程图及示例

```
graph TD
    A[获取信号数据<br/>示例:<br/>com.alipay.sdk.util<br/>com.example.feature1] --> B[获取特征库<br/>示例:<br/>com.alipay.sdk<br/>^com\.example\.[a-zA-Z0-9]+$]
    B --> C[按类型分组处理<br/>示例:<br/>将DEX包名、Service等<br/>分别归类处理]
    C --> D[DEX包名处理]
    C --> E[组件类型处理<br/>示例:<br/>处理Service/Activity等组件]
    C --> F[其他类型处理<br/>示例:<br/>处理Native库、权限等]
    D --> G[精确前缀匹配<br/>示例:<br/>com.alipay.sdk.util 匹配<br/>com.alipay.sdk 前缀]
    E --> G
    F --> G
    G --> H{匹配成功?<br/>示例:<br/>com.alipay.sdk.util → 是<br/>com.unknown.feature → 否}
    H -->|是| I[记录匹配结果<br/>区分前缀/正则匹配<br/>示例:<br/>标记为已知SDK]
    H -->|否| J[加入未匹配列表<br/>示例:<br/>com.unknown.feature 加入列表]
    I --> K[收集匹配前缀<br/>示例:<br/>记录 com.alipay.sdk]
    K --> L[过滤下级信号<br/>正则匹配不过滤<br/>示例:<br/>过滤 com.alipay.sdk.*]
    L --> M[层级统计分析<br/>示例:<br/>统计 com.unknown.* 出现次数]
    M --> N[提取候选特征<br/>示例:<br/>提取出现≥3次的层级]
    N --> O[合并父级特征<br/>示例:<br/>com.unknown.sdk 有3个子级]
    O --> P[输出潜在SDK特征<br/>示例:<br/>com.unknown.sdk 为潜在特征]
```

### 详细步骤示例说明

#### 步骤A: 获取信号数据
从[class_app_discovered_packages](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_discovered_packages.py#L0-L0)表中获取所有待处理的信号数据，例如：
- `com.alipay.sdk.util` (DEX包名)
- `com.alipay.other.component` (DEX包名)
- `com.example.feature1` (DEX包名)
- `com.google.firebase.components.ComponentDiscoveryService` (Service组件)
- `libtensorflow_inference.so` (Native库)

#### 步骤B: 获取特征库
从[class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)表中获取所有特征，例如：
- `com.alipay.sdk` (前缀匹配规则)
- `^com\.alipay\..+$` (正则表达式规则)
- `com.google.firebase.components` (Service组件前缀)
- `libtensorflow_` (Native库前缀)

#### 步骤C: 按类型分组处理
将信号按类型分组：
- DEX包名类型：`com.alipay.sdk.util`, `com.alipay.other.component`, `com.example.feature1`
- Service组件类型：`com.google.firebase.components.ComponentDiscoveryService`
- Native库类型：`libtensorflow_inference.so`

#### 步骤D-F: 类型处理
分别处理各类型信号，以DEX包名为例：
- 对`com.alipay.sdk.util`进行匹配检查
- 对`com.alipay.other.component`进行匹配检查
- 对`com.example.feature1`进行匹配检查

#### 步骤G: 精确前缀匹配
检查信号是否匹配特征库中的前缀规则：
- `com.alipay.sdk.util`匹配`com.alipay.sdk`前缀 → 匹配成功
- `com.alipay.other.component`不匹配任何前缀 → 继续检查正则表达式

#### 步骤H: 匹配判断
判断信号是否匹配任何特征：
- `com.alipay.sdk.util` → 匹配成功
- `com.example.feature1` → 需要进一步检查正则表达式

#### 步骤I: 记录匹配结果
记录匹配成功的信号及其匹配的特征：
- `com.alipay.sdk.util` → 匹配`com.alipay.sdk`(前缀匹配)
- `com.example.feature1` → 匹配`^com\.example\.[a-zA-Z0-9]+$`(正则表达式匹配)

#### 步骤J: 未匹配列表
将未匹配的信号加入未匹配列表：
- 例如：`com.unknown.feature`未匹配任何特征

#### 步骤K: 收集匹配前缀
收集所有前缀匹配成功的特征前缀：
- 收集到：[`com.alipay.sdk`, `com.google.firebase.components`]

#### 步骤L: 过滤下级信号
过滤掉匹配前缀的下级信号，但保留正则表达式匹配的信号：
- 过滤以`com.alipay.sdk`开头的信号，如`com.alipay.sdk.network.api`
- 不过滤正则表达式匹配的信号，如`com.alipay.other.component`

#### 步骤M: 层级统计分析
对未匹配信号进行层级分解和统计：
- `com.unknown.sdk.feature1` → [com, com.unknown, com.unknown.sdk, com.unknown.sdk.feature1]
- 统计各层级出现次数

#### 步骤N: 提取候选特征
提取出现3次或以上的层级作为候选特征：
- `com.unknown`(出现3次)
- `com.unknown.sdk`(出现3次)
- `com.another.new`(出现3次)

#### 步骤O: 合并父级特征
统计每个父级下的直接子级数量，合并满足条件的特征：
- `com.unknown.sdk`有3个直接子级 → 满足条件
- `com.another.new`有3个直接子级 → 满足条件

#### 步骤P: 输出潜在SDK特征
输出最终的潜在SDK特征：
- `com.unknown.sdk`(3个子级)
- `com.another.new`(3个子级)

## 实施细节说明

### 包名格式处理
- 确保所有包名使用点号(.)分隔格式
- 处理从DEX文件中提取的原始数据时，将斜杠(/)替换为点号(.) 

### 匹配算法优化
- 使用集合(set)存储已匹配前缀，提高查找效率
- 对于大型数据集，可考虑建立前缀索引
- 对正则表达式规则进行预编译以提高性能
- 在匹配过程中，优先处理前缀匹配，再处理正则表达式匹配

### 正则表达式处理细节
- 对正则表达式进行预编译，避免重复编译
- 处理正则表达式错误，对于无效的正则表达式应跳过并记录
- 区分前缀匹配和正则表达式匹配的结果，分别记录

### 统计阈值设定
- 当前阈值设定为3次，可根据实际数据分布调整
- 阈值过低会增加误报，过高会遗漏潜在特征

### 结果存储说明
匹配结果将存储在[class_app_version_sdks](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_version_sdks.py#L0-L0)表中：
1. 已识别的SDK：
   - 设置`sdk_knowledge_base_id`为匹配的特征ID
   - 设置`match_type`为匹配类型（prefix或regex）
   - 设置`is_potential`为false
   - `sdk_package_prefix`字段存储来自[class_sdk_knowledge_base](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_sdk_knowledge_base.py#L0-L0)的package_prefix值
2. 潜在的新SDK特征：
   - `sdk_knowledge_base_id`设置为空
   - `match_type`设置为potential
   - `is_potential`设置为true
   - `sdk_package_prefix`字段存储识别出的潜在SDK包名前缀
   - `signal_count`记录该潜在SDK下的信号数量
   - `child_packages`记录发现的子包列表

### 结果输出格式
匹配结果包含：
1. 已识别的SDK信号列表
2. 每个信号匹配的SDK信息及匹配类型（前缀/正则表达式）
3. 潜在的新SDK特征列表
4. 每个潜在特征的子包数量和示例

## 总结

该方案通过精确的字符串前缀匹配和正则表达式匹配，结合层级统计分析，能够有效识别已知SDK并发现潜在的新SDK特征。对于正则表达式匹配的特殊处理确保了复杂匹配规则的正确应用，无需依赖机器学习模型，完全基于数据本身的特征进行分析。流程清晰、可操作性强，便于实际部署和维护。

同时，方案支持处理来自LibChecker的多种类型信号，不仅限于DEX包名，还包括各种Android组件和库类型，提供了完整的特征匹配和新特征发现能力。处理结果存储在[class_app_version_sdks](file:///home/<USER>/dev/tenyy-dind/tenyy/src/models/class_app_version_sdks.py#L0-L0)表中，区分已知SDK和潜在SDK，便于后续分析和处理。
