from tenyy.src.apkinfo_analysis.apk_analysis_flow.sdk_match_flow import SDKMatcher
from sqlalchemy import text

matcher = SDKMatcher()

with matcher.engine.connect() as conn:
    print('=== Primary Key Constraints ===')
    try:
        # 使用information_schema检查主键约束
        result = conn.execute(text("""
            SELECT kcu.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
            WHERE tc.constraint_type = 'PRIMARY KEY' 
            AND tc.table_name = 'class_app_version_sdks'
            ORDER BY kcu.ordinal_position
        """))
        rows = result.fetchall()
        print(f'Found {len(rows)} primary key columns:')
        for i, row in enumerate(rows):
            print(f'  {i+1}. {row[0]}')
    except Exception as e:
        print(f'Error fetching primary key constraints: {e}')
    
    print('\n=== Unique Constraints ===')
    try:
        # 使用information_schema检查唯一约束
        result = conn.execute(text("""
            SELECT tc.constraint_name, kcu.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
            WHERE tc.constraint_type = 'UNIQUE' 
            AND tc.table_name = 'class_app_version_sdks'
            ORDER BY tc.constraint_name, kcu.ordinal_position
        """))
        rows = result.fetchall()
        print(f'Found {len(rows)} unique constraint column entries:')
        current_constraint = None
        columns = []
        for row in rows:
            constraint_name, column_name = row
            if constraint_name != current_constraint:
                if current_constraint is not None:
                    print(f'  {current_constraint}: {", ".join(columns)}')
                current_constraint = constraint_name
                columns = [column_name]
            else:
                columns.append(column_name)
        if current_constraint is not None:
            print(f'  {current_constraint}: {", ".join(columns)}')
    except Exception as e:
        print(f'Error fetching unique constraints: {e}')
    
    print('\n=== Indexes ===')
    try:
        # 检查索引信息
        result = conn.execute(text("""
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'class_app_version_sdks'
        """))
        rows = result.fetchall()
        print(f'Found {len(rows)} indexes:')
        for i, row in enumerate(rows):
            print(f'  {i+1}. {row[0]}: {row[1]}')
    except Exception as e:
        print(f'Error fetching indexes: {e}')